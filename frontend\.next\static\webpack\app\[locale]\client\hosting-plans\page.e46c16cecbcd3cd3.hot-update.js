"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/hosting-plans/page",{

/***/ "(app-pages-browser)/./src/app/components/ReinstallModal.jsx":
/*!***********************************************!*\
  !*** ./src/app/components/ReinstallModal.jsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _services_vpsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _services_customImageService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/customImageService */ \"(app-pages-browser)/./src/app/services/customImageService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ReinstallModal = (param)=>{\n    let { isOpen, onClose, server, onReinstallSuccess } = param;\n    var _customImages_find, _customImages_find1, _customImages_find2, _availableApplications_find, _availableApplications_find1, _availableApplications_find2;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        imageId: \"\",\n        password: \"\",\n        userData: \"\",\n        sshKeys: [],\n        installationType: \"standard\",\n        advancedImageType: \"standard\",\n        selectedApplication: \"\",\n        adminPassword: \"\",\n        enableRootUser: false,\n        publicSshKey: \"\",\n        cloudInitTemplate: \"\",\n        customScript: \"\",\n        // Custom Image fields\n        customImageUrl: \"\",\n        customImageName: \"\",\n        customImageOsType: \"Linux\",\n        customImageVersion: \"\",\n        customImageDescription: \"\"\n    });\n    const [availableImages, setAvailableImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableApplications, setAvailableApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customImages, setCustomImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingImages, setLoadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingApplications, setLoadingApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAdminPassword, setShowAdminPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingPassword, setIsGeneratingPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCustomImageModal, setShowCustomImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            fetchAvailableImages();\n            fetchAvailableApplications();\n            fetchCustomImages();\n            setErrors({});\n            setShowPassword(false);\n        }\n    }, [\n        isOpen\n    ]);\n    // Fonction pour générer un mot de passe sécurisé (alphanumeric seulement)\n    const generateSecurePassword = ()=>{\n        setIsGeneratingPassword(true);\n        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n        const numbers = \"0123456789\";\n        // Pas de caractères spéciaux pour éviter les problèmes SSH\n        const allChars = lowercase + uppercase + numbers;\n        let password = \"\";\n        // Assurer au moins un caractère de chaque type\n        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n        password += numbers[Math.floor(Math.random() * numbers.length)];\n        // Compléter avec des caractères aléatoires (12 caractères total)\n        for(let i = 3; i < 12; i++){\n            password += allChars[Math.floor(Math.random() * allChars.length)];\n        }\n        // Mélanger le mot de passe\n        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n        setTimeout(()=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    password\n                }));\n            setIsGeneratingPassword(false);\n            setShowPassword(true);\n        }, 500);\n    };\n    // Fonction pour générer une clé SSH publique d'exemple (simulation)\n    const generateExampleSSHKey = ()=>{\n        const keyTypes = [\n            \"ssh-rsa\",\n            \"ssh-ed25519\"\n        ];\n        const keyType = keyTypes[Math.floor(Math.random() * keyTypes.length)];\n        const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n        const username = \"user\";\n        const hostname = \"localhost\";\n        if (keyType === \"ssh-ed25519\") {\n            return \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI\".concat(randomString, \" \").concat(username, \"@\").concat(hostname);\n        } else {\n            return \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ\".concat(randomString, \" \").concat(username, \"@\").concat(hostname);\n        }\n    };\n    // Fonction pour évaluer la force du mot de passe (alphanumeric)\n    const getPasswordStrength = (password)=>{\n        if (!password) return {\n            percentage: 0,\n            label: \"\",\n            color: \"bg-gray-300\"\n        };\n        let score = 0;\n        // Longueur (plus important pour les mots de passe alphanumériques)\n        if (password.length >= 8) score += 2;\n        if (password.length >= 10) score += 1;\n        if (password.length >= 12) score += 1;\n        // Complexité (alphanumeric seulement)\n        if (/[a-z]/.test(password)) score += 1;\n        if (/[A-Z]/.test(password)) score += 1;\n        if (/[0-9]/.test(password)) score += 1;\n        // Évaluation finale adaptée pour alphanumeric\n        if (score <= 3) {\n            return {\n                percentage: 25,\n                label: \"Faible\",\n                color: \"bg-red-500\"\n            };\n        } else if (score <= 5) {\n            return {\n                percentage: 60,\n                label: \"Bon\",\n                color: \"bg-yellow-500\"\n            };\n        } else if (score <= 6) {\n            return {\n                percentage: 85,\n                label: \"Fort\",\n                color: \"bg-blue-500\"\n            };\n        } else {\n            return {\n                percentage: 100,\n                label: \"Excellent\",\n                color: \"bg-green-500\"\n            };\n        }\n    };\n    // Fonction pour récupérer les images disponibles\n    const fetchAvailableImages = async ()=>{\n        try {\n            setLoadingImages(true);\n            console.log(\"\\uD83D\\uDDBC️ Fetching available images...\");\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getAvailableImages();\n            if (response.data.success && response.data.data) {\n                const allImages = response.data.data;\n                console.log(\"✅ Retrieved \".concat(allImages.length, \" images:\"), allImages);\n                // Filter out Windows images that are incompatible with Linux VPS\n                const compatibleImages = allImages.filter((img)=>{\n                    const name = img.name.toLowerCase();\n                    const osType = (img.osType || \"\").toLowerCase();\n                    // Exclude Windows images\n                    if (osType === \"windows\" || name.includes(\"windows\")) {\n                        console.log(\"\\uD83D\\uDEAB Filtering out Windows image: \".concat(img.name));\n                        return false;\n                    }\n                    // Include Linux images\n                    return true;\n                });\n                console.log(\"✅ Compatible images after filtering: \".concat(compatibleImages.length));\n                setAvailableImages(compatibleImages);\n                // Sélectionner la première image compatible par défaut\n                if (compatibleImages.length > 0) {\n                    setFormData((prev)=>({\n                            ...prev,\n                            imageId: compatibleImages[0].imageId || compatibleImages[0].id || \"\"\n                        }));\n                }\n            } else {\n                console.error(\"❌ Failed to fetch images:\", response.data.message);\n                // Fallback vers des images par défaut en cas d'erreur\n                const fallbackImages = [\n                    {\n                        imageId: \"afecbb85-e2fc-46f0-9684-b46b1faf00bb\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        osType: \"Linux\",\n                        description: \"Ubuntu 22.04 LTS Jammy Jellyfish\"\n                    }\n                ];\n                setAvailableImages(fallbackImages);\n                setFormData((prev)=>({\n                        ...prev,\n                        imageId: fallbackImages[0].imageId\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching images:\", error);\n            // Fallback vers des images par défaut en cas d'erreur\n            const fallbackImages = [\n                {\n                    imageId: \"afecbb85-e2fc-46f0-9684-b46b1faf00bb\",\n                    name: \"Ubuntu 22.04 LTS\",\n                    osType: \"Linux\",\n                    description: \"Ubuntu 22.04 LTS Jammy Jellyfish\"\n                }\n            ];\n            setAvailableImages(fallbackImages);\n            setFormData((prev)=>({\n                    ...prev,\n                    imageId: fallbackImages[0].imageId\n                }));\n        } finally{\n            setLoadingImages(false);\n        }\n    };\n    // Fonction pour récupérer les applications disponibles\n    const fetchAvailableApplications = async ()=>{\n        try {\n            setLoadingApplications(true);\n            console.log(\"\\uD83D\\uDCE6 Fetching available applications...\");\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getAvailableApplications();\n            if (response.data.success && response.data.data) {\n                const applications = response.data.data;\n                console.log(\"✅ Retrieved \".concat(applications.length, \" applications:\"), applications);\n                setAvailableApplications(applications);\n            } else {\n                console.error(\"❌ Failed to fetch applications:\", response.data.message);\n                setAvailableApplications([]);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching applications:\", error);\n            setAvailableApplications([]);\n        } finally{\n            setLoadingApplications(false);\n        }\n    };\n    // Fonction pour récupérer les custom images\n    const fetchCustomImages = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDDBC️ Fetching custom images...\");\n            console.log(\"\\uD83D\\uDD0D customImageService:\", _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\n            const response = await _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomImages();\n            console.log(\"\\uD83D\\uDD0D Custom images response:\", response);\n            if (response.success) {\n                console.log(\"✅ Retrieved \".concat(response.data.length, \" custom images\"));\n                setCustomImages(response.data);\n            } else {\n                console.error(\"❌ Failed to fetch custom images:\", response.message);\n                setCustomImages([]);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching custom images:\", error);\n            console.error(\"❌ Error details:\", error.message, error.stack);\n            setCustomImages([]);\n        }\n    };\n    // Fonction de validation\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation de l'image\n        if (!formData.imageId && formData.installationType === \"standard\") {\n            newErrors.imageId = \"Veuillez s\\xe9lectionner un syst\\xe8me d'exploitation\";\n        }\n        // Validation selon le type d'installation\n        if (formData.installationType === \"standard\") {\n            // Installation standard : mot de passe requis\n            if (!formData.password) {\n                newErrors.password = \"Le mot de passe est requis\";\n            } else if (formData.password.length < 8) {\n                newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n            }\n        } else {\n            // Installation avancée : mot de passe OU clé SSH requis\n            if (!formData.adminPassword && !formData.publicSshKey) {\n                newErrors.access = \"Password or public SSH-Keys must be set in order to access the VPS.\";\n            }\n            if (formData.adminPassword && formData.adminPassword.length < 8) {\n                newErrors.adminPassword = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n            }\n            // Validation Custom Image\n            if (formData.advancedImageType === \"custom\" && !formData.customImageUrl) {\n                newErrors.customImage = \"Custom image is required\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Fonction de soumission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            console.log(\"\\uD83D\\uDD04 Starting VPS reinstallation...\");\n            // Préparer les données de réinstallation\n            const reinstallData = {\n                imageId: formData.imageId\n            };\n            // Données selon le type d'installation\n            if (formData.installationType === \"standard\") {\n                reinstallData.password = formData.password;\n                reinstallData.enableRootUser = true; // Force enable root user for password authentication\n                // Add selected application if any\n                if (formData.selectedApplication) {\n                    reinstallData.selectedApplication = formData.selectedApplication;\n                }\n                // Add Cloud-Init configuration to enable SSH password authentication\n                reinstallData.userData = \"#cloud-config\\n# Enable password authentication for SSH\\nssh_pwauth: true\\npassword: \".concat(formData.password, \"\\nchpasswd:\\n  expire: false\\n\\n# Configure SSH to allow password authentication\\nwrite_files:\\n  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf\\n    content: |\\n      PasswordAuthentication yes\\n      PermitRootLogin yes\\n      PubkeyAuthentication yes\\n    permissions: '0644'\\n\\nruncmd:\\n  - systemctl restart sshd || service ssh restart\\n  - echo \\\"Password authentication enabled for standard installation\\\" >> /root/install.log\");\n            } else {\n                // Installation avancée\n                if (formData.adminPassword) {\n                    reinstallData.password = formData.adminPassword;\n                }\n                if (formData.publicSshKey) {\n                    reinstallData.sshKeys = [\n                        formData.publicSshKey\n                    ];\n                }\n                if (formData.enableRootUser) {\n                    reinstallData.enableRootUser = true;\n                }\n                if (formData.userData) {\n                    reinstallData.userData = formData.userData;\n                }\n                if (formData.customScript) {\n                    reinstallData.customScript = formData.customScript;\n                }\n                if (formData.cloudInitTemplate) {\n                    reinstallData.cloudInitTemplate = formData.cloudInitTemplate;\n                }\n                // Custom Image handling\n                if (formData.advancedImageType === \"custom\") {\n                    reinstallData.customImageUrl = formData.customImageUrl;\n                }\n            }\n            console.log(\"\\uD83D\\uDCE4 Reinstall data:\", reinstallData);\n            // Appeler l'API de réinstallation\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].reinstallVPS(server.id, reinstallData);\n            if (response.data.success) {\n                console.log(\"✅ VPS reinstallation started successfully\");\n                // Appeler le callback de succès si fourni\n                if (onReinstallSuccess) {\n                    onReinstallSuccess(response.data);\n                }\n                // Fermer le modal\n                onClose();\n            } else {\n                throw new Error(response.data.message || \"Failed to start VPS reinstallation\");\n            }\n        } catch (error) {\n            console.error(\"❌ VPS reinstallation failed:\", error);\n            setErrors({\n                submit: error.message || \"Une erreur est survenue lors de la r\\xe9installation\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-500 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: [\n                                                \"R\\xe9installer le VPS \",\n                                                (server === null || server === void 0 ? void 0 : server.name) || (server === null || server === void 0 ? void 0 : server.id)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 border border-orange-200 rounded-md p-4 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-400 mt-0.5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-orange-800\",\n                                                        children: \"Attention : R\\xe9installation du VPS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-orange-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Cette action va \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"effacer compl\\xe8tement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 39\n                                                                    }, undefined),\n                                                                    \" toutes les donn\\xe9es pr\\xe9sentes sur le VPS et installer un nouveau syst\\xe8me d'exploitation.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"list-disc list-inside mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Tous les fichiers et configurations seront perdus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Le VPS sera temporairement indisponible pendant l'installation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Cette action est \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"irr\\xe9versible\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 436,\n                                                                                columnNumber: 44\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Type d'installation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.installationType === \"standard\" ? \"border-blue-500 bg-blue-50\" : \"border-gray-200\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"installationType\",\n                                                                        value: \"standard\",\n                                                                        checked: formData.installationType === \"standard\",\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    installationType: e.target.value\n                                                                                })),\n                                                                        className: \"mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-blue-600 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Installation standard\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.installationType === \"advanced\" ? \"border-purple-500 bg-purple-50\" : \"border-gray-200\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"installationType\",\n                                                                        value: \"advanced\",\n                                                                        checked: formData.installationType === \"advanced\",\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    installationType: e.target.value\n                                                                                })),\n                                                                        className: \"mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-purple-600 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Installation avanc\\xe9e/Image personnalis\\xe9e\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        formData.installationType === \"advanced\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                            children: \"Type d'image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.advancedImageType === \"standard\" ? \"border-blue-500 bg-blue-50\" : \"border-gray-200\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"radio\",\n                                                                                name: \"advancedImageType\",\n                                                                                value: \"standard\",\n                                                                                checked: formData.advancedImageType === \"standard\",\n                                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            advancedImageType: e.target.value\n                                                                                        })),\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-blue-600 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 528,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Standard Image\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 529,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.advancedImageType === \"custom\" ? \"border-purple-500 bg-purple-50\" : \"border-gray-200\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"radio\",\n                                                                                name: \"advancedImageType\",\n                                                                                value: \"custom\",\n                                                                                checked: formData.advancedImageType === \"custom\",\n                                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            advancedImageType: e.target.value\n                                                                                        })),\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-purple-600 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 553,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Custom Image\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 554,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 552,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                formData.advancedImageType === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Standard Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                loadingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Chargement des images...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.imageId,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                imageId: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                    disabled: isLoading,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"S\\xe9lectionner un syst\\xe8me d'exploitation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        availableImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: image.imageId || image.id,\n                                                                                children: [\n                                                                                    image.name,\n                                                                                    \" \",\n                                                                                    image.version ? \"(\".concat(image.version, \")\") : \"\"\n                                                                                ]\n                                                                            }, image.imageId || image.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 583,\n                                                                                columnNumber: 31\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Root Password\" : \"Admin Password\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: formData.enableRootUser ? \"Mot de passe pour l'utilisateur root\" : \"Mot de passe pour l'utilisateur admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 595,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            value: formData.adminPassword,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        adminPassword: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24\",\n                                                                            placeholder: \"Select or create new password\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-y-0 right-0 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        setIsGeneratingPassword(true);\n                                                                                        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n                                                                                        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n                                                                                        const numbers = \"0123456789\";\n                                                                                        const allChars = lowercase + uppercase + numbers;\n                                                                                        let password = \"\";\n                                                                                        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n                                                                                        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n                                                                                        password += numbers[Math.floor(Math.random() * numbers.length)];\n                                                                                        for(let i = 3; i < 12; i++){\n                                                                                            password += allChars[Math.floor(Math.random() * allChars.length)];\n                                                                                        }\n                                                                                        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n                                                                                        setTimeout(()=>{\n                                                                                            setFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    adminPassword: password\n                                                                                                }));\n                                                                                            setIsGeneratingPassword(false);\n                                                                                            setShowPassword(true);\n                                                                                        }, 500);\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                                    disabled: isLoading || isGeneratingPassword,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 637,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 607,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                                    className: \"px-2 py-1 text-gray-500 hover:text-gray-700\",\n                                                                                    title: showPassword ? \"Masquer le mot de passe\" : \"Afficher le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 646,\n                                                                                        columnNumber: 47\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 646,\n                                                                                        columnNumber: 80\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 639,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.adminPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                adminPassword: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-gray-400 hover:text-red-500 rounded-r\",\n                                                                                    title: \"Supprimer le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 656,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 649,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.enableRootUser,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    enableRootUser: e.target.checked\n                                                                                })),\n                                                                        className: \"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            \"Enable Root User\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                                title: \"Activer l'utilisateur root\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 675,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Public SSH-Key for User Root\" : \"Public SSH-Key for User Admin\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Cl\\xe9 SSH publique pour l'acc\\xe8s\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.publicSshKey,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        publicSshKey: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20\",\n                                                                            rows: 3,\n                                                                            placeholder: \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-2 right-2 flex flex-col space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        const key = generateExampleSSHKey();\n                                                                                        setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: key\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer une cl\\xe9 SSH d'exemple\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 706,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.publicSshKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                                                    title: \"Supprimer la cl\\xe9 SSH\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 716,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Cloud-Init\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Template Cloud-Init pour la configuration automatique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.cloudInitTemplate,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select cloud init template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 736,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: app.applicationId || app.id,\n                                                                                        children: [\n                                                                                            app.name,\n                                                                                            \" \",\n                                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                                        ]\n                                                                                    }, app.applicationId || app.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.cloudInitTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer la s\\xe9lection\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    value: formData.userData,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                userData: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2\",\n                                                                    rows: 8,\n                                                                    placeholder: \"#cloud-config # Configuration Cloud-Init personnalis\\xe9e packages: - nginx - docker.io\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 754,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Custom Script\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Script personnalis\\xe9 \\xe0 ex\\xe9cuter lors de l'installation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.customScript,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customScript: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            rows: 6,\n                                                                            placeholder: \"#!/bin/bash # Votre script personnalis\\xe9 ici echo 'Installation personnalis\\xe9e en cours...'\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.customScript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customScript: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-3 top-3 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer le script\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: \"Script bash qui sera ex\\xe9cut\\xe9 apr\\xe8s l'installation du syst\\xe8me d'exploitation.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                formData.advancedImageType === \"custom\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Custom Image\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-purple-500\",\n                                                                            title: \"S\\xe9lectionner ou cr\\xe9er une image personnalis\\xe9e\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.customImageUrl,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customImageUrl: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select existing Custom Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                customImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: image.imageId || image.id,\n                                                                                        children: [\n                                                                                            image.name,\n                                                                                            \" \",\n                                                                                            image.version ? \"(\".concat(image.version, \")\") : \"\",\n                                                                                            \" - \",\n                                                                                            image.osType\n                                                                                        ]\n                                                                                    }, image.imageId || image.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 815,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    console.log(\"\\uD83D\\uDD04 Opening Custom Image Modal...\");\n                                                                                    setShowCustomImageModal(true);\n                                                                                },\n                                                                                className: \"inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors\",\n                                                                                disabled: isLoading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 831,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    \"Add Custom Image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 822,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                errors.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 mt-1\",\n                                                                    children: errors.customImage\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                !formData.customImageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 mt-1\",\n                                                                    children: \"Image is required.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                formData.customImageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-2 bg-purple-50 rounded-md\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-purple-800\",\n                                                                            children: [\n                                                                                \"Custom Image s\\xe9lectionn\\xe9e: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: ((_customImages_find = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find === void 0 ? void 0 : _customImages_find.name) || \"Custom Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 845,\n                                                                                    columnNumber: 58\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        ((_customImages_find1 = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find1 === void 0 ? void 0 : _customImages_find1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-purple-600 mt-1\",\n                                                                            children: (_customImages_find2 = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find2 === void 0 ? void 0 : _customImages_find2.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 850,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Root Password\" : \"Admin Password\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: formData.enableRootUser ? \"Mot de passe pour l'utilisateur root\" : \"Mot de passe pour l'utilisateur admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 862,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            value: formData.adminPassword,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        adminPassword: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24\",\n                                                                            placeholder: \"Select or create new password\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-y-0 right-0 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        setIsGeneratingPassword(true);\n                                                                                        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n                                                                                        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n                                                                                        const numbers = \"0123456789\";\n                                                                                        const allChars = lowercase + uppercase + numbers;\n                                                                                        let password = \"\";\n                                                                                        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n                                                                                        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n                                                                                        password += numbers[Math.floor(Math.random() * numbers.length)];\n                                                                                        for(let i = 3; i < 12; i++){\n                                                                                            password += allChars[Math.floor(Math.random() * allChars.length)];\n                                                                                        }\n                                                                                        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n                                                                                        setTimeout(()=>{\n                                                                                            setFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    adminPassword: password\n                                                                                                }));\n                                                                                            setIsGeneratingPassword(false);\n                                                                                            setShowPassword(true);\n                                                                                        }, 500);\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                                    disabled: isLoading || isGeneratingPassword,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 874,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                                    className: \"px-2 py-1 text-gray-500 hover:text-gray-700\",\n                                                                                    title: showPassword ? \"Masquer le mot de passe\" : \"Afficher le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 913,\n                                                                                        columnNumber: 47\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 913,\n                                                                                        columnNumber: 80\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 906,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.adminPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                adminPassword: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-gray-400 hover:text-red-500 rounded-r\",\n                                                                                    title: \"Supprimer le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 923,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 916,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 859,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.enableRootUser,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    enableRootUser: e.target.checked\n                                                                                })),\n                                                                        className: \"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            \"Enable Root User\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                                title: \"Activer l'utilisateur root\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 942,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Public SSH-Key for User Root\" : \"Public SSH-Key for User Admin\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Cl\\xe9 SSH publique pour l'acc\\xe8s\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 951,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.publicSshKey,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        publicSshKey: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20\",\n                                                                            rows: 3,\n                                                                            placeholder: \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-2 right-2 flex flex-col space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        const key = generateExampleSSHKey();\n                                                                                        setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: key\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer une cl\\xe9 SSH d'exemple\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 973,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 963,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.publicSshKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                                                    title: \"Supprimer la cl\\xe9 SSH\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 983,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 962,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Cloud-Init\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Template Cloud-Init pour la configuration automatique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 992,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.cloudInitTemplate,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select cloud init template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 1003,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: app.applicationId || app.id,\n                                                                                        children: [\n                                                                                            app.name,\n                                                                                            \" \",\n                                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                                        ]\n                                                                                    }, app.applicationId || app.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 1005,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.cloudInitTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer la s\\xe9lection\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 1017,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1011,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    value: formData.userData,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                userData: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2\",\n                                                                    rows: 8,\n                                                                    placeholder: \"#cloud-config # Configuration Cloud-Init personnalis\\xe9e packages: - nginx - docker.io\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        formData.installationType === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Syst\\xe8me d'exploitation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1040,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        loadingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Chargement des images...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.imageId,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        imageId: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(errors.imageId ? \"border-red-300\" : \"border-gray-300\"),\n                                                            disabled: isLoading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionner un syst\\xe8me d'exploitation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1057,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                availableImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: image.imageId || image.id,\n                                                                        children: [\n                                                                            image.name,\n                                                                            \" \",\n                                                                            image.version ? \"(\".concat(image.version, \")\") : \"\"\n                                                                        ]\n                                                                    }, image.imageId || image.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1049,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        errors.imageId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mt-1\",\n                                                            children: errors.imageId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Application (optionnel)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1072,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        loadingApplications ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Chargement des applications...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.selectedApplication,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        selectedApplication: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            disabled: isLoading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Aucune application (OS seulement)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1088,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: app.applicationId || app.id,\n                                                                        children: [\n                                                                            app.name,\n                                                                            \" \",\n                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                        ]\n                                                                    }, app.applicationId || app.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1090,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        formData.selectedApplication && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-2 bg-green-50 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: [\n                                                                        \"Application s\\xe9lectionn\\xe9e: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: ((_availableApplications_find = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find === void 0 ? void 0 : _availableApplications_find.name) || formData.selectedApplication\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1100,\n                                                                            columnNumber: 53\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                ((_availableApplications_find1 = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find1 === void 0 ? void 0 : _availableApplications_find1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-600 mt-1\",\n                                                                    children: (_availableApplications_find2 = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find2 === void 0 ? void 0 : _availableApplications_find2.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1071,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Mot de passe root\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                password: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(errors.password ? \"border-red-300\" : \"border-gray-300\"),\n                                                                    placeholder: \"Mot de passe pour l'acc\\xe8s root\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                    title: showPassword ? \"Cacher le mot de passe\" : \"Voir le mot de passe\",\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1136,\n                                                                        columnNumber: 41\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1136,\n                                                                        columnNumber: 74\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: generateSecurePassword,\n                                                                    disabled: isGeneratingPassword || isLoading,\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 disabled:text-gray-400\",\n                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1145,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1138,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        formData.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-2 rounded-full transition-all duration-300 \".concat(getPasswordStrength(formData.password).color),\n                                                                            style: {\n                                                                                width: \"\".concat(getPasswordStrength(formData.password).percentage, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1152,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getPasswordStrength(formData.password).color.replace(\"bg-\", \"text-\")),\n                                                                        children: getPasswordStrength(formData.password).label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1159,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 1150,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mt-1\",\n                                                            children: errors.password\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-50 border border-red-200 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600 text-sm\",\n                                                children: errors.submit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 1175,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.access && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-50 border border-red-200 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600 text-sm\",\n                                                children: errors.access\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-3 pt-6 border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: onClose,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n                                                    disabled: isLoading,\n                                                    children: \"Annuler\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    disabled: isLoading,\n                                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \"R\\xe9installation...\"\n                                                        ]\n                                                    }, void 0, true) : \"R\\xe9installer le VPS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined),\n            showCustomImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Add Custom Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCustomImageModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 1225,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1219,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Image URL\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"URL de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            value: formData.customImageUrl,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageUrl: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"https://example.com/my-custom-image.iso\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Image Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Nom de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1249,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1247,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.customImageName,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageName: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"My Custom Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1251,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"OS Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Type de syst\\xe8me d'exploitation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1264,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.customImageOsType,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageOsType: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Linux\",\n                                                    children: \"Linux\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Windows\",\n                                                    children: \"Windows\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Version\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Version du syst\\xe8me d'exploitation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1278,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.customImageVersion,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageVersion: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-red-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"22.04, 2022, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1282,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600 mt-1\",\n                                            children: \"Version is required.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1277,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Description\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Description de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1294,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.customImageDescription,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageDescription: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            rows: 3,\n                                            placeholder: \"Description de votre image personnalis\\xe9e...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1293,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1229,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 p-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowCustomImageModal(false),\n                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1309,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: async ()=>{\n                                        try {\n                                            // Validate required fields\n                                            if (!formData.customImageUrl) {\n                                                alert(\"Image URL is required\");\n                                                return;\n                                            }\n                                            if (!formData.customImageName) {\n                                                alert(\"Image Name is required\");\n                                                return;\n                                            }\n                                            if (!formData.customImageVersion) {\n                                                alert(\"Version is required\");\n                                                return;\n                                            }\n                                            console.log(\"Saving custom image:\", {\n                                                url: formData.customImageUrl,\n                                                name: formData.customImageName,\n                                                osType: formData.customImageOsType,\n                                                version: formData.customImageVersion,\n                                                description: formData.customImageDescription\n                                            });\n                                            // Save custom image via API\n                                            const result = await _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].createCustomImage({\n                                                url: formData.customImageUrl,\n                                                name: formData.customImageName,\n                                                osType: formData.customImageOsType,\n                                                version: formData.customImageVersion,\n                                                description: formData.customImageDescription\n                                            });\n                                            if (result.success) {\n                                                console.log(\"✅ Custom image created successfully:\", result.data);\n                                                // Refresh the custom images list\n                                                await fetchCustomImages();\n                                                // Update the custom image selector with the new image\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageUrl: result.data.imageId || result.data.id,\n                                                        // Reset custom image form\n                                                        customImageName: \"\",\n                                                        customImageVersion: \"\",\n                                                        customImageDescription: \"\"\n                                                    }));\n                                                setShowCustomImageModal(false);\n                                                alert(\"Custom image created successfully!\");\n                                            }\n                                        } catch (error) {\n                                            console.error(\"❌ Failed to create custom image:\", error);\n                                            alert(\"Failed to create custom image: \" + error.message);\n                                        }\n                                    },\n                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    children: \"Upload\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1316,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1308,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                    lineNumber: 1218,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                lineNumber: 1217,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ReinstallModal, \"g1v6fkF+Rx6M9OmTMnZCydQSbJg=\");\n_c = ReinstallModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ReinstallModal);\nvar _c;\n$RefreshReg$(_c, \"ReinstallModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/ReinstallModal.jsx\n"));

/***/ })

});