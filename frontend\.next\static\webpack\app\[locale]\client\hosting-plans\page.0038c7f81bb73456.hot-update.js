"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/hosting-plans/page",{

/***/ "(app-pages-browser)/./src/app/components/ReinstallModal.jsx":
/*!***********************************************!*\
  !*** ./src/app/components/ReinstallModal.jsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Eye,EyeOff,HardDrive,Info,Key,RefreshCw,Settings,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _services_vpsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _services_customImageService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/customImageService */ \"(app-pages-browser)/./src/app/services/customImageService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ReinstallModal = (param)=>{\n    let { isOpen, onClose, server, onReinstallSuccess } = param;\n    var _customImages_find, _customImages_find1, _customImages_find2, _availableApplications_find, _availableApplications_find1, _availableApplications_find2;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        imageId: \"\",\n        password: \"\",\n        userData: \"\",\n        sshKeys: [],\n        installationType: \"standard\",\n        advancedImageType: \"standard\",\n        selectedApplication: \"\",\n        adminPassword: \"\",\n        enableRootUser: false,\n        publicSshKey: \"\",\n        cloudInitTemplate: \"\",\n        customScript: \"\",\n        // Custom Image fields\n        customImageUrl: \"\",\n        customImageName: \"\",\n        customImageOsType: \"Linux\",\n        customImageVersion: \"\",\n        customImageDescription: \"\"\n    });\n    const [availableImages, setAvailableImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableApplications, setAvailableApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customImages, setCustomImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingImages, setLoadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingApplications, setLoadingApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAdminPassword, setShowAdminPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingPassword, setIsGeneratingPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCustomImageModal, setShowCustomImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            fetchAvailableImages();\n            fetchAvailableApplications();\n            fetchCustomImages();\n            setErrors({});\n            setShowPassword(false);\n            setShowCustomImageModal(false); // Reset modal state\n        }\n    }, [\n        isOpen\n    ]);\n    // Debug log for modal state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Custom Image Modal State:\", showCustomImageModal);\n    }, [\n        showCustomImageModal\n    ]);\n    // Fonction pour générer un mot de passe sécurisé (alphanumeric seulement)\n    const generateSecurePassword = ()=>{\n        setIsGeneratingPassword(true);\n        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n        const numbers = \"0123456789\";\n        // Pas de caractères spéciaux pour éviter les problèmes SSH\n        const allChars = lowercase + uppercase + numbers;\n        let password = \"\";\n        // Assurer au moins un caractère de chaque type\n        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n        password += numbers[Math.floor(Math.random() * numbers.length)];\n        // Compléter avec des caractères aléatoires (12 caractères total)\n        for(let i = 3; i < 12; i++){\n            password += allChars[Math.floor(Math.random() * allChars.length)];\n        }\n        // Mélanger le mot de passe\n        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n        setTimeout(()=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    password\n                }));\n            setIsGeneratingPassword(false);\n            setShowPassword(true);\n        }, 500);\n    };\n    // Fonction pour générer une clé SSH publique d'exemple (simulation)\n    const generateExampleSSHKey = ()=>{\n        const keyTypes = [\n            \"ssh-rsa\",\n            \"ssh-ed25519\"\n        ];\n        const keyType = keyTypes[Math.floor(Math.random() * keyTypes.length)];\n        const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n        const username = \"user\";\n        const hostname = \"localhost\";\n        if (keyType === \"ssh-ed25519\") {\n            return \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI\".concat(randomString, \" \").concat(username, \"@\").concat(hostname);\n        } else {\n            return \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ\".concat(randomString, \" \").concat(username, \"@\").concat(hostname);\n        }\n    };\n    // Fonction pour évaluer la force du mot de passe (alphanumeric)\n    const getPasswordStrength = (password)=>{\n        if (!password) return {\n            percentage: 0,\n            label: \"\",\n            color: \"bg-gray-300\"\n        };\n        let score = 0;\n        // Longueur (plus important pour les mots de passe alphanumériques)\n        if (password.length >= 8) score += 2;\n        if (password.length >= 10) score += 1;\n        if (password.length >= 12) score += 1;\n        // Complexité (alphanumeric seulement)\n        if (/[a-z]/.test(password)) score += 1;\n        if (/[A-Z]/.test(password)) score += 1;\n        if (/[0-9]/.test(password)) score += 1;\n        // Évaluation finale adaptée pour alphanumeric\n        if (score <= 3) {\n            return {\n                percentage: 25,\n                label: \"Faible\",\n                color: \"bg-red-500\"\n            };\n        } else if (score <= 5) {\n            return {\n                percentage: 60,\n                label: \"Bon\",\n                color: \"bg-yellow-500\"\n            };\n        } else if (score <= 6) {\n            return {\n                percentage: 85,\n                label: \"Fort\",\n                color: \"bg-blue-500\"\n            };\n        } else {\n            return {\n                percentage: 100,\n                label: \"Excellent\",\n                color: \"bg-green-500\"\n            };\n        }\n    };\n    // Fonction pour récupérer les images disponibles\n    const fetchAvailableImages = async ()=>{\n        try {\n            setLoadingImages(true);\n            console.log(\"\\uD83D\\uDDBC️ Fetching available images...\");\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getAvailableImages();\n            if (response.data.success && response.data.data) {\n                const allImages = response.data.data;\n                console.log(\"✅ Retrieved \".concat(allImages.length, \" images:\"), allImages);\n                // Filter out Windows images that are incompatible with Linux VPS\n                const compatibleImages = allImages.filter((img)=>{\n                    const name = img.name.toLowerCase();\n                    const osType = (img.osType || \"\").toLowerCase();\n                    // Exclude Windows images\n                    if (osType === \"windows\" || name.includes(\"windows\")) {\n                        console.log(\"\\uD83D\\uDEAB Filtering out Windows image: \".concat(img.name));\n                        return false;\n                    }\n                    // Include Linux images\n                    return true;\n                });\n                console.log(\"✅ Compatible images after filtering: \".concat(compatibleImages.length));\n                setAvailableImages(compatibleImages);\n                // Sélectionner la première image compatible par défaut\n                if (compatibleImages.length > 0) {\n                    setFormData((prev)=>({\n                            ...prev,\n                            imageId: compatibleImages[0].imageId || compatibleImages[0].id || \"\"\n                        }));\n                }\n            } else {\n                console.error(\"❌ Failed to fetch images:\", response.data.message);\n                // Fallback vers des images par défaut en cas d'erreur\n                const fallbackImages = [\n                    {\n                        imageId: \"afecbb85-e2fc-46f0-9684-b46b1faf00bb\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        osType: \"Linux\",\n                        description: \"Ubuntu 22.04 LTS Jammy Jellyfish\"\n                    }\n                ];\n                setAvailableImages(fallbackImages);\n                setFormData((prev)=>({\n                        ...prev,\n                        imageId: fallbackImages[0].imageId\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching images:\", error);\n            // Fallback vers des images par défaut en cas d'erreur\n            const fallbackImages = [\n                {\n                    imageId: \"afecbb85-e2fc-46f0-9684-b46b1faf00bb\",\n                    name: \"Ubuntu 22.04 LTS\",\n                    osType: \"Linux\",\n                    description: \"Ubuntu 22.04 LTS Jammy Jellyfish\"\n                }\n            ];\n            setAvailableImages(fallbackImages);\n            setFormData((prev)=>({\n                    ...prev,\n                    imageId: fallbackImages[0].imageId\n                }));\n        } finally{\n            setLoadingImages(false);\n        }\n    };\n    // Fonction pour récupérer les applications disponibles\n    const fetchAvailableApplications = async ()=>{\n        try {\n            setLoadingApplications(true);\n            console.log(\"\\uD83D\\uDCE6 Fetching available applications...\");\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getAvailableApplications();\n            if (response.data.success && response.data.data) {\n                const applications = response.data.data;\n                console.log(\"✅ Retrieved \".concat(applications.length, \" applications:\"), applications);\n                setAvailableApplications(applications);\n            } else {\n                console.error(\"❌ Failed to fetch applications:\", response.data.message);\n                setAvailableApplications([]);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching applications:\", error);\n            setAvailableApplications([]);\n        } finally{\n            setLoadingApplications(false);\n        }\n    };\n    // Fonction pour récupérer les custom images\n    const fetchCustomImages = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDDBC️ Fetching custom images...\");\n            console.log(\"\\uD83D\\uDD0D customImageService:\", _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\n            const response = await _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomImages();\n            console.log(\"\\uD83D\\uDD0D Custom images response:\", response);\n            if (response.success) {\n                console.log(\"✅ Retrieved \".concat(response.data.length, \" custom images\"));\n                setCustomImages(response.data);\n            } else {\n                console.error(\"❌ Failed to fetch custom images:\", response.message);\n                setCustomImages([]);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching custom images:\", error);\n            console.error(\"❌ Error details:\", error.message, error.stack);\n            setCustomImages([]);\n        }\n    };\n    // Fonction de validation\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation de l'image\n        if (!formData.imageId && formData.installationType === \"standard\") {\n            newErrors.imageId = \"Veuillez s\\xe9lectionner un syst\\xe8me d'exploitation\";\n        }\n        // Validation selon le type d'installation\n        if (formData.installationType === \"standard\") {\n            // Installation standard : mot de passe requis\n            if (!formData.password) {\n                newErrors.password = \"Le mot de passe est requis\";\n            } else if (formData.password.length < 8) {\n                newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n            }\n        } else {\n            // Installation avancée : mot de passe OU clé SSH requis\n            if (!formData.adminPassword && !formData.publicSshKey) {\n                newErrors.access = \"Password or public SSH-Keys must be set in order to access the VPS.\";\n            }\n            if (formData.adminPassword && formData.adminPassword.length < 8) {\n                newErrors.adminPassword = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n            }\n            // Validation Custom Image\n            if (formData.advancedImageType === \"custom\" && !formData.customImageUrl) {\n                newErrors.customImage = \"Custom image is required\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Fonction de soumission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            console.log(\"\\uD83D\\uDD04 Starting VPS reinstallation...\");\n            // Préparer les données de réinstallation\n            const reinstallData = {\n                imageId: formData.imageId\n            };\n            // Données selon le type d'installation\n            if (formData.installationType === \"standard\") {\n                reinstallData.password = formData.password;\n                reinstallData.enableRootUser = true; // Force enable root user for password authentication\n                // Add selected application if any\n                if (formData.selectedApplication) {\n                    reinstallData.selectedApplication = formData.selectedApplication;\n                }\n                // Add Cloud-Init configuration to enable SSH password authentication\n                reinstallData.userData = \"#cloud-config\\n# Enable password authentication for SSH\\nssh_pwauth: true\\npassword: \".concat(formData.password, \"\\nchpasswd:\\n  expire: false\\n\\n# Configure SSH to allow password authentication\\nwrite_files:\\n  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf\\n    content: |\\n      PasswordAuthentication yes\\n      PermitRootLogin yes\\n      PubkeyAuthentication yes\\n    permissions: '0644'\\n\\nruncmd:\\n  - systemctl restart sshd || service ssh restart\\n  - echo \\\"Password authentication enabled for standard installation\\\" >> /root/install.log\");\n            } else {\n                // Installation avancée\n                if (formData.adminPassword) {\n                    reinstallData.password = formData.adminPassword;\n                }\n                if (formData.publicSshKey) {\n                    reinstallData.sshKeys = [\n                        formData.publicSshKey\n                    ];\n                }\n                if (formData.enableRootUser) {\n                    reinstallData.enableRootUser = true;\n                }\n                if (formData.userData) {\n                    reinstallData.userData = formData.userData;\n                }\n                if (formData.customScript) {\n                    reinstallData.customScript = formData.customScript;\n                }\n                if (formData.cloudInitTemplate) {\n                    reinstallData.cloudInitTemplate = formData.cloudInitTemplate;\n                }\n                // Custom Image handling\n                if (formData.advancedImageType === \"custom\") {\n                    reinstallData.customImageUrl = formData.customImageUrl;\n                }\n            }\n            console.log(\"\\uD83D\\uDCE4 Reinstall data:\", reinstallData);\n            // Appeler l'API de réinstallation\n            const response = await _services_vpsService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].reinstallVPS(server.id, reinstallData);\n            if (response.data.success) {\n                console.log(\"✅ VPS reinstallation started successfully\");\n                // Appeler le callback de succès si fourni\n                if (onReinstallSuccess) {\n                    onReinstallSuccess(response.data);\n                }\n                // Fermer le modal\n                onClose();\n            } else {\n                throw new Error(response.data.message || \"Failed to start VPS reinstallation\");\n            }\n        } catch (error) {\n            console.error(\"❌ VPS reinstallation failed:\", error);\n            setErrors({\n                submit: error.message || \"Une erreur est survenue lors de la r\\xe9installation\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-500 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: [\n                                                \"R\\xe9installer le VPS \",\n                                                (server === null || server === void 0 ? void 0 : server.name) || (server === null || server === void 0 ? void 0 : server.id)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 border border-orange-200 rounded-md p-4 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-400 mt-0.5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-orange-800\",\n                                                        children: \"Attention : R\\xe9installation du VPS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-orange-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Cette action va \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"effacer compl\\xe8tement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 39\n                                                                    }, undefined),\n                                                                    \" toutes les donn\\xe9es pr\\xe9sentes sur le VPS et installer un nouveau syst\\xe8me d'exploitation.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"list-disc list-inside mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Tous les fichiers et configurations seront perdus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Le VPS sera temporairement indisponible pendant l'installation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Cette action est \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"irr\\xe9versible\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 442,\n                                                                                columnNumber: 44\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Type d'installation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.installationType === \"standard\" ? \"border-blue-500 bg-blue-50\" : \"border-gray-200\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"installationType\",\n                                                                        value: \"standard\",\n                                                                        checked: formData.installationType === \"standard\",\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    installationType: e.target.value\n                                                                                })),\n                                                                        className: \"mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-blue-600 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 473,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Installation standard\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.installationType === \"advanced\" ? \"border-purple-500 bg-purple-50\" : \"border-gray-200\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"installationType\",\n                                                                        value: \"advanced\",\n                                                                        checked: formData.installationType === \"advanced\",\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    installationType: e.target.value\n                                                                                })),\n                                                                        className: \"mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-purple-600 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Installation avanc\\xe9e/Image personnalis\\xe9e\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        formData.installationType === \"advanced\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                            children: \"Type d'image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.advancedImageType === \"standard\" ? \"border-blue-500 bg-blue-50\" : \"border-gray-200\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"radio\",\n                                                                                name: \"advancedImageType\",\n                                                                                value: \"standard\",\n                                                                                checked: formData.advancedImageType === \"standard\",\n                                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            advancedImageType: e.target.value\n                                                                                        })),\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-blue-600 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 534,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Standard Image\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 \".concat(formData.advancedImageType === \"custom\" ? \"border-purple-500 bg-purple-50\" : \"border-gray-200\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"radio\",\n                                                                                name: \"advancedImageType\",\n                                                                                value: \"custom\",\n                                                                                checked: formData.advancedImageType === \"custom\",\n                                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            advancedImageType: e.target.value\n                                                                                        })),\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-purple-600 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 559,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Custom Image\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 560,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 558,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                formData.advancedImageType === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Standard Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                loadingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Chargement des images...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: formData.imageId,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                imageId: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                    disabled: isLoading,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"S\\xe9lectionner un syst\\xe8me d'exploitation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        availableImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: image.imageId || image.id,\n                                                                                children: [\n                                                                                    image.name,\n                                                                                    \" \",\n                                                                                    image.version ? \"(\".concat(image.version, \")\") : \"\"\n                                                                                ]\n                                                                            }, image.imageId || image.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 589,\n                                                                                columnNumber: 31\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Root Password\" : \"Admin Password\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: formData.enableRootUser ? \"Mot de passe pour l'utilisateur root\" : \"Mot de passe pour l'utilisateur admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            value: formData.adminPassword,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        adminPassword: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24\",\n                                                                            placeholder: \"Select or create new password\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-y-0 right-0 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        setIsGeneratingPassword(true);\n                                                                                        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n                                                                                        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n                                                                                        const numbers = \"0123456789\";\n                                                                                        const allChars = lowercase + uppercase + numbers;\n                                                                                        let password = \"\";\n                                                                                        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n                                                                                        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n                                                                                        password += numbers[Math.floor(Math.random() * numbers.length)];\n                                                                                        for(let i = 3; i < 12; i++){\n                                                                                            password += allChars[Math.floor(Math.random() * allChars.length)];\n                                                                                        }\n                                                                                        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n                                                                                        setTimeout(()=>{\n                                                                                            setFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    adminPassword: password\n                                                                                                }));\n                                                                                            setIsGeneratingPassword(false);\n                                                                                            setShowPassword(true);\n                                                                                        }, 500);\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                                    disabled: isLoading || isGeneratingPassword,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 643,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                                    className: \"px-2 py-1 text-gray-500 hover:text-gray-700\",\n                                                                                    title: showPassword ? \"Masquer le mot de passe\" : \"Afficher le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 47\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 80\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 645,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.adminPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                adminPassword: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-gray-400 hover:text-red-500 rounded-r\",\n                                                                                    title: \"Supprimer le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 662,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 655,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.enableRootUser,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    enableRootUser: e.target.checked\n                                                                                })),\n                                                                        className: \"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            \"Enable Root User\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                                title: \"Activer l'utilisateur root\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Public SSH-Key for User Root\" : \"Public SSH-Key for User Admin\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Cl\\xe9 SSH publique pour l'acc\\xe8s\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.publicSshKey,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        publicSshKey: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20\",\n                                                                            rows: 3,\n                                                                            placeholder: \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-2 right-2 flex flex-col space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        const key = generateExampleSSHKey();\n                                                                                        setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: key\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer une cl\\xe9 SSH d'exemple\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 712,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.publicSshKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                                                    title: \"Supprimer la cl\\xe9 SSH\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 722,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Cloud-Init\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Template Cloud-Init pour la configuration automatique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.cloudInitTemplate,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select cloud init template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 742,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: app.applicationId || app.id,\n                                                                                        children: [\n                                                                                            app.name,\n                                                                                            \" \",\n                                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                                        ]\n                                                                                    }, app.applicationId || app.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 744,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.cloudInitTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer la s\\xe9lection\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 756,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    value: formData.userData,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                userData: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2\",\n                                                                    rows: 8,\n                                                                    placeholder: \"#cloud-config # Configuration Cloud-Init personnalis\\xe9e packages: - nginx - docker.io\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Custom Script\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Script personnalis\\xe9 \\xe0 ex\\xe9cuter lors de l'installation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 774,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.customScript,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customScript: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            rows: 6,\n                                                                            placeholder: \"#!/bin/bash # Votre script personnalis\\xe9 ici echo 'Installation personnalis\\xe9e en cours...'\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.customScript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customScript: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-3 top-3 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer le script\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: \"Script bash qui sera ex\\xe9cut\\xe9 apr\\xe8s l'installation du syst\\xe8me d'exploitation.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                formData.advancedImageType === \"custom\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Custom Image\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-purple-500\",\n                                                                            title: \"S\\xe9lectionner ou cr\\xe9er une image personnalis\\xe9e\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 810,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.customImageUrl,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        customImageUrl: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select existing Custom Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                customImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: image.imageId || image.id,\n                                                                                        children: [\n                                                                                            image.name,\n                                                                                            \" \",\n                                                                                            image.version ? \"(\".concat(image.version, \")\") : \"\",\n                                                                                            \" - \",\n                                                                                            image.osType\n                                                                                        ]\n                                                                                    }, image.imageId || image.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 821,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    console.log(\"\\uD83D\\uDD04 Opening Custom Image Modal...\");\n                                                                                    setShowCustomImageModal(true);\n                                                                                },\n                                                                                className: \"inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors\",\n                                                                                disabled: isLoading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 837,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    \"Add Custom Image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                errors.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 mt-1\",\n                                                                    children: errors.customImage\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                !formData.customImageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 mt-1\",\n                                                                    children: \"Image is required.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                formData.customImageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-2 bg-purple-50 rounded-md\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-purple-800\",\n                                                                            children: [\n                                                                                \"Custom Image s\\xe9lectionn\\xe9e: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: ((_customImages_find = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find === void 0 ? void 0 : _customImages_find.name) || \"Custom Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 851,\n                                                                                    columnNumber: 58\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 850,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        ((_customImages_find1 = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find1 === void 0 ? void 0 : _customImages_find1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-purple-600 mt-1\",\n                                                                            children: (_customImages_find2 = customImages.find((img)=>(img.imageId || img.id) === formData.customImageUrl)) === null || _customImages_find2 === void 0 ? void 0 : _customImages_find2.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Root Password\" : \"Admin Password\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: formData.enableRootUser ? \"Mot de passe pour l'utilisateur root\" : \"Mot de passe pour l'utilisateur admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            value: formData.adminPassword,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        adminPassword: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24\",\n                                                                            placeholder: \"Select or create new password\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-y-0 right-0 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        setIsGeneratingPassword(true);\n                                                                                        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n                                                                                        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n                                                                                        const numbers = \"0123456789\";\n                                                                                        const allChars = lowercase + uppercase + numbers;\n                                                                                        let password = \"\";\n                                                                                        password += lowercase[Math.floor(Math.random() * lowercase.length)];\n                                                                                        password += uppercase[Math.floor(Math.random() * uppercase.length)];\n                                                                                        password += numbers[Math.floor(Math.random() * numbers.length)];\n                                                                                        for(let i = 3; i < 12; i++){\n                                                                                            password += allChars[Math.floor(Math.random() * allChars.length)];\n                                                                                        }\n                                                                                        password = password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n                                                                                        setTimeout(()=>{\n                                                                                            setFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    adminPassword: password\n                                                                                                }));\n                                                                                            setIsGeneratingPassword(false);\n                                                                                            setShowPassword(true);\n                                                                                        }, 500);\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                                    disabled: isLoading || isGeneratingPassword,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 910,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 880,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                                    className: \"px-2 py-1 text-gray-500 hover:text-gray-700\",\n                                                                                    title: showPassword ? \"Masquer le mot de passe\" : \"Afficher le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 919,\n                                                                                        columnNumber: 47\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 919,\n                                                                                        columnNumber: 80\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 912,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.adminPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                adminPassword: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-gray-400 hover:text-red-500 rounded-r\",\n                                                                                    title: \"Supprimer le mot de passe\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 929,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 922,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.enableRootUser,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    enableRootUser: e.target.checked\n                                                                                })),\n                                                                        className: \"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 939,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            \"Enable Root User\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                                title: \"Activer l'utilisateur root\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 946,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        formData.enableRootUser ? \"Public SSH-Key for User Root\" : \"Public SSH-Key for User Admin\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Cl\\xe9 SSH publique pour l'acc\\xe8s\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 955,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: formData.publicSshKey,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        publicSshKey: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20\",\n                                                                            rows: 3,\n                                                                            placeholder: \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-2 right-2 flex flex-col space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        const key = generateExampleSSHKey();\n                                                                                        setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: key\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors\",\n                                                                                    title: \"G\\xe9n\\xe9rer une cl\\xe9 SSH d'exemple\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 979,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 969,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                formData.publicSshKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>setFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                publicSshKey: \"\"\n                                                                                            })),\n                                                                                    className: \"px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                                                    title: \"Supprimer la cl\\xe9 SSH\",\n                                                                                    disabled: isLoading,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 989,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 982,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 968,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Cloud-Init\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                                            title: \"Template Cloud-Init pour la configuration automatique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 998,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: formData.cloudInitTemplate,\n                                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: e.target.value\n                                                                                    })),\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10\",\n                                                                            disabled: isLoading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Select cloud init template\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                    lineNumber: 1009,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: app.applicationId || app.id,\n                                                                                        children: [\n                                                                                            app.name,\n                                                                                            \" \",\n                                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                                        ]\n                                                                                    }, app.applicationId || app.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                        lineNumber: 1011,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1003,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        formData.cloudInitTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        cloudInitTemplate: \"\"\n                                                                                    })),\n                                                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500\",\n                                                                            title: \"Supprimer la s\\xe9lection\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                                lineNumber: 1023,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1002,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    value: formData.userData,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                userData: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2\",\n                                                                    rows: 8,\n                                                                    placeholder: \"#cloud-config # Configuration Cloud-Init personnalis\\xe9e packages: - nginx - docker.io\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1027,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 997,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        formData.installationType === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Syst\\xe8me d'exploitation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        loadingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1051,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Chargement des images...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.imageId,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        imageId: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(errors.imageId ? \"border-red-300\" : \"border-gray-300\"),\n                                                            disabled: isLoading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionner un syst\\xe8me d'exploitation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1063,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                availableImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: image.imageId || image.id,\n                                                                        children: [\n                                                                            image.name,\n                                                                            \" \",\n                                                                            image.version ? \"(\".concat(image.version, \")\") : \"\"\n                                                                        ]\n                                                                    }, image.imageId || image.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1055,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        errors.imageId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mt-1\",\n                                                            children: errors.imageId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1072,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Application (optionnel)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        loadingApplications ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Chargement des applications...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.selectedApplication,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        selectedApplication: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            disabled: isLoading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Aucune application (OS seulement)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1094,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                availableApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: app.applicationId || app.id,\n                                                                        children: [\n                                                                            app.name,\n                                                                            \" \",\n                                                                            app.version ? \"(\".concat(app.version, \")\") : \"\"\n                                                                        ]\n                                                                    }, app.applicationId || app.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        formData.selectedApplication && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-2 bg-green-50 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: [\n                                                                        \"Application s\\xe9lectionn\\xe9e: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: ((_availableApplications_find = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find === void 0 ? void 0 : _availableApplications_find.name) || formData.selectedApplication\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1106,\n                                                                            columnNumber: 53\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                ((_availableApplications_find1 = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find1 === void 0 ? void 0 : _availableApplications_find1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-600 mt-1\",\n                                                                    children: (_availableApplications_find2 = availableApplications.find((app)=>(app.applicationId || app.id) === formData.selectedApplication)) === null || _availableApplications_find2 === void 0 ? void 0 : _availableApplications_find2.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1111,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1077,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1122,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Mot de passe root\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                password: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(errors.password ? \"border-red-300\" : \"border-gray-300\"),\n                                                                    placeholder: \"Mot de passe pour l'acc\\xe8s root\",\n                                                                    disabled: isLoading\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                    title: showPassword ? \"Cacher le mot de passe\" : \"Voir le mot de passe\",\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1142,\n                                                                        columnNumber: 41\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1142,\n                                                                        columnNumber: 74\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: generateSecurePassword,\n                                                                    disabled: isGeneratingPassword || isLoading,\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 disabled:text-gray-400\",\n                                                                    title: \"G\\xe9n\\xe9rer un mot de passe s\\xe9curis\\xe9\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 \".concat(isGeneratingPassword ? \"animate-spin\" : \"\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        formData.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-2 rounded-full transition-all duration-300 \".concat(getPasswordStrength(formData.password).color),\n                                                                            style: {\n                                                                                width: \"\".concat(getPasswordStrength(formData.password).percentage, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                            lineNumber: 1158,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1157,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getPasswordStrength(formData.password).color.replace(\"bg-\", \"text-\")),\n                                                                        children: getPasswordStrength(formData.password).label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 1156,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mt-1\",\n                                                            children: errors.password\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                            lineNumber: 1172,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1120,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-50 border border-red-200 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600 text-sm\",\n                                                children: errors.submit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.access && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-50 border border-red-200 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600 text-sm\",\n                                                children: errors.access\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                lineNumber: 1187,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-3 pt-6 border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: onClose,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n                                                    disabled: isLoading,\n                                                    children: \"Annuler\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    disabled: isLoading,\n                                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                                lineNumber: 1208,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \"R\\xe9installation...\"\n                                                        ]\n                                                    }, void 0, true) : \"R\\xe9installer le VPS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, undefined),\n            showCustomImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Add Custom Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1226,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCustomImageModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1227,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1225,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Image URL\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"URL de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1238,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            value: formData.customImageUrl,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageUrl: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"https://example.com/my-custom-image.iso\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1237,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Image Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Nom de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.customImageName,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageName: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"My Custom Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"OS Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Type de syst\\xe8me d'exploitation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1270,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.customImageOsType,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageOsType: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Linux\",\n                                                    children: \"Linux\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Windows\",\n                                                    children: \"Windows\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1278,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1272,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Version\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Version du syst\\xe8me d'exploitation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1286,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.customImageVersion,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageVersion: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-red-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"22.04, 2022, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1288,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600 mt-1\",\n                                            children: \"Version is required.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1295,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Description\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Eye_EyeOff_HardDrive_Info_Key_RefreshCw_Settings_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 inline ml-1 text-blue-500\",\n                                                    title: \"Description de l'image personnalis\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1300,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.customImageDescription,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageDescription: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            rows: 3,\n                                            placeholder: \"Description de votre image personnalis\\xe9e...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1299,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1235,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 p-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowCustomImageModal(false),\n                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1315,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: async ()=>{\n                                        try {\n                                            // Validate required fields\n                                            if (!formData.customImageUrl) {\n                                                alert(\"Image URL is required\");\n                                                return;\n                                            }\n                                            if (!formData.customImageName) {\n                                                alert(\"Image Name is required\");\n                                                return;\n                                            }\n                                            if (!formData.customImageVersion) {\n                                                alert(\"Version is required\");\n                                                return;\n                                            }\n                                            console.log(\"Saving custom image:\", {\n                                                url: formData.customImageUrl,\n                                                name: formData.customImageName,\n                                                osType: formData.customImageOsType,\n                                                version: formData.customImageVersion,\n                                                description: formData.customImageDescription\n                                            });\n                                            // Save custom image via API\n                                            const result = await _services_customImageService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].createCustomImage({\n                                                url: formData.customImageUrl,\n                                                name: formData.customImageName,\n                                                osType: formData.customImageOsType,\n                                                version: formData.customImageVersion,\n                                                description: formData.customImageDescription\n                                            });\n                                            if (result.success) {\n                                                console.log(\"✅ Custom image created successfully:\", result.data);\n                                                // Refresh the custom images list\n                                                await fetchCustomImages();\n                                                // Update the custom image selector with the new image\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        customImageUrl: result.data.imageId || result.data.id,\n                                                        // Reset custom image form\n                                                        customImageName: \"\",\n                                                        customImageVersion: \"\",\n                                                        customImageDescription: \"\"\n                                                    }));\n                                                setShowCustomImageModal(false);\n                                                alert(\"Custom image created successfully!\");\n                                            }\n                                        } catch (error) {\n                                            console.error(\"❌ Failed to create custom image:\", error);\n                                            alert(\"Failed to create custom image: \" + error.message);\n                                        }\n                                    },\n                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    children: \"Upload\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                                    lineNumber: 1322,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                            lineNumber: 1314,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                    lineNumber: 1224,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\components\\\\ReinstallModal.jsx\",\n                lineNumber: 1223,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ReinstallModal, \"HmcAqSAvuZjVYO+emn2HW3sZd1A=\");\n_c = ReinstallModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ReinstallModal);\nvar _c;\n$RefreshReg$(_c, \"ReinstallModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/ReinstallModal.jsx\n"));

/***/ })

});