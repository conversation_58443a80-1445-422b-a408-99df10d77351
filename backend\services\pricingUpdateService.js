/**
 * Pricing Update Service
 * Updates existing VPS packages or creates new ones based on scraped Contabo data
 * Integrates with existing Package model and VPS system
 */

const Package = require('../models/Package');
const Category = require('../models/Category');
const Brand = require('../models/Brand');
const Specification = require('../models/Specification');
const ProductStatus = require('../constants/enums/poduct-status');
const { v4: uuidv4 } = require('uuid');

class PricingUpdateService {
  constructor() {
    this.provider = 'contabo';
    this.brandName = 'Contabo';
    this.categoryName = 'VPS';

    // Static discount structure for all VPS packages
    this.staticDiscounts = [
      { period: 6, percentage: 5 },   // 5% discount for 6 months
      { period: 12, percentage: 15 }  // 15% discount for 12 months
    ];
  }

  /**
   * Update or create VPS packages from scraped data
   * @param {Array} scrapedPackages - Array of packages from Gemini scraping
   * @returns {Promise<Object>} Update results
   */
  async updateContaboPackages(scrapedPackages) {
    try {
      console.log(`[PRICING] Processing ${scrapedPackages.length} scraped packages...`);

      // Ensure VPS category and Contabo brand exist
      const { category, brand } = await this.ensureCategoryAndBrand();

      const results = {
        updated: 0,
        created: 0,
        errors: [],
        packages: []
      };

      for (const scrapedPkg of scrapedPackages) {
        try {
          const result = await this.processPackage(scrapedPkg, category, brand);
          
          if (result.action === 'updated') {
            results.updated++;
          } else if (result.action === 'created') {
            results.created++;
          } else if (result.action === 'unchanged') {
            // Package exists but no price change - don't count as updated
            console.log(`[PRICING] No changes for ${scrapedPkg.name}`);
          }
          
          results.packages.push(result);
          
        } catch (error) {
          console.error(`[PRICING] Error processing package ${scrapedPkg.name}:`, error.message);
          results.errors.push({
            package: scrapedPkg.name,
            error: error.message
          });
        }
      }

      console.log(`[PRICING] Update complete: ${results.created} created, ${results.updated} updated, ${results.errors.length} errors`);
      return results;

    } catch (error) {
      console.error('[PRICING] Failed to update packages:', error.message);
      throw error;
    }
  }

  /**
   * Process a single package (update existing or create new)
   * @param {Object} scrapedPkg - Scraped package data
   * @param {Object} category - VPS category
   * @param {Object} brand - Contabo brand
   * @returns {Promise<Object>} Process result
   */
  async processPackage(scrapedPkg, category, brand) {
    // Generate provider product ID from package name
    const providerProductId = this.generateProviderProductId(scrapedPkg.name);
    
    // Try to find existing package by provider product ID
    let existingPackage = await Package.findOne({
      'vpsConfig.provider': this.provider,
      'vpsConfig.providerProductId': providerProductId
    });

    if (existingPackage) {
      // Update existing package
      return await this.updateExistingPackage(existingPackage, scrapedPkg);
    } else {
      // Create new package
      return await this.createNewPackage(scrapedPkg, category, brand, providerProductId);
    }
  }

  /**
   * Update an existing package with new pricing and specifications
   * @param {Object} existingPackage - Existing package document
   * @param {Object} scrapedPkg - New scraped data
   * @returns {Promise<Object>} Update result
   */
  async updateExistingPackage(existingPackage, scrapedPkg) {
    const oldPrice = existingPackage.price;
    const newPrice = scrapedPkg.pricing.monthly;

    // Check if there's actually a price change
    const priceChanged = oldPrice !== newPrice;

    // Update package fields
    existingPackage.price = newPrice;
    existingPackage.regularPrice = newPrice;
    existingPackage.description = this.generateDescription(scrapedPkg.specifications);
    existingPackage.discounts = this.staticDiscounts; // Update discount structure
    existingPackage.vpsConfig.providerPlanName = scrapedPkg.name;

    // Update specifications
    await this.updatePackageSpecifications(existingPackage, scrapedPkg.specifications);

    await existingPackage.save();

    if (priceChanged) {
      console.log(`[PRICING] Price changed for ${scrapedPkg.name}: $${oldPrice} → $${newPrice}`);
    } else {
      console.log(`[PRICING] No price change for ${scrapedPkg.name}: $${oldPrice} (updated other fields)`);
    }

    return {
      action: priceChanged ? 'updated' : 'unchanged',
      package: existingPackage,
      priceChange: {
        old: oldPrice,
        new: newPrice,
        difference: newPrice - oldPrice,
        percentageChange: oldPrice > 0 ? ((newPrice - oldPrice) / oldPrice * 100).toFixed(2) : 0
      }
    };
  }

  /**
   * Create a new VPS package
   * @param {Object} scrapedPkg - Scraped package data
   * @param {Object} category - VPS category
   * @param {Object} brand - Contabo brand
   * @param {string} providerProductId - Generated provider product ID
   * @returns {Promise<Object>} Creation result
   */
  async createNewPackage(scrapedPkg, category, brand, providerProductId) {
    const reference = uuidv4().slice(0, 8);
    
    const packageData = {
      reference: reference,
      name: scrapedPkg.name,
      name_fr: scrapedPkg.name,
      quantity: 1,
      description: this.generateDescription(scrapedPkg.specifications),
      description_fr: this.generateDescription(scrapedPkg.specifications),
      price: scrapedPkg.pricing.monthly,
      regularPrice: scrapedPkg.pricing.monthly,
      discounts: this.staticDiscounts, // Add static discount structure
      category: category._id,
      brand: brand._id,
      specifications: [],
      status: ProductStatus.PUBLISHED,
      vpsConfig: {
        provider: this.provider,
        providerProductId: providerProductId,
        providerPlanName: scrapedPkg.name
      }
    };

    const newPackage = new Package(packageData);
    await newPackage.save();

    // Update brand with new package reference
    await Brand.findByIdAndUpdate(
      brand._id,
      { $push: { packages: newPackage._id } },
      { new: true }
    );

    // Create specifications
    await this.createPackageSpecifications(newPackage, scrapedPkg.specifications);

    console.log(`[PRICING] Created new package: ${scrapedPkg.name} - $${scrapedPkg.pricing.monthly}`);

    return {
      action: 'created',
      package: newPackage,
      priceChange: {
        old: 0,
        new: scrapedPkg.pricing.monthly,
        difference: scrapedPkg.pricing.monthly,
        percentageChange: 'new'
      }
    };
  }

  /**
   * Ensure VPS category and Contabo brand exist with proper linking
   * @returns {Promise<Object>} Category and brand objects
   */
  async ensureCategoryAndBrand() {
    // Find or create VPS category
    let category = await Category.findOne({ name: this.categoryName });
    if (!category) {
      category = new Category({
        name: this.categoryName,
        name_fr: this.categoryName,
        description: 'Virtual Private Servers',
        description_fr: 'Serveurs Privés Virtuels',
        brands: [] // Initialize empty brands array
      });
      await category.save();
      console.log('[PRICING] Created VPS category');
    }

    // Find or create Contabo brand
    let brand = await Brand.findOne({ name: this.brandName });
    if (!brand) {
      brand = new Brand({
        name: this.brandName,
        name_fr: this.brandName,
        category: category._id,
        packages: []
      });
      await brand.save();
      console.log('[PRICING] Created Contabo brand');

      // Add brand to category's brands array if not already there
      if (!category.brands.includes(brand._id)) {
        category.brands.push(brand._id);
        await category.save();
        console.log('[PRICING] Added Contabo brand to VPS category');
      }
    } else {
      // Brand exists, ensure it's linked to the category
      if (!category.brands.includes(brand._id)) {
        category.brands.push(brand._id);
        await category.save();
        console.log('[PRICING] Linked existing Contabo brand to VPS category');
      }
    }

    return { category, brand };
  }

  /**
   * Generate provider product ID from package name
   * @param {string} packageName - Package name like "Cloud VPS 10"
   * @returns {string} Provider product ID like "V10"
   */
  generateProviderProductId(packageName) {
    // Extract number from "Cloud VPS 10" -> "V10"
    const match = packageName.match(/(\d+)/);
    return match ? `V${match[1]}` : packageName.replace(/\s+/g, '').toUpperCase();
  }

  /**
   * Generate package description from specifications
   * @param {Object} specs - Package specifications
   * @returns {string} Generated description
   */
  generateDescription(specs) {
    return `${specs.vCPU}, ${specs.ram}, ${specs.storage}`;
  }

  /**
   * Calculate discounted prices for display
   * @param {number} monthlyPrice - Base monthly price
   * @returns {Object} Pricing breakdown with discounts
   */
  calculateDiscountedPrices(monthlyPrice) {
    const pricing = {
      monthly: monthlyPrice,
      sixMonths: {
        total: monthlyPrice * 6,
        discountPercentage: 5,
        discountAmount: (monthlyPrice * 6 * 5) / 100,
        finalTotal: monthlyPrice * 6 * (1 - 5/100),
        monthlyEquivalent: (monthlyPrice * 6 * (1 - 5/100)) / 6
      },
      yearly: {
        total: monthlyPrice * 12,
        discountPercentage: 15,
        discountAmount: (monthlyPrice * 12 * 15) / 100,
        finalTotal: monthlyPrice * 12 * (1 - 15/100),
        monthlyEquivalent: (monthlyPrice * 12 * (1 - 15/100)) / 12
      }
    };

    return pricing;
  }

  /**
   * Create specifications for a new package
   * @param {Object} packageDoc - Package document
   * @param {Object} specs - Specifications data
   */
  async createPackageSpecifications(packageDoc, specs) {
    const specData = [
      { name: 'vCPU', value: specs.vCPU },
      { name: 'RAM', value: specs.ram },
      { name: 'Storage', value: specs.storage },
      { name: 'Snapshots', value: specs.snapshots },
      { name: 'Traffic', value: specs.traffic }
    ];

    const specIds = [];
    for (const spec of specData) {
      const specification = new Specification({
        name: spec.name,
        name_fr: spec.name,
        value: spec.value,
        value_fr: spec.value
      });
      await specification.save();
      specIds.push(specification._id);
    }

    packageDoc.specifications = specIds;
    await packageDoc.save();
  }

  /**
   * Update specifications for an existing package
   * @param {Object} packageDoc - Package document
   * @param {Object} specs - New specifications data
   */
  async updatePackageSpecifications(packageDoc, specs) {
    // For now, we'll keep existing specifications
    // In a more advanced implementation, we could update them
    console.log(`[PRICING] Specifications update for ${packageDoc.name} (keeping existing)`);
  }
}

module.exports = PricingUpdateService;
