<!DOCTYPE html>
<html>
<head>
    <title>Test API VPS</title>
</head>
<body>
    <h1>Test API VPS</h1>
    <button onclick="testPackagesAPI()">Test Packages API</button>
    <button onclick="testRegionsAPI()">Test Regions API</button>
    <div id="results"></div>

    <script>
        async function testPackagesAPI() {
            try {
                console.log('Testing packages API...');
                const response = await fetch('http://localhost:5002/package/get-packages?brandName=VPS%20Hosting');
                const data = await response.json();
                console.log('Packages response:', data);
                
                document.getElementById('results').innerHTML = `
                    <h2>Packages API Response:</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // Test if we can find the specific package
                const targetPackage = data.find(pkg => pkg._id === '687109aa5096836f2776d174');
                if (targetPackage) {
                    console.log('✅ Found target package:', targetPackage.name);
                } else {
                    console.log('❌ Target package not found');
                }
            } catch (error) {
                console.error('Error testing packages API:', error);
                document.getElementById('results').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }

        async function testRegionsAPI() {
            try {
                console.log('Testing regions API...');
                const packageId = '687109aa5096836f2776d174'; // CLOUD VPS 20
                const response = await fetch(`http://localhost:5002/api/regions/package/${packageId}`);
                const data = await response.json();
                console.log('Regions response:', data);
                
                document.getElementById('results').innerHTML = `
                    <h2>Regions API Response for Package ${packageId}:</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error testing regions API:', error);
                document.getElementById('results').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
