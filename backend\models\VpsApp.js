const mongoose = require('mongoose');

const variantSchema = new mongoose.Schema({
  variantId: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(v);
      },
      message: 'variantId must be a valid UUID format'
    }
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true
  }
}, { _id: false });

const vpsAppSchema = new mongoose.Schema({
  appId: {
    type: String,
    required: true,
    unique: true,
    index: true,
    validate: {
      validator: function(v) {
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(v);
      },
      message: 'appId must be a valid UUID format'
    }
  },
  name: {
    type: String,
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['control_panel', 'lamp', 'blockchain'],
    index: true
  },
  supportedOs: [{
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(v);
      },
      message: 'supportedOs must contain valid UUID format'
    }
  }],
  variants: [variantSchema],
  dependencies: [{
    type: String,
    validate: {
      validator: function(v) {
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(v);
      },
      message: 'dependencies must contain valid UUID format'
    }
  }],
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['published', 'draft'],
    default: 'draft',
    index: true
  },
  customIcon: {
    type: String,
    default: null
  },
  iconType: {
    type: String,
    enum: ['cpanel', 'plesk', 'blockchain', 'custom'],
    default: 'custom'
  },
  category: {
    type: String,
    enum: ['apps', 'blockchain'],
    default: 'apps',
    index: true
  },
  isPopular: {
    type: Boolean,
    default: false,
    index: true
  },
  displayOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  collection: 'vps_apps'
});

vpsAppSchema.index({ name: 1 });
vpsAppSchema.index({ type: 1 });
vpsAppSchema.index({ appId: 1 });

vpsAppSchema.statics.findByAppId = function(appId) {
  return this.findOne({ appId: appId });
};

vpsAppSchema.statics.findByType = function(type) {
  return this.find({ type: type });
};

vpsAppSchema.statics.findBySupportedOS = function(osId) {
  return this.find({ supportedOs: osId });
};

vpsAppSchema.statics.getControlPanels = function() {
  return this.findByType('control_panel');
};

vpsAppSchema.statics.getLAMPStacks = function() {
  return this.findByType('lamp');
};

vpsAppSchema.statics.getBlockchainApps = function() {
  return this.findByType('blockchain');
};

vpsAppSchema.statics.getAllApps = function() {
  return this.find({}).sort({ name: 1 });
};

vpsAppSchema.methods.getDisplayName = function() {
  return this.description || this.name;
};

vpsAppSchema.methods.getCheapestVariant = function() {
  if (this.variants && this.variants.length > 0) {
    return this.variants.reduce((min, variant) =>
      variant.price < min.price ? variant : min
    );
  }
  return null;
};

vpsAppSchema.methods.getMostExpensiveVariant = function() {
  if (this.variants && this.variants.length > 0) {
    return this.variants.reduce((max, variant) =>
      variant.price > max.price ? variant : max
    );
  }
  return null;
};

vpsAppSchema.methods.isCompatibleWithOS = function(osId) {
  return this.supportedOs.includes(osId);
};

vpsAppSchema.methods.hasDependencies = function() {
  return this.dependencies && this.dependencies.length > 0;
};

const VpsApp = mongoose.model('VpsApp', vpsAppSchema);

module.exports = VpsApp;
