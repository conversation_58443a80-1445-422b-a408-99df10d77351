const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiParsingService {
  constructor() {
    this.apiKey = 'AIzaSyBB3NN4eJvC9XcsnlCS9jVND7QpaU83Kvo';
    this.genAI = new GoogleGenerativeAI(this.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
  }

  async scrapeContaboPricing() {
    console.log('🌐 Using Gemini URL Context to scrape Contabo VPS page directly...');

    const prompt = `Extract all VPS packages, prices (give me the monthly price not yearly! this is so important don't give me the discounted price), and specifications from this Contabo VPS page. Return as JSON format (exactly like the example below , don't answer with anything else than this !) :
{
  "packages": [
    {
      "name": "package name",
      "pricing": {"monthly": price_number},
      "specifications": {
        "vCPU": "vCPU Cores info",
        "ram": "RAM info",
        "storage": "Storage (NVMe or SSD) info",
        "snapshots": "Snapshots info",
        "traffic": "Traffic info"
      }
    }
  ]
}`;

    // Retry configuration
    const maxAttempts = 3;
    const retryDelayMs = 2 * 60 * 1000; // 2 minutes in milliseconds
    let lastError = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`[GEMINI] Attempt ${attempt}/${maxAttempts}: Calling Gemini API...`);

        const result = await this.model.generateContent({
          contents: [{ parts: [{ text: `${prompt}\n\nURL: https://contabo.com/en-us/vps/` }] }],
          tools: [{ urlContext: {} }]
        });

        const responseText = result.response.text().trim();

        console.log('\n🤖 GEMINI DIRECT SCRAPING RESPONSE:');
        console.log(responseText);
        console.log('\n');

        // Show URL metadata if available
        if (result.response.candidates?.[0]?.urlContextMetadata) {
          console.log('📄 URLs Retrieved:');
          console.log(result.response.candidates[0].urlContextMetadata);
        }

        // Validate response contains expected VPS data structure
        if (!responseText || responseText.trim().length === 0) {
          throw new Error('Empty response from Gemini API');
        }

        if (!responseText.includes('packages') || !responseText.includes('pricing')) {
          throw new Error('Response does not contain expected VPS data structure');
        }

        // Success - return the response
        if (attempt > 1) {
          console.log(`[GEMINI] ✅ Success on attempt ${attempt}/${maxAttempts}`);
        }
        return { success: true, response: responseText };

      } catch (error) {
        lastError = error;
        console.error(`[GEMINI] ❌ Attempt ${attempt}/${maxAttempts} failed:`, error.message);

        // If this is not the last attempt, wait and retry
        if (attempt < maxAttempts) {
          console.log(`[GEMINI] ⏳ Waiting ${retryDelayMs / 1000} seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        }
      }
    }

    // All attempts failed
    console.error(`[GEMINI] ❌ All ${maxAttempts} attempts failed. Last error:`, lastError.message);
    return {
      success: false,
      error: lastError.message,
      attempts: maxAttempts
    };
  }
}

module.exports = GeminiParsingService;
