# Tests avec curl

## 1. Test endpoint images
```bash
curl -X GET "http://localhost:5002/api/vps/images" \
  -H "Content-Type: application/json"
```

## 2. Test endpoint applications
```bash
curl -X GET "http://localhost:5002/api/vps/applications" \
  -H "Content-Type: application/json"
```

## 3. Test validation endpoint reinstall (donn<PERSON> invalides)
```bash
curl -X POST "http://localhost:5002/api/vps/instances/test-instance/reinstall" \
  -H "Content-Type: application/json" \
  -d "{}"
```

## 4. Test validation avec données valides
```bash
curl -X POST "http://localhost:5002/api/vps/instances/test-instance/reinstall" \
  -H "Content-Type: application/json" \
  -d '{
    "imageId": "test-image-id",
    "password": "TestPassword123!"
  }'
```

## 5. Test validation avec toutes les options
```bash
curl -X POST "http://localhost:5002/api/vps/instances/test-instance/reinstall" \
  -H "Content-Type: application/json" \
  -d '{
    "imageId": "ubuntu-22.04",
    "password": "SecurePassword123!",
    "sshKeys": ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
    "enableRootUser": true,
    "applicationId": "docker",
    "userData": "#cloud-config\npackages:\n  - nginx"
  }'
```

## Réponses attendues :

### ✅ Succès (200)
- Images et applications : Liste des éléments disponibles

### ❌ Erreur de validation (400)
- Message d'erreur avec détails des champs manquants

### ❌ Instance non trouvée (404)
- Message indiquant que l'instance n'existe pas
