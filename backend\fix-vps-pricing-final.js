const mongoose = require('mongoose');
const VPSRegion = require('./models/VPSRegion');
const Package = require('./models/Package');

async function fixVPSPricingFinal() {
  try {
    console.log('🚀 SOLUTION FINALE - Nettoyage complet des prix VPS...');
    
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/hosting-website');
    console.log('✅ Connected to MongoDB');

    // 1. Get VPS packages
    const vpsPackages = await Package.find({
      name: { $in: ['CLOUD VPS 10', 'CLOUD VPS 20'] }
    }).select('_id name');

    if (vpsPackages.length !== 2) {
      console.error('❌ Expected 2 VPS packages, found:', vpsPackages.length);
      return;
    }

    console.log('\n📦 VPS Packages found:');
    vpsPackages.forEach(pkg => {
      console.log(`  ${pkg.name}: ${pkg._id}`);
    });

    const vps10 = vpsPackages.find(p => p.name === 'CLOUD VPS 10');
    const vps20 = vpsPackages.find(p => p.name === 'CLOUD VPS 20');

    // 2. Get all regions
    const regions = await VPSRegion.find();
    console.log(`\n🌍 Found ${regions.length} regions to fix`);

    let fixedCount = 0;

    for (const region of regions) {
      console.log(`\n🔧 Fixing region: ${region.name} (${region.regionId})`);
      console.log(`   Current pricing entries: ${region.pricing.length}`);

      // Show current pricing
      region.pricing.forEach((p, index) => {
        console.log(`   ${index + 1}. ${p.packageName} (${p.packageId}): ${p.additionalPrice} MAD`);
      });

      // SOLUTION: Recréer complètement le pricing avec des entrées séparées
      const newPricing = [];

      // Pour VPS 10
      const vps10Pricing = region.pricing.find(p => 
        p.packageId.toString() === vps10._id.toString()
      );
      newPricing.push({
        packageId: vps10._id,
        packageName: vps10.name,
        additionalPrice: vps10Pricing ? vps10Pricing.additionalPrice : 0
      });

      // Pour VPS 20
      const vps20Pricing = region.pricing.find(p => 
        p.packageId.toString() === vps20._id.toString()
      );
      newPricing.push({
        packageId: vps20._id,
        packageName: vps20.name,
        additionalPrice: vps20Pricing ? vps20Pricing.additionalPrice : 0
      });

      // Remplacer complètement le pricing
      region.pricing = newPricing;

      console.log(`   ✅ New pricing structure:`);
      region.pricing.forEach((p, index) => {
        console.log(`   ${index + 1}. ${p.packageName} (${p.packageId}): ${p.additionalPrice} MAD`);
      });

      // Sauvegarder
      await region.save();
      fixedCount++;
      console.log(`   💾 Region ${region.name} saved successfully`);
    }

    console.log(`\n🎉 TERMINÉ ! ${fixedCount} régions corrigées.`);
    console.log('\n✅ Chaque région a maintenant exactement 2 entrées de pricing :');
    console.log('   - Une pour CLOUD VPS 10');
    console.log('   - Une pour CLOUD VPS 20');
    console.log('\n🔄 Redémarrez votre serveur et testez l\'interface admin !');

    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixVPSPricingFinal();
