const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Assuming you have a User model for clients
    // For admin notifications, this could be null or a specific admin user ID
  },
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin', // Assuming you have an Admin model
  },
  userType: {
    type: String,
    enum: ['client', 'admin'],
    required: true,
  },
  type: {
    type: String,
    required: true,
    enum: [
      'new_ticket',
      'ticket_status_update',
      'ticket_updated',
      'ticket_deleted',
      'ssl_expiry',
      'order_update',
      'ssl_update',
      'hosting_update',
      'webdev_update',
      'new_blog_post',
      'general',
      'user_registered',
      'user_verified',
      'abandoned_cart',
      'custom_notification'
    ], // Add more types as needed
  },
  title: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    required: true,
  },
  link: {
    type: String, // Optional link to navigate to (e.g., /admin/tickets/ticketId)
  },
  // Fields to store information about who performed the action
  actionBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
    },
    role: {
      type: String,
    }
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ adminId: 1, createdAt: -1 });
notificationSchema.index({ type: 1, createdAt: -1 });

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;