const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const VpsOS = require('../../models/VpsOS');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

const router = express.Router();

// Configuration multer pour l'upload d'icônes
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/os-icons');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non supporté. Utilisez JPG, PNG, SVG ou WebP.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB max
  }
});

// Middleware d'authentification pour toutes les routes
// TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
// router.use(authenticateToken);
// router.use(requireAdmin);

/**
 * @route GET /api/admin/os-images
 * @desc Récupérer toutes les images OS
 * @access Admin
 */
router.get('/', async (req, res) => {
  try {
    const { status, category, search, page = 1, limit = 20 } = req.query;
    
    // Construire le filtre
    let filter = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { version: { $regex: search, $options: 'i' } }
      ];
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [osImages, total] = await Promise.all([
      VpsOS.find(filter)
        .sort({ displayOrder: 1, name: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      VpsOS.countDocuments(filter)
    ]);

    // Ajouter l'URL complète des icônes
    const osImagesWithIcons = osImages.map(image => ({
      ...image.toObject(),
      iconUrl: image.customIcon ? `/uploads/os-icons/${image.customIcon}` : null
    }));

    res.json({
      success: true,
      data: osImagesWithIcons,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des images OS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des images OS'
    });
  }
});

/**
 * @route GET /api/admin/os-images/:id
 * @desc Récupérer une image OS par ID
 * @access Admin
 */
router.get('/:id', async (req, res) => {
  try {
    const osImage = await VpsOS.findByOsId(req.params.id);
    
    if (!osImage) {
      return res.status(404).json({
        success: false,
        message: 'Image OS non trouvée'
      });
    }

    const osImageWithIcon = {
      ...osImage.toObject(),
      iconUrl: osImage.customIcon ? `/uploads/os-icons/${osImage.customIcon}` : null
    };

    res.json({
      success: true,
      data: osImageWithIcon
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'image OS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'image OS'
    });
  }
});

/**
 * @route POST /api/admin/os-images
 * @desc Créer une nouvelle image OS
 * @access Admin
 */
router.post('/', upload.single('icon'), async (req, res) => {
  try {
    const {
      name,
      version,
      description,
      category = 'os',
      price = 'Inclus',
      iconType = 'custom',
      displayOrder = 0,
      status = 'draft',
      osType = 'Linux',
      isLTS = false,
      isPopular = false,
      releaseDate
    } = req.body;

    // Validation des champs requis
    if (!name || !version || !description) {
      return res.status(400).json({
        success: false,
        message: 'Les champs nom, version et description sont requis'
      });
    }

    // Générer un nouvel osId
    const osId = uuidv4();

    // Préparer les données
    const osData = {
      osId,
      name: name.trim(),
      version: version.trim(),
      description: description.trim(),
      category,
      price,
      iconType,
      displayOrder: parseInt(displayOrder) || 0,
      status,
      osType,
      isLTS: isLTS === 'true' || isLTS === true,
      isPopular: isPopular === 'true' || isPopular === true,
      releaseDate: releaseDate ? new Date(releaseDate) : null
    };

    // Ajouter l'icône personnalisée si uploadée
    if (req.file) {
      osData.customIcon = req.file.filename;
      osData.iconType = 'custom';
    }

    // Créer l'image OS
    const newOsImage = new VpsOS(osData);
    await newOsImage.save();

    const osImageWithIcon = {
      ...newOsImage.toObject(),
      iconUrl: newOsImage.customIcon ? `/uploads/os-icons/${newOsImage.customIcon}` : null
    };

    res.status(201).json({
      success: true,
      message: 'Image OS créée avec succès',
      data: osImageWithIcon
    });
  } catch (error) {
    // Supprimer le fichier uploadé en cas d'erreur
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression du fichier:', unlinkError);
      }
    }

    console.error('Erreur lors de la création de l\'image OS:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Une image OS avec cet ID existe déjà'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de l\'image OS'
    });
  }
});

/**
 * @route PUT /api/admin/os-images/:id
 * @desc Modifier une image OS
 * @access Admin
 */
router.put('/:id', upload.single('icon'), async (req, res) => {
  try {
    const osImage = await VpsOS.findByOsId(req.params.id);
    
    if (!osImage) {
      // Supprimer le fichier uploadé si l'image n'existe pas
      if (req.file) {
        try {
          await fs.unlink(req.file.path);
        } catch (unlinkError) {
          console.error('Erreur lors de la suppression du fichier:', unlinkError);
        }
      }
      
      return res.status(404).json({
        success: false,
        message: 'Image OS non trouvée'
      });
    }

    const {
      name,
      version,
      description,
      category,
      price,
      iconType,
      displayOrder,
      status,
      osType,
      isLTS,
      isPopular,
      releaseDate
    } = req.body;

    // Mettre à jour les champs
    if (name) osImage.name = name.trim();
    if (version) osImage.version = version.trim();
    if (description) osImage.description = description.trim();
    if (category) osImage.category = category;
    if (price !== undefined) osImage.price = price;
    if (iconType) osImage.iconType = iconType;
    if (displayOrder !== undefined) osImage.displayOrder = parseInt(displayOrder) || 0;
    if (status) osImage.status = status;
    if (osType) osImage.osType = osType;
    if (isLTS !== undefined) osImage.isLTS = isLTS === 'true' || isLTS === true;
    if (isPopular !== undefined) osImage.isPopular = isPopular === 'true' || isPopular === true;
    if (releaseDate) osImage.releaseDate = new Date(releaseDate);

    // Gérer la nouvelle icône
    if (req.file) {
      // Supprimer l'ancienne icône si elle existe
      if (osImage.customIcon) {
        try {
          await fs.unlink(path.join(__dirname, '../../uploads/os-icons', osImage.customIcon));
        } catch (unlinkError) {
          console.error('Erreur lors de la suppression de l\'ancienne icône:', unlinkError);
        }
      }
      
      osImage.customIcon = req.file.filename;
      osImage.iconType = 'custom';
    }

    await osImage.save();

    const osImageWithIcon = {
      ...osImage.toObject(),
      iconUrl: osImage.customIcon ? `/uploads/os-icons/${osImage.customIcon}` : null
    };

    res.json({
      success: true,
      message: 'Image OS modifiée avec succès',
      data: osImageWithIcon
    });
  } catch (error) {
    // Supprimer le fichier uploadé en cas d'erreur
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression du fichier:', unlinkError);
      }
    }

    console.error('Erreur lors de la modification de l\'image OS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la modification de l\'image OS'
    });
  }
});

/**
 * @route DELETE /api/admin/os-images/:id
 * @desc Supprimer une image OS
 * @access Admin
 */
router.delete('/:id', async (req, res) => {
  try {
    const osImage = await VpsOS.findByOsId(req.params.id);

    if (!osImage) {
      return res.status(404).json({
        success: false,
        message: 'Image OS non trouvée'
      });
    }

    // Supprimer l'icône personnalisée si elle existe
    if (osImage.customIcon) {
      try {
        await fs.unlink(path.join(__dirname, '../../uploads/os-icons', osImage.customIcon));
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression de l\'icône:', unlinkError);
      }
    }

    await VpsOS.deleteOne({ osId: req.params.id });

    res.json({
      success: true,
      message: 'Image OS supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'image OS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression de l\'image OS'
    });
  }
});

/**
 * @route PATCH /api/admin/os-images/:id/status
 * @desc Changer le statut d'une image OS (published/draft)
 * @access Admin
 */
router.patch('/:id/status', async (req, res) => {
  try {
    const { status } = req.body;

    if (!['published', 'draft'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Statut invalide. Utilisez "published" ou "draft"'
      });
    }

    const osImage = await VpsOS.findByOsId(req.params.id);

    if (!osImage) {
      return res.status(404).json({
        success: false,
        message: 'Image OS non trouvée'
      });
    }

    osImage.status = status;
    await osImage.save();

    res.json({
      success: true,
      message: `Image OS ${status === 'published' ? 'publiée' : 'mise en brouillon'} avec succès`,
      data: { status: osImage.status }
    });
  } catch (error) {
    console.error('Erreur lors du changement de statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du changement de statut'
    });
  }
});

/**
 * @route GET /api/admin/os-images/stats
 * @desc Récupérer les statistiques des images OS
 * @access Admin
 */
router.get('/stats/overview', async (req, res) => {
  try {
    const [total, published, draft, popular, byCategory] = await Promise.all([
      VpsOS.countDocuments(),
      VpsOS.countDocuments({ status: 'published' }),
      VpsOS.countDocuments({ status: 'draft' }),
      VpsOS.countDocuments({ category: 'popular' }),
      VpsOS.aggregate([
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ])
    ]);

    const categoryStats = {};
    byCategory.forEach(item => {
      categoryStats[item._id] = item.count;
    });

    res.json({
      success: true,
      data: {
        total,
        published,
        draft,
        popular,
        byCategory: categoryStats
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des statistiques'
    });
  }
});

module.exports = router;
