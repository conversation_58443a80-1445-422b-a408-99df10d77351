'use client'

import React, { useState, useEffect } from 'react';
import {
  HardDrive,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Save,
  X,
  Upload,
  Settings,
  Monitor,
  Smartphone,
  Globe,
  Package,
  CheckCircle,
  Clock,
  AlertCircle,
  Tag,
  DollarSign,
  Layers,
  Server,
  Database,
  Cpu,
  MemoryStick,
  HardDriveIcon,
  Zap,
  Shield,
  Activity
} from 'lucide-react';
import { Card, Typography, Button, Input, Chip, Badge, Switch, Textarea, Dialog, DialogHeader, DialogBody, DialogFooter } from "@material-tailwind/react";
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import ImageModal from './components/ImageModal';
import PreviewModal from './components/PreviewModal';
import { osImagesService, appImagesService, fileUtils } from '../../services/vpsImagesService';

export default function VPSImagesAdmin() {
  // États principaux
  const [loading, setLoading] = useState(false);
  const [osImages, setOsImages] = useState([]);
  const [appImages, setAppImages] = useState([]);
  const [activeTab, setActiveTab] = useState('os'); // 'os' | 'apps'
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all'); // 'all' | 'published' | 'draft'
  const [filterCategory, setFilterCategory] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // États pour le formulaire
  const [formData, setFormData] = useState({
    // Champs communs
    name: '',
    description: '',
    version: '',
    status: 'draft', // 'published' | 'draft'
    category: 'os',
    price: 'Inclus',
    iconType: 'custom',
    displayOrder: 0,
    
    // Champs spécifiques aux apps
    type: 'control_panel',
    supportedOs: [],
    variants: [],
    dependencies: [],
    
    // Champs spécifiques aux OS
    osType: 'Linux',
    isLTS: false,
    releaseDate: null
  });

  // Données de référence
  const osTypes = ['Linux', 'Windows'];
  const categories = {
    os: ['os'],
    apps: ['apps', 'blockchain']
  };
  const appTypes = ['control_panel', 'lamp', 'blockchain'];
  const iconTypes = {
    os: ['ubuntu', 'windows', 'debian', 'centos', 'almalinux', 'rockylinux', 'fedora', 'opensuse', 'freebsd', 'archlinux', 'custom'],
    apps: ['cpanel', 'plesk', 'blockchain', 'custom']
  };



  // Charger les données au montage
  useEffect(() => {
    loadImages();
  }, []);

  const loadImages = async () => {
    setLoading(true);
    try {
      const [osResponse, appResponse] = await Promise.all([
        osImagesService.getAll(),
        appImagesService.getAll()
      ]);

      setOsImages(osResponse.data || []);
      setAppImages(appResponse.data || []);

      console.log('Images chargées:', {
        osImages: osResponse.data?.length || 0,
        appImages: appResponse.data?.length || 0,
        popularOS: osResponse.data?.filter(img => img.category === 'popular').length || 0,
        popularApps: appResponse.data?.filter(img => img.category === 'popular').length || 0
      });

      // Debug: afficher les catégories disponibles
      if (osResponse.data?.length > 0) {
        const osCategories = [...new Set(osResponse.data.map(img => img.category))];
        console.log('Catégories OS disponibles:', osCategories);
        console.log('Exemple d\'image OS:', osResponse.data[0]);
      }

      if (appResponse.data?.length > 0) {
        const appCategories = [...new Set(appResponse.data.map(img => img.category))];
        console.log('Catégories Apps disponibles:', appCategories);
        console.log('Exemple d\'image App:', appResponse.data[0]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des images:', error);
      toast.error(error.message || 'Erreur lors du chargement des images');
      setOsImages([]);
      setAppImages([]);
    } finally {
      setLoading(false);
    }
  };

  // Filtrer et trier les images
  const getFilteredImages = () => {
    const images = activeTab === 'os' ? osImages : appImages;

    return images
      .filter(image => {
        const matchesSearch = image.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             image.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = filterStatus === 'all' || image.status === filterStatus;
        const matchesCategory = filterCategory === 'all' || image.category === filterCategory;

        return matchesSearch && matchesStatus && matchesCategory;
      })
      .sort((a, b) => {
        // Trier d'abord par displayOrder (croissant), puis par nom (alphabétique)
        if (a.displayOrder !== b.displayOrder) {
          return (a.displayOrder || 0) - (b.displayOrder || 0);
        }
        return a.name.localeCompare(b.name);
      });
  };

  // Obtenir les images paginées
  const getPaginatedImages = () => {
    const filteredImages = getFilteredImages();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredImages.slice(startIndex, endIndex);
  };

  // Calculer le nombre total de pages
  const getTotalPages = () => {
    const filteredImages = getFilteredImages();
    return Math.ceil(filteredImages.length / itemsPerPage);
  };

  // Réinitialiser la page quand les filtres changent
  const resetPagination = () => {
    setCurrentPage(1);
  };

  // Réinitialiser la pagination quand les filtres ou l'onglet changent
  useEffect(() => {
    resetPagination();
  }, [searchTerm, filterStatus, filterCategory, activeTab]);

  // Gérer l'ajout/modification
  const handleSave = async (iconFile = null) => {
    setLoading(true);
    try {
      // Validation des champs requis
      if (!formData.name || !formData.description) {
        toast.error('Les champs nom et description sont requis');
        return;
      }

      // Valider le fichier icône si présent
      if (iconFile) {
        try {
          fileUtils.validateImageFile(iconFile);
        } catch (validationError) {
          toast.error(validationError.message);
          return;
        }
      }

      // Créer le FormData
      const submitData = fileUtils.createFormData(formData, iconFile);

      // Debug: Afficher les données envoyées
      console.log('🔍 Données à sauvegarder:', formData);
      console.log('🔍 FormData créé:', submitData);

      // Debug: Afficher le contenu du FormData
      for (let [key, value] of submitData.entries()) {
        console.log(`🔍 FormData[${key}]:`, value);
      }

      if (selectedImage) {
        // Modification
        const imageId = selectedImage.osId || selectedImage.appId;
        if (activeTab === 'os') {
          await osImagesService.update(imageId, submitData);
        } else {
          await appImagesService.update(imageId, submitData);
        }
        toast.success('Image modifiée avec succès');
      } else {
        // Création
        if (activeTab === 'os') {
          await osImagesService.create(submitData);
        } else {
          await appImagesService.create(submitData);
        }
        toast.success('Image ajoutée avec succès');
      }

      setShowAddModal(false);
      setShowEditModal(false);
      setSelectedImage(null);
      resetForm();
      loadImages();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast.error(error.message || 'Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  // Gérer la suppression
  const handleDelete = async (image) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette image ?')) return;

    setLoading(true);
    try {
      const imageId = image.osId || image.appId;

      if (image.osId) {
        await osImagesService.delete(imageId);
      } else {
        await appImagesService.delete(imageId);
      }

      toast.success('Image supprimée avec succès');
      loadImages();
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error(error.message || 'Erreur lors de la suppression');
    } finally {
      setLoading(false);
    }
  };

  // Changer le statut (publié/brouillon)
  const toggleStatus = async (image, currentStatus) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';
    setLoading(true);
    try {
      const imageId = image.osId || image.appId;

      if (image.osId) {
        await osImagesService.updateStatus(imageId, newStatus);
      } else {
        await appImagesService.updateStatus(imageId, newStatus);
      }

      toast.success(`Image ${newStatus === 'published' ? 'publiée' : 'mise en brouillon'}`);
      loadImages();
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
      toast.error(error.message || 'Erreur lors du changement de statut');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      version: '',
      status: 'draft',
      category: activeTab === 'os' ? 'os' : 'apps',
      isPopular: false,
      price: 'Inclus',
      iconType: 'custom',
      displayOrder: 0,
      type: 'control_panel',
      supportedOs: [],
      variants: [],
      dependencies: [],
      osType: 'Linux',
      isLTS: false,
      releaseDate: null
    });
  };

  const openAddModal = () => {
    resetForm();
    setFormData(prev => ({ ...prev, category: activeTab === 'os' ? 'os' : 'apps' }));
    setShowAddModal(true);
  };

  const openEditModal = (image) => {
    setSelectedImage(image);

    // Normaliser les données pour le nouveau système isPopular
    const normalizedData = {
      ...image,
      releaseDate: image.releaseDate ? new Date(image.releaseDate).toISOString().split('T')[0] : null,
      // Si l'ancienne catégorie était 'popular', la convertir
      category: image.category === 'popular' ? (activeTab === 'os' ? 'os' : 'apps') : image.category,
      // S'assurer que isPopular est défini
      isPopular: image.isPopular || image.category === 'popular'
    };

    setFormData(normalizedData);
    setShowEditModal(true);
  };

  // Obtenir l'icône pour le type d'image
  const getImageIcon = (image) => {
    // Si l'image a une icône personnalisée, l'utiliser
    if (image.iconUrl) {
      return (
        <img
          src={fileUtils.getIconUrl(image.iconUrl)}
          alt={image.name}
          className="h-5 w-5 object-cover rounded"
          onError={(e) => {
            // Fallback vers l'icône par défaut si l'image ne charge pas
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'block';
          }}
        />
      );
    }

    // Icônes par défaut
    const iconMap = {
      ubuntu: <Monitor className="h-5 w-5 text-orange-500" />,
      windows: <Monitor className="h-5 w-5 text-blue-500" />,
      debian: <Monitor className="h-5 w-5 text-red-500" />,
      centos: <Monitor className="h-5 w-5 text-purple-500" />,
      almalinux: <Monitor className="h-5 w-5 text-blue-600" />,
      rockylinux: <Monitor className="h-5 w-5 text-green-600" />,
      fedora: <Monitor className="h-5 w-5 text-blue-700" />,
      cpanel: <Settings className="h-5 w-5 text-blue-600" />,
      plesk: <Settings className="h-5 w-5 text-purple-600" />,
      blockchain: <Database className="h-5 w-5 text-yellow-600" />
    };

    return iconMap[image.iconType] || <HardDrive className="h-5 w-5 text-gray-500" />;
  };

  // Obtenir la couleur du badge de statut
  const getStatusColor = (status) => {
    return status === 'published' ? 'green' : 'orange';
  };

  // Obtenir la couleur du badge de catégorie
  const getCategoryColor = (category) => {
    const colors = {
      popular: 'purple',
      os: 'blue',
      apps: 'green',
      blockchain: 'yellow'
    };
    return colors[category] || 'gray';
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <Typography variant="h4" className="font-bold text-gray-900 flex items-center gap-2">
            <HardDrive className="h-8 w-8 text-blue-600" />
            Gestion des Images VPS
          </Typography>
          <Typography className="text-gray-600 mt-1">
            Gérez les images OS et applications pour la configuration VPS
          </Typography>
        </div>
        
        <div className="flex gap-3">
          <Button
            onClick={() => setShowPreview(true)}
            variant="outlined"
            className="flex items-center gap-2"
            size="lg"
          >
            <Eye className="h-5 w-5" />
            Prévisualiser
          </Button>
          <Button
            onClick={openAddModal}
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            size="lg"
          >
            <Plus className="h-5 w-5" />
            Ajouter une Image
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-blue-100">
          <div className="flex items-center justify-between">
            <div>
              <Typography className="text-sm text-blue-600 font-medium">Total Images</Typography>
              <Typography variant="h4" className="text-blue-900 font-bold">
                {osImages.length + appImages.length}
              </Typography>
            </div>
            <HardDrive className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-green-50 to-green-100">
          <div className="flex items-center justify-between">
            <div>
              <Typography className="text-sm text-green-600 font-medium">Images OS</Typography>
              <Typography variant="h4" className="text-green-900 font-bold">
                {osImages.length}
              </Typography>
            </div>
            <Monitor className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-purple-50 to-purple-100">
          <div className="flex items-center justify-between">
            <div>
              <Typography className="text-sm text-purple-600 font-medium">Applications</Typography>
              <Typography variant="h4" className="text-purple-900 font-bold">
                {appImages.length}
              </Typography>
            </div>
            <Package className="h-8 w-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-orange-50 to-orange-100">
          <div className="flex items-center justify-between">
            <div>
              <Typography className="text-sm text-orange-600 font-medium">Publiées</Typography>
              <Typography variant="h4" className="text-orange-900 font-bold">
                {[...osImages, ...appImages].filter(img => img.status === 'published').length}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Onglets et Filtres */}
      <Card className="p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          {/* Onglets */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('os')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                activeTab === 'os'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Monitor className="h-4 w-4 inline mr-2" />
              Images OS ({osImages.length})
            </button>
            <button
              onClick={() => setActiveTab('apps')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                activeTab === 'apps'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Package className="h-4 w-4 inline mr-2" />
              Applications ({appImages.length})
            </button>
          </div>

          {/* Filtres */}
          <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
            {/* Recherche */}
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Rechercher une image..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full sm:w-64"
              />
            </div>

            {/* Filtre par statut */}
            <div className="w-full sm:w-40">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Tous les statuts</option>
                <option value="published">Publiées</option>
                <option value="draft">Brouillons</option>
              </select>
            </div>

            {/* Filtre par catégorie */}
            <div className="w-full sm:w-40">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Toutes catégories</option>
                {categories[activeTab]?.map(cat => (
                  <option key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </Card>

      {/* Liste des Images */}
      <Card className="overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Chargement...</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Image
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Version
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Catégorie
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prix
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ordre
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {getPaginatedImages().map((image) => (
                  <motion.tr
                    key={image.osId || image.appId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-3">
                          {getImageIcon(image)}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {image.name}
                          </div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {image.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{image.version}</span>
                      {activeTab === 'os' && image.isLTS && (
                        <Badge color="blue" className="ml-2">LTS</Badge>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <Chip
                          color={getCategoryColor(image.category)}
                          value={image.category}
                          className="capitalize"
                        />
                        {image.isPopular && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Popular
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${
                        image.price === 'Inclus' ? 'text-green-600' : 'text-gray-900'
                      }`}>
                        {image.price}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <Chip
                          color={getStatusColor(image.status)}
                          value={image.status === 'published' ? 'Publiée' : 'Brouillon'}
                          className="capitalize"
                        />
                        <Switch
                          checked={image.status === 'published'}
                          onChange={() => toggleStatus(image, image.status)}
                          color="green"
                          disabled={loading}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{image.displayOrder}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="text"
                          color="blue"
                          onClick={() => openEditModal(image)}
                          className="p-2"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="text"
                          color="red"
                          onClick={() => handleDelete(image)}
                          className="p-2"
                          disabled={loading}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>

            {getFilteredImages().length === 0 && (
              <div className="text-center py-12">
                <HardDrive className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <Typography variant="h6" className="text-gray-500 mb-2">
                  Aucune image trouvée
                </Typography>
                <Typography className="text-gray-400">
                  {searchTerm || filterStatus !== 'all' || filterCategory !== 'all'
                    ? 'Essayez de modifier vos filtres'
                    : 'Commencez par ajouter votre première image'}
                </Typography>
              </div>
            )}

            {/* Pagination */}
            {getFilteredImages().length > 0 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  {/* Items per page selector */}
                  <div className="flex items-center gap-2">
                    <Typography className="text-sm text-gray-700">
                      Items per page:
                    </Typography>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(parseInt(e.target.value));
                        setCurrentPage(1);
                      }}
                      className="border border-gray-300 rounded px-3 py-1 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={15}>15</option>
                      <option value={20}>20</option>
                      <option value={50}>50</option>
                    </select>
                  </div>

                  {/* Page info */}
                  <div className="flex items-center gap-4">
                    <Typography className="text-sm text-gray-700">
                      Page {currentPage} of {getTotalPages()} ({getFilteredImages().length} items)
                    </Typography>

                    {/* Navigation buttons */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outlined"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="border-gray-300 text-gray-600 hover:bg-gray-50"
                      >
                        PREVIOUS
                      </Button>
                      <Button
                        variant="outlined"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(getTotalPages(), currentPage + 1))}
                        disabled={currentPage === getTotalPages()}
                        className="border-gray-300 text-gray-600 hover:bg-gray-50"
                      >
                        NEXT
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Modale d'ajout/modification */}
      <ImageModal
        isOpen={showAddModal || showEditModal}
        onClose={() => {
          setShowAddModal(false);
          setShowEditModal(false);
          setSelectedImage(null);
          resetForm();
        }}
        onSave={handleSave}
        formData={formData}
        setFormData={setFormData}
        activeTab={activeTab}
        loading={loading}
        selectedImage={selectedImage}
        availableOsImages={osImages}
      />

      {/* Modale de prévisualisation */}
      <PreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        osImages={osImages}
        appImages={appImages}
      />
    </div>
  );
}
