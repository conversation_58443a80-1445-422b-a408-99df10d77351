/**
 * Test avec l'ID réel de l'instance VPS
 * Instance ID: 202718127 (CLOUD VPS 10-1753106023138)
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5002';
const REAL_INSTANCE_ID = '202718127'; // ID réel de votre instance

async function testWithRealInstance() {
  console.log('🚀 Test avec l\'ID réel de l\'instance VPS\n');
  console.log(`📋 Instance ID: ${REAL_INSTANCE_ID}`);
  console.log('📋 Instance Name: CLOUD VPS 10-1753106023138\n');

  try {
    // Données de réinstallation avec une image Ubuntu valide
    const reinstallData = {
      imageId: 'd64d5c6c-9dda-4e38-8174-0ee282474d8a', // Ubuntu 24.04 (ID réel de <PERSON>bo)
      password: 'SecurePassword123!',
      enableRootUser: true
    };

    console.log('📤 Données de réinstallation:');
    console.log(JSON.stringify(reinstallData, null, 2));
    console.log('');

    console.log('⚠️  ATTENTION: Ce test va effectuer une VRAIE réinstallation !');
    console.log('⚠️  L\'instance sera réinstallée avec Ubuntu 24.04');
    console.log('⚠️  Toutes les données actuelles seront perdues !');
    console.log('');

    // Demander confirmation (simulée)
    console.log('🔄 Envoi de la requête de réinstallation...');

    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/${REAL_INSTANCE_ID}/reinstall`,
      reinstallData,
      {
        headers: { 
          'Content-Type': 'application/json',
          'x-request-id': generateRequestId()
        },
        validateStatus: () => true // Accepter tous les codes de statut
      }
    );

    console.log('📋 Réponse du serveur:');
    console.log(`   Status: ${response.status}`);
    console.log(`   Message: ${response.data.message || 'Pas de message'}`);
    
    if (response.data.data) {
      console.log('   Données:', JSON.stringify(response.data.data, null, 2));
    }

    if (response.status === 200) {
      console.log('✅ Réinstallation initiée avec succès !');
      console.log('');
      console.log('📝 Prochaines étapes:');
      console.log('   1. La réinstallation peut prendre 5-15 minutes');
      console.log('   2. L\'instance sera redémarrée automatiquement');
      console.log('   3. Utilisez le nouveau mot de passe pour vous connecter');
      console.log('   4. Vérifiez le statut dans le panel Contabo');
    } else {
      console.log('❌ Erreur lors de la réinstallation');
      if (response.data.errors) {
        console.log('   Erreurs de validation:', response.data.errors);
      }
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    if (error.response) {
      console.error('📋 Détails:', error.response.data);
    }
  }
}

// Test de validation seulement (sans réinstallation)
async function testValidationOnly() {
  console.log('\n' + '='.repeat(50));
  console.log('🧪 Test de validation seulement (pas de réinstallation)\n');

  try {
    // Test avec des données invalides pour vérifier la validation
    const invalidData = {
      // imageId manquant intentionnellement
      password: 'short', // Mot de passe trop court
    };

    console.log('📤 Test avec données invalides...');
    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/${REAL_INSTANCE_ID}/reinstall`,
      invalidData,
      {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      }
    );

    console.log(`📋 Status: ${response.status}`);
    if (response.status === 400) {
      console.log('✅ Validation fonctionne correctement');
      console.log('📋 Erreurs:', response.data.errors);
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

function generateRequestId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Exécuter les tests
if (require.main === module) {
  console.log('🚨 AVERTISSEMENT IMPORTANT 🚨');
  console.log('Ce script va effectuer une VRAIE réinstallation sur votre VPS !');
  console.log('Toutes les données actuelles seront perdues !');
  console.log('');
  console.log('Pour continuer, décommentez la ligne testWithRealInstance() ci-dessous');
  console.log('');

  // DÉCOMMENTEZ CETTE LIGNE POUR EFFECTUER LA VRAIE RÉINSTALLATION
  // testWithRealInstance();

  // Test de validation seulement (sécurisé)
  testValidationOnly();
}

module.exports = { testWithRealInstance, testValidationOnly };
