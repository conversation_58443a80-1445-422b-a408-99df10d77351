const ContaboProvider = require('./providers/ContaboProvider');

class CustomImageService {
  constructor() {
    this.contaboProvider = new ContaboProvider();
  }

  /**
   * Create a custom image
   * @param {Object} imageData - Custom image data
   * @returns {Promise<Object>} Created image details
   */
  async createCustomImage(imageData) {
    try {
      console.log('🖼️ Creating custom image:', imageData);

      // Validate required fields
      if (!imageData.url) {
        throw new Error('Image URL is required');
      }
      if (!imageData.name) {
        throw new Error('Image name is required');
      }
      if (!imageData.version) {
        throw new Error('Image version is required');
      }

      // Check if an image with this name already exists
      let uniqueName = imageData.name;
      try {
        const existingImages = await this.getCustomImages();
        const existingNames = existingImages.data.map(img => img.name);

        // If name exists, generate a unique one
        if (existingNames.includes(uniqueName)) {
          const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '-');
          uniqueName = `${imageData.name}-${timestamp}`;
          console.log(`⚠️ Image name '${imageData.name}' already exists, using unique name: '${uniqueName}'`);
        }
      } catch (error) {
        console.log('⚠️ Could not check existing images, proceeding with original name');
      }

      // Prepare custom image payload for Contabo API
      const customImagePayload = {
        name: uniqueName,
        description: imageData.description || '',
        url: imageData.url,
        osType: imageData.osType || 'Linux',
        version: imageData.version
      };

      console.log('📤 Sending custom image creation request:', customImagePayload);

      // Call Contabo API to create custom image
      const response = await this.contaboProvider.makeRequest(
        'POST',
        '/compute/images',
        customImagePayload
      );

      console.log('✅ Custom image created successfully:', response);

      const responseData = response.data.data?.[0] || response.data;
      const imageId = responseData.imageId || responseData.id;

      return {
        success: true,
        data: {
          ...responseData,
          id: imageId,
          imageId: imageId
        },
        message: 'Custom image created successfully'
      };

    } catch (error) {
      console.error('❌ Failed to create custom image:', error.message);
      throw new Error(`Failed to create custom image: ${error.message}`);
    }
  }

  /**
   * Get all custom images
   * @returns {Promise<Array>} List of custom images
   */
  async getCustomImages() {
    try {
      console.log('📋 Fetching custom images...');

      // Get all images and filter custom ones
      const response = await this.contaboProvider.makeRequest('GET', '/compute/images');
      const allImages = response.data || [];

      // Filter custom images (those created by user)
      const customImages = allImages.filter(img => {
        // Custom images typically have different properties or naming conventions
        // This depends on how Contabo API distinguishes custom images
        return img.custom === true || img.type === 'custom' || img.source === 'user';
      });

      console.log(`✅ Found ${customImages.length} custom images`);

      return {
        success: true,
        data: customImages,
        message: `Retrieved ${customImages.length} custom images`
      };

    } catch (error) {
      console.error('❌ Failed to fetch custom images:', error.message);
      throw new Error(`Failed to fetch custom images: ${error.message}`);
    }
  }

  /**
   * Delete a custom image
   * @param {string} imageId - Image ID to delete
   * @returns {Promise<Object>} Deletion result
   */
  async deleteCustomImage(imageId) {
    try {
      console.log('🗑️ Deleting custom image:', imageId);

      const response = await this.contaboProvider.makeRequest(
        'DELETE',
        `/compute/images/${imageId}`
      );

      console.log('✅ Custom image deleted successfully');

      return {
        success: true,
        message: 'Custom image deleted successfully'
      };

    } catch (error) {
      console.error('❌ Failed to delete custom image:', error.message);
      throw new Error(`Failed to delete custom image: ${error.message}`);
    }
  }

  /**
   * Update a custom image
   * @param {string} imageId - Image ID to update
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Update result
   */
  async updateCustomImage(imageId, updateData) {
    try {
      console.log('✏️ Updating custom image:', imageId, updateData);

      const response = await this.contaboProvider.makeRequest(
        'PUT',
        `/compute/images/${imageId}`,
        updateData
      );

      console.log('✅ Custom image updated successfully');

      return {
        success: true,
        data: response.data,
        message: 'Custom image updated successfully'
      };

    } catch (error) {
      console.error('❌ Failed to update custom image:', error.message);
      throw new Error(`Failed to update custom image: ${error.message}`);
    }
  }

  /**
   * Get custom image details
   * @param {string} imageId - Image ID
   * @returns {Promise<Object>} Image details
   */
  async getCustomImageDetails(imageId) {
    try {
      console.log('🔍 Getting custom image details:', imageId);

      const response = await this.contaboProvider.makeRequest(
        'GET',
        `/compute/images/${imageId}`
      );

      console.log('✅ Custom image details retrieved');

      return {
        success: true,
        data: response.data,
        message: 'Custom image details retrieved successfully'
      };

    } catch (error) {
      console.error('❌ Failed to get custom image details:', error.message);
      throw new Error(`Failed to get custom image details: ${error.message}`);
    }
  }
}

module.exports = CustomImageService;
