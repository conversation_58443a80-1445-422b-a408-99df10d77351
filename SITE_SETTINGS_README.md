# Site Settings Implementation

This document describes the complete site settings functionality that has been implemented for the admin panel.

## Overview

The site settings system allows administrators to manage website configuration through a user-friendly interface. It includes general settings (site information, contact details, social links) and SEO settings (meta tags, analytics, verification codes).

## Backend Implementation

### 1. Database Model (`backend/models/SiteSettings.js`)

- **MongoDB Schema**: Stores general and SEO settings
- **Validation**: Email validation, required fields, URL validation
- **Static Methods**: `getSettings()` method that creates default settings if none exist
- **Single Document**: Ensures only one settings document exists

**Key Features:**
- Automatic default settings creation
- Comprehensive validation
- Timestamps and audit trail (updatedBy field)

### 2. Controller (`backend/controllers/admin/siteSettingsController.js`)

**Available Endpoints:**
- `GET /admin/site-settings` - Get all settings
- `PUT /admin/site-settings` - Update all settings
- `GET /admin/site-settings/:section` - Get specific section (general/seo)
- `PUT /admin/site-settings/:section` - Update specific section
- `POST /admin/site-settings/reset` - Reset to default values

**Features:**
- Error handling with detailed validation messages
- User tracking (who made changes)
- Section-specific updates for better performance

### 3. Validation Middleware (`backend/middlewares/requests/siteSettingsRequest.js`)

**Validation Rules:**
- **General Settings**: Site name (required), email validation, URL validation for social links
- **SEO Settings**: Title length (60 chars), description length (160 chars), Google Analytics ID format
- **Security**: Strips unknown fields, prevents injection

### 4. Routes (`backend/routes/adminRouter.js`)

All routes are protected with:
- `adminMiddleware` - Ensures only admins can access
- `asyncBackFrontEndLang` - Language support
- Appropriate validation middleware

## Frontend Implementation

### 1. Service Layer (`frontend/src/app/services/siteSettingsService.js`)

**API Methods:**
- `getSiteSettings()` - Fetch all settings
- `updateSiteSettings(data)` - Update all settings
- `getSiteSettingsSection(section)` - Fetch specific section
- `updateSiteSettingsSection(section, data)` - Update specific section
- `resetSiteSettings()` - Reset to defaults

### 2. Admin Interface (`frontend/src/app/admin/settings/page.jsx`)

**Features:**
- **Two-tab Interface**: General and SEO settings
- **Real-time Validation**: Form validation with error messages
- **Success/Error Feedback**: User-friendly notifications
- **Auto-save**: Saves all settings at once
- **Responsive Design**: Works on all screen sizes

**Removed Features** (as requested):
- Appearance tab (colors, fonts)
- Features tab (blog, comments, maintenance mode)

**Added Features:**
- Favicon input in SEO section
- Comprehensive SEO meta tag management

### 3. SEO Head Component (`frontend/src/app/components/SEOHead.jsx`)

**Purpose**: Demonstrates how to use site settings for SEO
**Features:**
- Dynamic meta tag generation
- Google Analytics integration
- Open Graph and Twitter Card support
- Search engine verification codes
- Favicon management

## Settings Structure

```javascript
{
  general: {
    siteName: "ZtechEngineering",
    siteDescription: "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success",
    contactEmail: "<EMAIL>",
    phone: "+1234567890",
    address: "123 Main St, City, State",
    socialLinks: {
      linkedin: "https://linkedin.com/company/example",
      twitter: "https://twitter.com/example",
      facebook: "https://facebook.com/example"
    }
  },
  seo: {
    defaultTitle: "Site Title",
    defaultDescription: "Site description for SEO",
    defaultKeywords: "keyword1, keyword2, keyword3",
    favicon: "https://example.com/favicon.ico",
    googleAnalyticsId: "G-XXXXXXXXXX",
    googleSiteVerification: "verification-code",
    bingVerification: "verification-code",
    robotsTxt: "User-agent: *\nAllow: /",
    sitemapEnabled: true
  }
}
```

## Installation & Setup

### 1. Backend Setup

The backend files are already integrated into the existing Express.js application:

```bash
# No additional dependencies needed
# Routes are automatically loaded through adminRouter.js
```

### 2. Frontend Setup

The frontend components integrate with the existing Next.js application:

```bash
# No additional dependencies needed
# Service is available through existing API service layer
```

### 3. Database

The MongoDB model will automatically create the collection and default settings on first use.

## Testing

### Backend Testing

Run the test script to verify backend functionality:

```bash
cd backend
node test-site-settings.js
```

### Frontend Testing

1. Navigate to `/admin/settings` in your admin panel
2. Try updating general settings (site name, contact info)
3. Try updating SEO settings (meta tags, analytics ID)
4. Verify validation by entering invalid data
5. Check that settings persist after page refresh

## Usage Examples

### Using Settings in Components

```jsx
import siteSettingsService from '../services/siteSettingsService';

function MyComponent() {
  const [settings, setSettings] = useState(null);
  
  useEffect(() => {
    const fetchSettings = async () => {
      const response = await siteSettingsService.getSiteSettings();
      setSettings(response.data);
    };
    fetchSettings();
  }, []);
  
  return (
    <div>
      <h1>{settings?.general.siteName}</h1>
      <p>{settings?.general.siteDescription}</p>
    </div>
  );
}
```

### Using SEO Head Component

```jsx
import SEOHead from '../components/SEOHead';

function MyPage() {
  return (
    <>
      <SEOHead 
        title="Custom Page Title"
        description="Custom page description"
        keywords="custom, keywords"
      />
      <div>Page content...</div>
    </>
  );
}
```

## Security Considerations

1. **Admin-only Access**: All endpoints require admin authentication
2. **Input Validation**: Comprehensive validation prevents malicious input
3. **XSS Prevention**: HTML content is properly escaped
4. **CSRF Protection**: Uses existing CSRF protection middleware

## Future Enhancements

1. **File Upload**: Add image upload for logos and favicons
2. **Theme Management**: Restore appearance settings with proper file handling
3. **Multi-language**: Support for multiple language versions of settings
4. **Backup/Restore**: Export/import settings functionality
5. **Version History**: Track changes over time

## Troubleshooting

### Common Issues

1. **Settings not loading**: Check admin authentication and database connection
2. **Validation errors**: Ensure all required fields are filled correctly
3. **SEO not working**: Verify the SEOHead component is properly imported
4. **Database errors**: Check MongoDB connection and permissions

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG=site-settings node server.js
```
