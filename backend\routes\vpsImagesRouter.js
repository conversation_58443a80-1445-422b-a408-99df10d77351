const express = require('express');
const VpsOS = require('../models/VpsOS');
const VpsApp = require('../models/VpsApp');

const router = express.Router();

/**
 * @route GET /api/vps/images
 * @desc Récupérer toutes les images publiées pour la configuration VPS
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    const { category, type, osType } = req.query;

    // Récupérer les images OS publiées
    let osFilter = { status: 'published' };
    if (category && category !== 'all') {
      osFilter.category = category;
    }
    if (osType && osType !== 'all') {
      osFilter.osType = osType;
    }

    // Récupérer les applications publiées
    let appFilter = { status: 'published' };
    if (category && category !== 'all') {
      appFilter.category = category;
    }
    if (type && type !== 'all') {
      appFilter.type = type;
    }

    const [osImages, appImages] = await Promise.all([
      VpsOS.find(osFilter)
        .sort({ displayOrder: 1, name: 1 })
        .select('-createdAt -updatedAt'),
      VpsApp.find(appFilter)
        .sort({ displayOrder: 1, name: 1 })
        .select('-createdAt -updatedAt')
    ]);

    // Ajouter les URLs des icônes
    const osImagesWithIcons = osImages.map(image => ({
      ...image.toObject(),
      iconUrl: image.customIcon ? `/uploads/os-icons/${image.customIcon}` : null
    }));

    const appImagesWithIcons = appImages.map(image => ({
      ...image.toObject(),
      iconUrl: image.customIcon ? `/uploads/app-icons/${image.customIcon}` : null
    }));

    // Organiser par catégories comme Contabo
    const organizedImages = {
      popular: [
        ...osImagesWithIcons.filter(img => img.isPopular === true),
        ...appImagesWithIcons.filter(img => img.isPopular === true)
      ],
      os: osImagesWithIcons.filter(img => img.category === 'os'),
      apps: appImagesWithIcons.filter(img => img.category === 'apps'),
      blockchain: appImagesWithIcons.filter(img => img.category === 'blockchain')
    };

    res.json({
      success: true,
      data: {
        organized: organizedImages,
        all: {
          os: osImagesWithIcons,
          apps: appImagesWithIcons
        },
        counts: {
          popular: organizedImages.popular.length,
          os: organizedImages.os.length,
          apps: organizedImages.apps.length,
          blockchain: organizedImages.blockchain.length,
          total: osImagesWithIcons.length + appImagesWithIcons.length
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des images VPS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des images VPS'
    });
  }
});

/**
 * @route GET /api/vps/images/popular
 * @desc Récupérer les images populaires
 * @access Public
 */
router.get('/popular', async (req, res) => {
  try {
    const [osImages, appImages] = await Promise.all([
      VpsOS.find({ status: 'published', isPopular: true })
        .sort({ displayOrder: 1, name: 1 })
        .select('-createdAt -updatedAt'),
      VpsApp.find({ status: 'published', isPopular: true })
        .sort({ displayOrder: 1, name: 1 })
        .select('-createdAt -updatedAt')
    ]);

    const popularImages = [
      ...osImages.map(image => ({
        ...image.toObject(),
        type: 'os',
        iconUrl: image.customIcon ? `/uploads/os-icons/${image.customIcon}` : null
      })),
      ...appImages.map(image => ({
        ...image.toObject(),
        type: 'app',
        iconUrl: image.customIcon ? `/uploads/app-icons/${image.customIcon}` : null
      }))
    ].sort((a, b) => a.displayOrder - b.displayOrder);

    res.json({
      success: true,
      data: popularImages
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des images populaires:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des images populaires'
    });
  }
});

/**
 * @route GET /api/vps/images/os
 * @desc Récupérer les images OS
 * @access Public
 */
router.get('/os', async (req, res) => {
  try {
    const { osType, category } = req.query;
    
    let filter = { status: 'published' };
    if (osType && osType !== 'all') {
      filter.osType = osType;
    }
    if (category && category !== 'all') {
      filter.category = category;
    } else {
      // Par défaut, exclure les populaires pour éviter les doublons
      filter.category = { $ne: 'popular' };
    }

    const osImages = await VpsOS.find(filter)
      .sort({ displayOrder: 1, name: 1 })
      .select('-createdAt -updatedAt');

    const osImagesWithIcons = osImages.map(image => ({
      ...image.toObject(),
      iconUrl: image.customIcon ? `/uploads/os-icons/${image.customIcon}` : null
    }));

    res.json({
      success: true,
      data: osImagesWithIcons
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des images OS:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des images OS'
    });
  }
});

/**
 * @route GET /api/vps/images/apps
 * @desc Récupérer les applications
 * @access Public
 */
router.get('/apps', async (req, res) => {
  try {
    const { type, category } = req.query;
    
    let filter = { status: 'published' };
    if (type && type !== 'all') {
      filter.type = type;
    }
    if (category && category !== 'all') {
      filter.category = category;
    } else {
      // Par défaut, exclure les populaires pour éviter les doublons
      filter.category = { $ne: 'popular' };
    }

    const appImages = await VpsApp.find(filter)
      .sort({ displayOrder: 1, name: 1 })
      .select('-createdAt -updatedAt');

    // Enrichir avec les détails des OS supportés
    const appImagesWithDetails = await Promise.all(
      appImages.map(async (app) => {
        const appObj = app.toObject();
        appObj.iconUrl = app.customIcon ? `/uploads/app-icons/${app.customIcon}` : null;
        
        // Ajouter les détails des OS supportés
        if (app.supportedOs && app.supportedOs.length > 0) {
          const supportedOsDetails = await VpsOS.find({ 
            osId: { $in: app.supportedOs },
            status: 'published'
          }).select('osId name version description');
          
          appObj.supportedOsDetails = supportedOsDetails;
        }
        
        return appObj;
      })
    );

    res.json({
      success: true,
      data: appImagesWithDetails
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des applications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des applications'
    });
  }
});

/**
 * @route GET /api/vps/images/blockchain
 * @desc Récupérer les applications blockchain
 * @access Public
 */
router.get('/blockchain', async (req, res) => {
  try {
    const blockchainApps = await VpsApp.find({ 
      status: 'published', 
      category: 'blockchain' 
    })
      .sort({ displayOrder: 1, name: 1 })
      .select('-createdAt -updatedAt');

    const blockchainAppsWithDetails = await Promise.all(
      blockchainApps.map(async (app) => {
        const appObj = app.toObject();
        appObj.iconUrl = app.customIcon ? `/uploads/app-icons/${app.customIcon}` : null;
        
        // Ajouter les détails des OS supportés
        if (app.supportedOs && app.supportedOs.length > 0) {
          const supportedOsDetails = await VpsOS.find({ 
            osId: { $in: app.supportedOs },
            status: 'published'
          }).select('osId name version description');
          
          appObj.supportedOsDetails = supportedOsDetails;
        }
        
        return appObj;
      })
    );

    res.json({
      success: true,
      data: blockchainAppsWithDetails
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des applications blockchain:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des applications blockchain'
    });
  }
});

/**
 * @route GET /api/vps/images/:id
 * @desc Récupérer une image spécifique (OS ou App)
 * @access Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Chercher d'abord dans les OS
    let image = await VpsOS.findOne({ 
      osId: id, 
      status: 'published' 
    }).select('-createdAt -updatedAt');
    
    let imageType = 'os';
    
    // Si pas trouvé dans les OS, chercher dans les apps
    if (!image) {
      image = await VpsApp.findOne({ 
        appId: id, 
        status: 'published' 
      }).select('-createdAt -updatedAt');
      imageType = 'app';
    }
    
    if (!image) {
      return res.status(404).json({
        success: false,
        message: 'Image non trouvée'
      });
    }

    const imageObj = image.toObject();
    imageObj.type = imageType;
    
    // Ajouter l'URL de l'icône
    if (imageType === 'os') {
      imageObj.iconUrl = image.customIcon ? `/uploads/os-icons/${image.customIcon}` : null;
    } else {
      imageObj.iconUrl = image.customIcon ? `/uploads/app-icons/${image.customIcon}` : null;
      
      // Pour les apps, ajouter les détails des OS supportés
      if (image.supportedOs && image.supportedOs.length > 0) {
        const supportedOsDetails = await VpsOS.find({ 
          osId: { $in: image.supportedOs },
          status: 'published'
        }).select('osId name version description');
        
        imageObj.supportedOsDetails = supportedOsDetails;
      }
    }

    res.json({
      success: true,
      data: imageObj
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'image:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'image'
    });
  }
});

module.exports = router;
