'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogHeader, DialogBody, DialogFooter, Button, Input, Textarea, Switch, Typography } from "@material-tailwind/react";
import { X, Save, MapPin, DollarSign } from 'lucide-react';

export default function RegionModal({
  isOpen,
  onClose,
  onSave,
  formData,
  setFormData,
  loading,
  selectedRegion,
  vpsPackages = [],
  handleInputChange,
  handlePricingChange
}) {
  const [activeTab, setActiveTab] = useState('basic');

  const continents = ['Europe', 'North America', 'South America', 'Asia', 'Africa', 'Oceania'];
  const statuses = ['active', 'inactive', 'maintenance'];
  const availableServices = ['vps', 'dedicated', 'storage', 'cdn'];

  const getPriceForPackage = (packageId) => {
    const pricing = formData.pricing.find(p => p.packageId === packageId);
    return pricing ? pricing.additionalPrice : 0;
  };

  return (
    <Dialog open={isOpen} handler={onClose} size="xl" className="max-h-[90vh] overflow-y-auto">
      <DialogHeader className="flex items-center justify-between">
        <Typography variant="h5" className="font-bold">
          {selectedRegion ? 'Modifier la région' : 'Ajouter une région'}
        </Typography>
        <Button variant="text" color="gray" onClick={onClose} className="p-2">
          <X className="h-5 w-5" />
        </Button>
      </DialogHeader>

      <DialogBody className="px-6 py-4">
        {/* Onglets */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('basic')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'basic'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Informations de base
          </button>
          <button
            onClick={() => setActiveTab('pricing')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'pricing'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Prix par package
          </button>
          <button
            onClick={() => setActiveTab('technical')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'technical'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Détails techniques
          </button>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'basic' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Region ID */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Region ID *
                </Typography>
                <Input
                  value={formData.regionId}
                  onChange={(e) => handleInputChange('regionId', e.target.value)}
                  placeholder="ex: france, germany, usa-east"
                  required
                />
              </div>

              {/* Nom */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Nom *
                </Typography>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="ex: France, Germany, USA East"
                  required
                />
              </div>

              {/* Pays */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Pays *
                </Typography>
                <Input
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  placeholder="ex: France, Germany, United States"
                  required
                />
              </div>

              {/* Code pays */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Code pays *
                </Typography>
                <Input
                  value={formData.countryCode}
                  onChange={(e) => handleInputChange('countryCode', e.target.value)}
                  placeholder="ex: FR, DE, US"
                  maxLength={3}
                  required
                />
              </div>

              {/* Ville */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Ville *
                </Typography>
                <Input
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="ex: Paris, Berlin, New York"
                  required
                />
              </div>

              {/* Continent */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Continent *
                </Typography>
                <select
                  value={formData.continent}
                  onChange={(e) => handleInputChange('continent', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  {continents.map(continent => (
                    <option key={continent} value={continent}>{continent}</option>
                  ))}
                </select>
              </div>

              {/* Flag */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Drapeau (Emoji) *
                </Typography>
                <Input
                  value={formData.flag}
                  onChange={(e) => handleInputChange('flag', e.target.value)}
                  placeholder="🇫🇷"
                  maxLength={4}
                  required
                />
              </div>

              {/* Statut */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Statut
                </Typography>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>
                      {status === 'active' ? 'Active' : status === 'inactive' ? 'Inactive' : 'Maintenance'}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <Typography variant="small" className="font-medium mb-2">
                Description
              </Typography>
              <Textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Description de la région..."
                rows={3}
              />
            </div>

            {/* Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Switch
                  checked={formData.isPopular}
                  onChange={(e) => handleInputChange('isPopular', e.target.checked)}
                />
                <Typography variant="small" className="font-medium">
                  Région populaire
                </Typography>
              </div>

              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Ordre d'affichage
                </Typography>
                <Input
                  type="number"
                  value={formData.displayOrder}
                  onChange={(e) => handleInputChange('displayOrder', parseInt(e.target.value) || 0)}
                  min="0"
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'pricing' && (
          <div className="space-y-4">
            <Typography variant="h6" className="font-medium mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Prix supplémentaires par package VPS
            </Typography>
            
            {vpsPackages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Aucun package VPS disponible
              </div>
            ) : (
              <div className="space-y-3">
                {vpsPackages.map(pkg => (
                  <div key={pkg._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <Typography className="font-medium">{pkg.name}</Typography>
                      <Typography variant="small" className="text-gray-600">
                        Prix de base: {pkg.price} MAD/mois
                      </Typography>
                    </div>
                    <div className="flex items-center gap-2">
                      <Typography variant="small" className="text-gray-600">+</Typography>
                      <Input
                        type="number"
                        value={getPriceForPackage(pkg._id)}
                        onChange={(e) => handlePricingChange(pkg._id, e.target.value)}
                        placeholder="0"
                        min="0"
                        step="0.01"
                        className="w-24"
                      />
                      <Typography variant="small" className="text-gray-600">MAD/mois</Typography>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'technical' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Fournisseur datacenter */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Fournisseur datacenter
                </Typography>
                <Input
                  value={formData.datacenterProvider}
                  onChange={(e) => handleInputChange('datacenterProvider', e.target.value)}
                  placeholder="ex: OVH, AWS, Google Cloud"
                />
              </div>

              {/* Vitesse réseau */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Vitesse réseau
                </Typography>
                <Input
                  value={formData.networkSpeed}
                  onChange={(e) => handleInputChange('networkSpeed', e.target.value)}
                  placeholder="ex: 1 Gbps, 10 Gbps"
                />
              </div>

              {/* Uptime */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Uptime garanti
                </Typography>
                <Input
                  value={formData.uptime}
                  onChange={(e) => handleInputChange('uptime', e.target.value)}
                  placeholder="ex: 99.9%, 99.95%"
                />
              </div>
            </div>

            {/* Coordonnées */}
            <div>
              <Typography variant="small" className="font-medium mb-2 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Coordonnées géographiques (optionnel)
              </Typography>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  value={formData.coordinates.latitude}
                  onChange={(e) => handleInputChange('coordinates.latitude', e.target.value)}
                  placeholder="Latitude (ex: 48.8566)"
                  step="any"
                />
                <Input
                  type="number"
                  value={formData.coordinates.longitude}
                  onChange={(e) => handleInputChange('coordinates.longitude', e.target.value)}
                  placeholder="Longitude (ex: 2.3522)"
                  step="any"
                />
              </div>
            </div>

            {/* Services disponibles */}
            <div>
              <Typography variant="small" className="font-medium mb-2">
                Services disponibles
              </Typography>
              <div className="flex flex-wrap gap-3">
                {availableServices.map(service => (
                  <label key={service} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.availableServices.includes(service)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handleInputChange('availableServices', [...formData.availableServices, service]);
                        } else {
                          handleInputChange('availableServices', formData.availableServices.filter(s => s !== service));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Typography variant="small" className="capitalize">
                      {service}
                    </Typography>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}
      </DialogBody>

      <DialogFooter className="flex gap-2">
        <Button variant="text" color="gray" onClick={onClose}>
          Annuler
        </Button>
        <Button
          onClick={onSave}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Save className="h-4 w-4" />
          )}
          {selectedRegion ? 'Modifier' : 'Ajouter'}
        </Button>
      </DialogFooter>
    </Dialog>
  );
}
