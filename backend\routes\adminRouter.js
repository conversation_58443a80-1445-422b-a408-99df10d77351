const express = require("express");
const {
  getAllUsers,
  addUser,
  deleteUser,
  modifyUser,
  getAllAdmins,
} = require("../controllers/admin/userController");
const {
  getAllPackages,
  addPackage,
  updatePackage,
  deletePackage,
  configureVPSPackage,
  addSpec,
  getAllSpecs,
  updateSpec,
  deeplTranslate,
  getAllCategories,
} = require("../controllers/admin/packController");
const adminRouter = express.Router();
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");
const { adminMiddleware } = require("../midelwares/authorization");
const { registerValidator } = require("../midelwares/requests/authRequest");
const {
  addUserValidator,
  updateUserValidator,
} = require("../midelwares/admin/userValidator");

// Import site settings validation middleware
const {
  validateSiteSettings,
  validateGeneralSettings,
  validateSeoSettings
} = require("../midelwares/requests/siteSettingsRequest");
const {
  getAllOrders,
  updateOrderStatus,
  getOrderById,
  updateSubOrderStatus,
} = require("../controllers/admin/orderController");
const {
  getDashboardStats,
  getPackageDistributionByCategory,
  getSSLCertificateStats,
} = require("../controllers/admin/dashboardController");
const {
  getAdminActivityLogs,
  getActivityLogFilters,
} = require("../controllers/admin/logController");
const {
  getAllConversations,
  getConversationById,
  deleteConversation,
  deleteMultipleConversations,
  deleteAllConversations,
  getUserConversations,
} = require("../controllers/admin/chatController");

const {
  getContext,
  updateContext,
} = require("../controllers/admin/chatbotContextController");
const {
  getAdminNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
} = require("../controllers/admin/notificationController");

// Import abandoned cart service
const { processAbandonedCarts } = require("../services/abandonedCartService");

// Import notification settings controller
const {
  getNotificationSettings,
  getNotificationSettingByType,
  updateNotificationSetting,
  triggerNotification
} = require("../controllers/admin/notificationSettingsController");

// Import custom notification controller
const {
  getCustomNotifications,
  createCustomNotification,
  updateCustomNotification,
  deleteCustomNotification,
  cancelCustomNotification
} = require("../controllers/admin/customNotificationController");

// Import site settings controller
const {
  getSiteSettings,
  updateSiteSettings,
  resetSiteSettings,
  getSiteSettingsSection,
  updateSiteSettingsSection
} = require("../controllers/admin/siteSettingsController");
const {
  createJob,
  getAllJobs,
  getJobById,
  updateJob,
  deleteJob,
  getJobApplications,
  getAllJobApplications,
  updateApplicationStatus,
  deleteApplication
} = require("../controllers/admin/jobController");

// GET ALL ADMINS
adminRouter.get("/admins", asyncBackFrontEndLang, adminMiddleware, getAllAdmins);

// Get All Users
adminRouter.get("/users", asyncBackFrontEndLang, adminMiddleware, getAllUsers);

// Add a New User
adminRouter.post(
  "/users",
  asyncBackFrontEndLang,
  adminMiddleware,
  addUserValidator,
  addUser
);

// Delete a User
adminRouter.delete(
  "/users/:userId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteUser
);

// Modify a User
adminRouter.put(
  "/users/:userId",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateUserValidator,
  modifyUser
);

// Get All Packages
adminRouter.get(
  "/packages",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllPackages
);

// Add a New Package
adminRouter.post(
  "/packages",
  asyncBackFrontEndLang,
  adminMiddleware,
  addPackage
);

// Update a Package
adminRouter.put(
  "/packages/:packageId",
  asyncBackFrontEndLang,
  adminMiddleware,
  updatePackage
);

// Delete a Package
adminRouter.delete(
  "/packages/:packageId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deletePackage
);

// Configure VPS Package with Provider Details
adminRouter.put(
  "/packages/:packageId/vps-config",
  asyncBackFrontEndLang,
  adminMiddleware,
  configureVPSPackage
);

// Add a New Spec
adminRouter.post("/specs", asyncBackFrontEndLang, adminMiddleware, addSpec);

// Get All Specs
adminRouter.get("/specs", asyncBackFrontEndLang, adminMiddleware, getAllSpecs);

adminRouter.put("/update-spec", adminMiddleware, updateSpec);

adminRouter.put("/update-spec", adminMiddleware, updateSpec);

adminRouter.get(
  "/categories",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllCategories
);

// GET ALL ORDERS WITH PAGINATION
adminRouter.get(
  "/orders",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllOrders
);

// UPDATE ORDER STATUS
adminRouter.put(
  "/orders/:orderId/status",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateOrderStatus
);

// UPDATE SUBORDER STATUS
adminRouter.put(
  "/orders/:orderId/:suborderId/status",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateSubOrderStatus
);

// GET A SPECIFIC ORDER
adminRouter.get(
  "/orders/:orderId",
  asyncBackFrontEndLang,
  adminMiddleware,
  getOrderById
);

// Dashboard Stats
adminRouter.get(
  "/dashboard/stats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getDashboardStats
);

// SSL Certificate Stats
adminRouter.get(
  "/dashboard/ssl-stats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getSSLCertificateStats
);

// Package Distribution by Category
adminRouter.get(
  "/dashboard/package-distribution",
  asyncBackFrontEndLang,
  adminMiddleware,
  getPackageDistributionByCategory
);

// Activity logs routes
adminRouter.get(
  "/activity-logs",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAdminActivityLogs
);
adminRouter.get(
  "/activity-logs/filters",
  asyncBackFrontEndLang,
  adminMiddleware,
  getActivityLogFilters
);

// GET ALL CHAT CONVERSATIONS
adminRouter.get(
  "/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllConversations
);

// GET A SPECIFIC CHAT CONVERSATION
adminRouter.get(
  "/chats/:conversationId",
  asyncBackFrontEndLang,
  adminMiddleware,
  getConversationById
);

// DELETE A SPECIFIC CHAT CONVERSATION
adminRouter.delete(
  "/chats/:conversationId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteConversation
);

// DELETE MULTIPLE CHAT CONVERSATIONS
adminRouter.post(
  "/chats/delete-multiple",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteMultipleConversations
);

// DELETE ALL CHAT CONVERSATIONS
adminRouter.delete(
  "/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteAllConversations
);

// GET USER CHAT CONVERSATIONS
adminRouter.get(
  "/users/:userId/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getUserConversations
);

// GET CHATBOT CONTEXT
adminRouter.get(
  "/chatbot-context",
  asyncBackFrontEndLang,
  adminMiddleware,
  getContext
);

// UPDATE CHATBOT CONTEXT
adminRouter.put(
  "/chatbot-context",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateContext
);

adminRouter.post("/deepl-translate", adminMiddleware, deeplTranslate);

// Notification Routes for Admin
adminRouter.get(
  "/notifications",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAdminNotifications
);

adminRouter.put(
  "/notifications/:notificationId/read",
  asyncBackFrontEndLang,
  adminMiddleware,
  markNotificationAsRead
);

adminRouter.put(
  "/notifications/read-all",
  asyncBackFrontEndLang,
  adminMiddleware,
  markAllNotificationsAsRead
);

// Notification Settings Routes

// Get all notification settings
adminRouter.get(
  "/notification-settings",
  asyncBackFrontEndLang,
  adminMiddleware,
  getNotificationSettings
);

// Get a specific notification setting by type
adminRouter.get(
  "/notification-settings/:type",
  asyncBackFrontEndLang,
  adminMiddleware,
  getNotificationSettingByType
);

// Update a notification setting
adminRouter.put(
  "/notification-settings/:type",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateNotificationSetting
);

// Manually trigger a notification process
adminRouter.post(
  "/notification-settings/:type/trigger",
  asyncBackFrontEndLang,
  adminMiddleware,
  triggerNotification
);

// Custom Notification Routes

// Get all custom notifications
adminRouter.get(
  "/custom-notifications",
  asyncBackFrontEndLang,
  adminMiddleware,
  getCustomNotifications
);

// Create a new custom notification
adminRouter.post(
  "/custom-notifications",
  asyncBackFrontEndLang,
  adminMiddleware,
  createCustomNotification
);

// Update a custom notification
adminRouter.put(
  "/custom-notifications/:id",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateCustomNotification
);

// Delete a custom notification
adminRouter.delete(
  "/custom-notifications/:id",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteCustomNotification
);

// Cancel a scheduled custom notification
adminRouter.post(
  "/custom-notifications/:id/cancel",
  asyncBackFrontEndLang,
  adminMiddleware,
  cancelCustomNotification
);

adminRouter.get(
  "/dashboard/package-distribution",
  asyncBackFrontEndLang,
  adminMiddleware,
  getPackageDistributionByCategory
);

// Job Routes
adminRouter.post("/jobs", asyncBackFrontEndLang, adminMiddleware, createJob);
adminRouter.get("/jobs", asyncBackFrontEndLang, adminMiddleware, getAllJobs);
adminRouter.get("/jobs/:id", asyncBackFrontEndLang, adminMiddleware, getJobById);
adminRouter.put("/jobs/:jobId", asyncBackFrontEndLang, adminMiddleware, updateJob);
adminRouter.delete("/jobs/:jobId", asyncBackFrontEndLang, adminMiddleware, deleteJob);

// Job Application Routes
adminRouter.get("/jobs/:jobId/applications", asyncBackFrontEndLang, adminMiddleware, getJobApplications);
adminRouter.get("/job-applications", asyncBackFrontEndLang, adminMiddleware, getAllJobApplications);
adminRouter.put("/job-applications/:applicationId/status", asyncBackFrontEndLang, adminMiddleware, updateApplicationStatus);
adminRouter.delete("/job-applications/:applicationId", asyncBackFrontEndLang, adminMiddleware, deleteApplication);

// Site Settings Routes

// Get all site settings
adminRouter.get(
  "/site-settings",
  asyncBackFrontEndLang,
  adminMiddleware,
  getSiteSettings
);

// Update all site settings
adminRouter.put(
  "/site-settings",
  asyncBackFrontEndLang,
  adminMiddleware,
  validateSiteSettings,
  updateSiteSettings
);

// Reset site settings to default
adminRouter.post(
  "/site-settings/reset",
  asyncBackFrontEndLang,
  adminMiddleware,
  resetSiteSettings
);

// Get specific section of site settings (general or seo)
adminRouter.get(
  "/site-settings/:section",
  asyncBackFrontEndLang,
  adminMiddleware,
  getSiteSettingsSection
);

// Update specific section of site settings
adminRouter.put(
  "/site-settings/:section",
  asyncBackFrontEndLang,
  adminMiddleware,
  (req, res, next) => {
    // Apply appropriate validation based on section
    if (req.params.section === 'general') {
      return validateGeneralSettings(req, res, next);
    } else if (req.params.section === 'seo') {
      return validateSeoSettings(req, res, next);
    } else {
      return res.status(400).json({
        success: false,
        message: 'Invalid section. Must be "general" or "seo"'
      });
    }
  },
  updateSiteSettingsSection
);

module.exports = adminRouter;
