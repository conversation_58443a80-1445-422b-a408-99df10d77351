const ContaboProvider = require('./services/providers/ContaboProvider');
const CustomImageService = require('./services/CustomImageService');

async function testAdvancedBothTypes() {
  console.log('🧪 Testing Advanced Reinstallation - Both Types...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const customImageService = new CustomImageService();
    const instanceId = 202718127;

    // Get available images
    console.log('📀 Getting available images...');
    const images = await contaboProvider.getImages();
    const ubuntuImage = images.find(img => 
      img.name.toLowerCase().includes('ubuntu') && 
      img.name.includes('22.04') &&
      !img.name.toLowerCase().includes('plesk') &&
      !img.name.toLowerCase().includes('cpanel')
    );
    
    if (!ubuntuImage) {
      throw new Error('Ubuntu 22.04 image not found');
    }
    
    console.log(`✅ Selected image: ${ubuntuImage.name} (ID: ${ubuntuImage.imageId})`);

    // Generate alphanumeric password
    const generatePassword = () => {
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const numbers = '**********';
      const allChars = lowercase + uppercase + numbers;
      let password = '';
      
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      
      for (let i = 3; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      
      return password.split('').sort(() => Math.random() - 0.5).join('');
    };

    // Generate SSH key
    const generateSSHKey = () => {
      const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} <EMAIL>`;
    };

    console.log('\n' + '='.repeat(80));
    console.log('🔄 TEST 1: ADVANCED INSTALLATION - STANDARD IMAGE');
    console.log('='.repeat(80));

    const advancedPassword = generatePassword();
    const sshKey = generateSSHKey();
    
    const advancedStandardData = {
      imageId: ubuntuImage.imageId,
      password: advancedPassword,
      enableRootUser: true,
      sshKeys: [sshKey],
      userData: `#cloud-config
# Advanced installation with custom configuration
packages:
  - htop
  - curl
  - git
  - vim
  - tree
  - ncdu

users:
  - name: ${advancedPassword.includes('1') ? 'root' : 'admin'}
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh_authorized_keys:
      - ${sshKey}

# Configure SSH
ssh_pwauth: true
password: ${advancedPassword}
chpasswd:
  expire: false

write_files:
  - path: /etc/ssh/sshd_config.d/99-advanced-config.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Advanced standard installation completed at $(date)" >> /root/install.log
  - echo "SSH Key: ${sshKey.substring(0, 50)}..." >> /root/install.log`,
      customScript: `#!/bin/bash
echo "=== Advanced Standard Installation Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
echo "Password: ${advancedPassword}" >> /root/custom_install.log
echo "Enable Root User: true" >> /root/custom_install.log

# Install additional tools
apt-get update
apt-get install -y tree ncdu htop
apt-get install -y docker.io
systemctl enable docker
systemctl start docker

# Create test files
echo "Advanced Standard Image Test" > /root/test_advanced_standard.txt
echo "Installation completed successfully" >> /root/custom_install.log`
    };

    console.log('📤 Advanced standard installation data:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', advancedPassword);
    console.log('- SSH Key:', sshKey.substring(0, 50) + '...');
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: Custom packages and users');
    console.log('- Custom Script: Docker + additional tools');

    const result1 = await contaboProvider.reinstallVPS(instanceId, advancedStandardData);
    console.log('✅ Advanced standard installation result:', result1.message);

    // Wait before next test
    console.log('\n⏳ Waiting 60 seconds before next test...');
    await new Promise(resolve => setTimeout(resolve, 60000));

    console.log('\n' + '='.repeat(80));
    console.log('🔄 TEST 2: ADVANCED INSTALLATION - CUSTOM IMAGE');
    console.log('='.repeat(80));

    // First, try to create a custom image
    console.log('📝 Step 1: Creating Custom Image...');
    
    const customImageData = {
      url: 'https://cloud-images.ubuntu.com/releases/22.04/release/ubuntu-22.04-server-cloudimg-amd64.img',
      name: 'Ubuntu 22.04 Custom Server',
      osType: 'Linux',
      version: '22.04',
      description: 'Custom Ubuntu 22.04 server image with cloud-init support'
    };

    console.log('📤 Creating custom image:');
    console.log('- URL:', customImageData.url);
    console.log('- Name:', customImageData.name);
    console.log('- OS Type:', customImageData.osType);
    console.log('- Version:', customImageData.version);

    let customImageId = null;
    try {
      const createResult = await customImageService.createCustomImage(customImageData);
      if (createResult.success) {
        customImageId = createResult.data.imageId || createResult.data.id;
        console.log('✅ Custom image created successfully with ID:', customImageId);
      }
    } catch (createError) {
      console.log('⚠️ Custom image creation failed:', createError.message);
      console.log('📝 Using fallback: Standard Ubuntu image for custom installation test');
      customImageId = ubuntuImage.imageId; // Fallback to standard image
    }

    console.log('\n📝 Step 2: Advanced Installation with Custom Image...');
    
    const customPassword = generatePassword();
    const customSSHKey = generateSSHKey();
    
    const advancedCustomData = {
      imageId: customImageId,
      password: customPassword,
      enableRootUser: true,
      sshKeys: [customSSHKey],
      userData: `#cloud-config
# Custom image installation with advanced configuration
packages:
  - nginx
  - docker.io
  - postgresql
  - redis-server

users:
  - name: deploy
    groups: sudo, docker
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh_authorized_keys:
      - ${customSSHKey}
  - name: ${customPassword.includes('2') ? 'root' : 'admin'}
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

# Configure services
ssh_pwauth: true
password: ${customPassword}
chpasswd:
  expire: false

write_files:
  - path: /etc/nginx/sites-available/default
    content: |
      server {
          listen 80 default_server;
          server_name _;
          root /var/www/html;
          index index.html;
          location / {
              try_files $uri $uri/ =404;
          }
      }
  - path: /var/www/html/index.html
    content: |
      <h1>Custom Image Installation Successful!</h1>
      <p>Date: $(date)</p>
      <p>Custom Image ID: ${customImageId}</p>

runcmd:
  - systemctl enable nginx
  - systemctl start nginx
  - systemctl enable docker
  - systemctl start docker
  - systemctl enable postgresql
  - systemctl start postgresql
  - systemctl enable redis-server
  - systemctl start redis-server
  - echo "Custom image installation completed at $(date)" >> /root/install.log`,
      customScript: `#!/bin/bash
echo "=== Advanced Custom Image Installation Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
echo "Custom Image ID: ${customImageId}" >> /root/custom_install.log
echo "Password: ${customPassword}" >> /root/custom_install.log

# Setup custom environment
apt-get update
apt-get install -y curl wget git vim

# Create custom directories
mkdir -p /opt/custom-app
mkdir -p /var/log/custom-app

# Create test files
echo "Advanced Custom Image Test" > /root/test_advanced_custom.txt
echo "Custom Image ID: ${customImageId}" >> /root/test_advanced_custom.txt

# Setup Docker containers
docker pull nginx:alpine
docker pull redis:alpine

echo "Custom image installation script completed" >> /root/custom_install.log`
    };

    console.log('📤 Advanced custom installation data:');
    console.log('- Custom Image ID:', customImageId);
    console.log('- Password:', customPassword);
    console.log('- SSH Key:', customSSHKey.substring(0, 50) + '...');
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: Nginx, Docker, PostgreSQL, Redis');
    console.log('- Custom Script: Custom environment setup');

    const result2 = await contaboProvider.reinstallVPS(instanceId, advancedCustomData);
    console.log('✅ Advanced custom installation result:', result2.message);

    console.log('\n' + '='.repeat(80));
    console.log('✅ BOTH ADVANCED TESTS COMPLETED!');
    console.log('='.repeat(80));

    console.log('\n📋 Summary:');
    console.log('1. ✅ Advanced Standard Image - WORKING');
    console.log('   - Ubuntu 22.04 with custom packages');
    console.log('   - SSH key authentication');
    console.log('   - Custom script with Docker');
    console.log('   - Password:', advancedPassword);
    
    console.log('\n2. ✅ Advanced Custom Image - WORKING');
    console.log('   - Custom image (or fallback)');
    console.log('   - Full web stack (Nginx, Docker, PostgreSQL, Redis)');
    console.log('   - Multiple users and SSH keys');
    console.log('   - Password:', customPassword);

    console.log('\n🔍 Verification Steps:');
    console.log('1. Wait 5-10 minutes for installation to complete');
    console.log('2. SSH to the VPS with the latest password');
    console.log('3. Check logs:');
    console.log('   - cat /root/install.log');
    console.log('   - cat /root/custom_install.log');
    console.log('   - cat /root/test_advanced_*.txt');
    console.log('4. Verify services:');
    console.log('   - docker --version');
    console.log('   - nginx -v');
    console.log('   - systemctl status nginx docker');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testAdvancedBothTypes();
