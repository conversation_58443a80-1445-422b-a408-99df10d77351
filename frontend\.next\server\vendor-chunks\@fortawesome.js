"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fortawesome";
exports.ids = ["vendor-chunks/@fortawesome"];
exports.modules = {

/***/ "(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css":
/*!*******************************************************************!*\
  !*** ./node_modules/@fortawesome/fontawesome-svg-core/styles.css ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9c8eb51c5021\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlL3N0eWxlcy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL0Bmb3J0YXdlc29tZS9mb250YXdlc29tZS1zdmctY29yZS9zdHlsZXMuY3NzPzcwMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5YzhlYjUxYzUwMjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@fortawesome/fontawesome-svg-core/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   config: () => (/* binding */ config$1),\n/* harmony export */   counter: () => (/* binding */ counter),\n/* harmony export */   dom: () => (/* binding */ dom$1),\n/* harmony export */   findIconDefinition: () => (/* binding */ findIconDefinition$1),\n/* harmony export */   icon: () => (/* binding */ icon),\n/* harmony export */   layer: () => (/* binding */ layer),\n/* harmony export */   library: () => (/* binding */ library$1),\n/* harmony export */   noAuto: () => (/* binding */ noAuto$1),\n/* harmony export */   parse: () => (/* binding */ parse$1),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   toHtml: () => (/* binding */ toHtml$1)\n/* harmony export */ });\nconst noop = ()=>{};\nlet _WINDOW = {};\nlet _DOCUMENT = {};\nlet _MUTATION_OBSERVER = null;\nlet _PERFORMANCE = {\n    mark: noop,\n    measure: noop\n};\ntry {\n    if (false) {}\n    if (typeof document !== \"undefined\") _DOCUMENT = document;\n    if (typeof MutationObserver !== \"undefined\") _MUTATION_OBSERVER = MutationObserver;\n    if (typeof performance !== \"undefined\") _PERFORMANCE = performance;\n} catch (e) {}\nconst { userAgent = \"\" } = _WINDOW.navigator || {};\nconst WINDOW = _WINDOW;\nconst DOCUMENT = _DOCUMENT;\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\nconst PERFORMANCE = _PERFORMANCE;\nconst IS_BROWSER = !!WINDOW.document;\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === \"function\" && typeof DOCUMENT.createElement === \"function\";\nconst IS_IE = ~userAgent.indexOf(\"MSIE\") || ~userAgent.indexOf(\"Trident/\");\nvar a = \"classic\", t = \"duotone\", r = \"sharp\", o = \"sharp-duotone\", c = [\n    a,\n    t,\n    r,\n    o\n];\nvar et$1 = {\n    classic: {\n        900: \"fas\",\n        400: \"far\",\n        normal: \"far\",\n        300: \"fal\",\n        100: \"fat\"\n    },\n    sharp: {\n        900: \"fass\",\n        400: \"fasr\",\n        300: \"fasl\",\n        100: \"fast\"\n    },\n    \"sharp-duotone\": {\n        900: \"fasds\"\n    }\n};\nvar bt = {\n    kit: {\n        fak: \"kit\",\n        \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n        fakd: \"kit-duotone\",\n        \"fa-kit-duotone\": \"kit-duotone\"\n    }\n}, Ct = [\n    \"kit\"\n];\nvar Dt = /fa(s|r|l|t|d|b|k|kd|ss|sr|sl|st|sds)?[\\-\\ ]/, Kt = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\nvar ao = {\n    \"Font Awesome 5 Free\": {\n        900: \"fas\",\n        400: \"far\"\n    },\n    \"Font Awesome 5 Pro\": {\n        900: \"fas\",\n        400: \"far\",\n        normal: \"far\",\n        300: \"fal\"\n    },\n    \"Font Awesome 5 Brands\": {\n        400: \"fab\",\n        normal: \"fab\"\n    },\n    \"Font Awesome 5 Duotone\": {\n        900: \"fad\"\n    }\n};\nvar eo = {\n    \"Font Awesome 6 Free\": {\n        900: \"fas\",\n        400: \"far\"\n    },\n    \"Font Awesome 6 Pro\": {\n        900: \"fas\",\n        400: \"far\",\n        normal: \"far\",\n        300: \"fal\",\n        100: \"fat\"\n    },\n    \"Font Awesome 6 Brands\": {\n        400: \"fab\",\n        normal: \"fab\"\n    },\n    \"Font Awesome 6 Duotone\": {\n        900: \"fad\"\n    },\n    \"Font Awesome 6 Sharp\": {\n        900: \"fass\",\n        400: \"fasr\",\n        normal: \"fasr\",\n        300: \"fasl\",\n        100: \"fast\"\n    },\n    \"Font Awesome 6 Sharp Duotone\": {\n        900: \"fasds\"\n    }\n}, lo = {\n    classic: {\n        \"fa-brands\": \"fab\",\n        \"fa-duotone\": \"fad\",\n        \"fa-light\": \"fal\",\n        \"fa-regular\": \"far\",\n        \"fa-solid\": \"fas\",\n        \"fa-thin\": \"fat\"\n    },\n    sharp: {\n        \"fa-solid\": \"fass\",\n        \"fa-regular\": \"fasr\",\n        \"fa-light\": \"fasl\",\n        \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n        \"fa-solid\": \"fasds\"\n    }\n}, y = {\n    classic: [\n        \"fas\",\n        \"far\",\n        \"fal\",\n        \"fat\"\n    ],\n    sharp: [\n        \"fass\",\n        \"fasr\",\n        \"fasl\",\n        \"fast\"\n    ],\n    \"sharp-duotone\": [\n        \"fasds\"\n    ]\n}, no = {\n    classic: {\n        fab: \"fa-brands\",\n        fad: \"fa-duotone\",\n        fal: \"fa-light\",\n        far: \"fa-regular\",\n        fas: \"fa-solid\",\n        fat: \"fa-thin\"\n    },\n    sharp: {\n        fass: \"fa-solid\",\n        fasr: \"fa-regular\",\n        fasl: \"fa-light\",\n        fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n        fasds: \"fa-solid\"\n    }\n}, fo = {\n    classic: {\n        solid: \"fas\",\n        regular: \"far\",\n        light: \"fal\",\n        thin: \"fat\",\n        duotone: \"fad\",\n        brands: \"fab\"\n    },\n    sharp: {\n        solid: \"fass\",\n        regular: \"fasr\",\n        light: \"fasl\",\n        thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n        solid: \"fasds\"\n    }\n}, ho = {\n    classic: {\n        fa: \"solid\",\n        fas: \"solid\",\n        \"fa-solid\": \"solid\",\n        far: \"regular\",\n        \"fa-regular\": \"regular\",\n        fal: \"light\",\n        \"fa-light\": \"light\",\n        fat: \"thin\",\n        \"fa-thin\": \"thin\",\n        fad: \"duotone\",\n        \"fa-duotone\": \"duotone\",\n        fab: \"brands\",\n        \"fa-brands\": \"brands\"\n    },\n    sharp: {\n        fa: \"solid\",\n        fass: \"solid\",\n        \"fa-solid\": \"solid\",\n        fasr: \"regular\",\n        \"fa-regular\": \"regular\",\n        fasl: \"light\",\n        \"fa-light\": \"light\",\n        fast: \"thin\",\n        \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n        fa: \"solid\",\n        fasds: \"solid\",\n        \"fa-solid\": \"solid\"\n    }\n}, x$1 = [\n    \"solid\",\n    \"regular\",\n    \"light\",\n    \"thin\",\n    \"duotone\",\n    \"brands\"\n], u$1 = [\n    1,\n    2,\n    3,\n    4,\n    5,\n    6,\n    7,\n    8,\n    9,\n    10\n], m$1 = u$1.concat([\n    11,\n    12,\n    13,\n    14,\n    15,\n    16,\n    17,\n    18,\n    19,\n    20\n]), t$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n}, yo = [\n    ...Object.keys(y),\n    ...x$1,\n    \"2xs\",\n    \"xs\",\n    \"sm\",\n    \"lg\",\n    \"xl\",\n    \"2xl\",\n    \"beat\",\n    \"border\",\n    \"fade\",\n    \"beat-fade\",\n    \"bounce\",\n    \"flip-both\",\n    \"flip-horizontal\",\n    \"flip-vertical\",\n    \"flip\",\n    \"fw\",\n    \"inverse\",\n    \"layers-counter\",\n    \"layers-text\",\n    \"layers\",\n    \"li\",\n    \"pull-left\",\n    \"pull-right\",\n    \"pulse\",\n    \"rotate-180\",\n    \"rotate-270\",\n    \"rotate-90\",\n    \"rotate-by\",\n    \"shake\",\n    \"spin-pulse\",\n    \"spin-reverse\",\n    \"spin\",\n    \"stack-1x\",\n    \"stack-2x\",\n    \"stack\",\n    \"ul\",\n    t$1.GROUP,\n    t$1.SWAP_OPACITY,\n    t$1.PRIMARY,\n    t$1.SECONDARY\n].concat(u$1.map((o)=>\"\".concat(o, \"x\"))).concat(m$1.map((o)=>\"w-\".concat(o)));\nvar mo = {\n    \"Font Awesome Kit\": {\n        400: \"fak\",\n        normal: \"fak\"\n    },\n    \"Font Awesome Kit Duotone\": {\n        400: \"fakd\",\n        normal: \"fakd\"\n    }\n}, Io = {\n    kit: {\n        \"fa-kit\": \"fak\"\n    },\n    \"kit-duotone\": {\n        \"fa-kit-duotone\": \"fakd\"\n    }\n}, Fo = {\n    kit: {\n        fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n        fakd: \"fa-kit-duotone\"\n    }\n}, So = {\n    kit: {\n        kit: \"fak\"\n    },\n    \"kit-duotone\": {\n        \"kit-duotone\": \"fakd\"\n    }\n};\nconst NAMESPACE_IDENTIFIER = \"___FONT_AWESOME___\";\nconst UNITS_IN_GRID = 16;\nconst DEFAULT_CSS_PREFIX = \"fa\";\nconst DEFAULT_REPLACEMENT_CLASS = \"svg-inline--fa\";\nconst DATA_FA_I2SVG = \"data-fa-i2svg\";\nconst DATA_FA_PSEUDO_ELEMENT = \"data-fa-pseudo-element\";\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = \"data-fa-pseudo-element-pending\";\nconst DATA_PREFIX = \"data-prefix\";\nconst DATA_ICON = \"data-icon\";\nconst HTML_CLASS_I2SVG_BASE_CLASS = \"fontawesome-i2svg\";\nconst MUTATION_APPROACH_ASYNC = \"async\";\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = [\n    \"HTML\",\n    \"HEAD\",\n    \"STYLE\",\n    \"SCRIPT\"\n];\nconst PRODUCTION = (()=>{\n    try {\n        return \"development\" === \"production\";\n    } catch (e$$1) {\n        return false;\n    }\n})();\nconst FAMILIES = [\n    a,\n    r,\n    o\n];\nfunction familyProxy(obj) {\n    // Defaults to the classic family if family is not available\n    return new Proxy(obj, {\n        get (target, prop) {\n            return prop in target ? target[prop] : target[a];\n        }\n    });\n}\nconst _PREFIX_TO_STYLE = {\n    ...ho\n};\n_PREFIX_TO_STYLE[a] = {\n    ...ho[a],\n    ...bt[\"kit\"],\n    ...bt[\"kit-duotone\"]\n};\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nconst _STYLE_TO_PREFIX = {\n    ...fo\n};\n_STYLE_TO_PREFIX[a] = {\n    ..._STYLE_TO_PREFIX[a],\n    ...So[\"kit\"],\n    ...So[\"kit-duotone\"]\n};\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nconst _PREFIX_TO_LONG_STYLE = {\n    ...no\n};\n_PREFIX_TO_LONG_STYLE[a] = {\n    ..._PREFIX_TO_LONG_STYLE[a],\n    ...Fo[\"kit\"]\n};\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nconst _LONG_STYLE_TO_PREFIX = {\n    ...lo\n};\n_LONG_STYLE_TO_PREFIX[a] = {\n    ..._LONG_STYLE_TO_PREFIX[a],\n    ...Io[\"kit\"]\n};\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nconst ICON_SELECTION_SYNTAX_PATTERN = Dt; // eslint-disable-line no-useless-escape\nconst LAYERS_TEXT_CLASSNAME = \"fa-layers-text\";\nconst FONT_FAMILY_PATTERN = Kt;\nconst _FONT_WEIGHT_TO_PREFIX = {\n    ...et$1\n};\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = [\n    \"class\",\n    \"data-prefix\",\n    \"data-icon\",\n    \"data-fa-transform\",\n    \"data-fa-mask\"\n];\nconst DUOTONE_CLASSES = t$1;\nconst prefixes = new Set();\nObject.keys(STYLE_TO_PREFIX[a]).map(prefixes.add.bind(prefixes));\nObject.keys(STYLE_TO_PREFIX[r]).map(prefixes.add.bind(prefixes));\nObject.keys(STYLE_TO_PREFIX[o]).map(prefixes.add.bind(prefixes));\nconst RESERVED_CLASSES = [\n    ...Ct,\n    ...yo\n];\nconst initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n    var element = DOCUMENT.querySelector(\"script[\" + attr + \"]\");\n    if (element) {\n        return element.getAttribute(attr);\n    }\n}\nfunction coerce(val) {\n    // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n    // We'll assume that this is an indication that it should be toggled to true\n    if (val === \"\") return true;\n    if (val === \"false\") return false;\n    if (val === \"true\") return true;\n    return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === \"function\") {\n    const attrs = [\n        [\n            \"data-family-prefix\",\n            \"familyPrefix\"\n        ],\n        [\n            \"data-css-prefix\",\n            \"cssPrefix\"\n        ],\n        [\n            \"data-family-default\",\n            \"familyDefault\"\n        ],\n        [\n            \"data-style-default\",\n            \"styleDefault\"\n        ],\n        [\n            \"data-replacement-class\",\n            \"replacementClass\"\n        ],\n        [\n            \"data-auto-replace-svg\",\n            \"autoReplaceSvg\"\n        ],\n        [\n            \"data-auto-add-css\",\n            \"autoAddCss\"\n        ],\n        [\n            \"data-auto-a11y\",\n            \"autoA11y\"\n        ],\n        [\n            \"data-search-pseudo-elements\",\n            \"searchPseudoElements\"\n        ],\n        [\n            \"data-observe-mutations\",\n            \"observeMutations\"\n        ],\n        [\n            \"data-mutate-approach\",\n            \"mutateApproach\"\n        ],\n        [\n            \"data-keep-original-source\",\n            \"keepOriginalSource\"\n        ],\n        [\n            \"data-measure-performance\",\n            \"measurePerformance\"\n        ],\n        [\n            \"data-show-missing-icons\",\n            \"showMissingIcons\"\n        ]\n    ];\n    attrs.forEach((_ref)=>{\n        let [attr, key] = _ref;\n        const val = coerce(getAttrConfig(attr));\n        if (val !== undefined && val !== null) {\n            initial[key] = val;\n        }\n    });\n}\nconst _default = {\n    styleDefault: \"solid\",\n    familyDefault: \"classic\",\n    cssPrefix: DEFAULT_CSS_PREFIX,\n    replacementClass: DEFAULT_REPLACEMENT_CLASS,\n    autoReplaceSvg: true,\n    autoAddCss: true,\n    autoA11y: true,\n    searchPseudoElements: false,\n    observeMutations: true,\n    mutateApproach: \"async\",\n    keepOriginalSource: true,\n    measurePerformance: false,\n    showMissingIcons: true\n}; // familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n    initial.cssPrefix = initial.familyPrefix;\n}\nconst _config = {\n    ..._default,\n    ...initial\n};\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nconst config = {};\nObject.keys(_default).forEach((key)=>{\n    Object.defineProperty(config, key, {\n        enumerable: true,\n        set: function(val) {\n            _config[key] = val;\n            _onChangeCb.forEach((cb)=>cb(config));\n        },\n        get: function() {\n            return _config[key];\n        }\n    });\n}); // familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, \"familyPrefix\", {\n    enumerable: true,\n    set: function(val) {\n        _config.cssPrefix = val;\n        _onChangeCb.forEach((cb)=>cb(config));\n    },\n    get: function() {\n        return _config.cssPrefix;\n    }\n});\nWINDOW.FontAwesomeConfig = config;\nconst _onChangeCb = [];\nfunction onChange(cb) {\n    _onChangeCb.push(cb);\n    return ()=>{\n        _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n    };\n}\nconst d$2 = UNITS_IN_GRID;\nconst meaninglessTransform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    rotate: 0,\n    flipX: false,\n    flipY: false\n};\nfunction insertCss(css) {\n    if (!css || !IS_DOM) {\n        return;\n    }\n    const style = DOCUMENT.createElement(\"style\");\n    style.setAttribute(\"type\", \"text/css\");\n    style.innerHTML = css;\n    const headChildren = DOCUMENT.head.childNodes;\n    let beforeChild = null;\n    for(let i = headChildren.length - 1; i > -1; i--){\n        const child = headChildren[i];\n        const tagName = (child.tagName || \"\").toUpperCase();\n        if ([\n            \"STYLE\",\n            \"LINK\"\n        ].indexOf(tagName) > -1) {\n            beforeChild = child;\n        }\n    }\n    DOCUMENT.head.insertBefore(style, beforeChild);\n    return css;\n}\nconst idPool = \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\";\nfunction nextUniqueId() {\n    let size = 12;\n    let id = \"\";\n    while(size-- > 0){\n        id += idPool[Math.random() * 62 | 0];\n    }\n    return id;\n}\nfunction toArray(obj) {\n    const array = [];\n    for(let i = (obj || []).length >>> 0; i--;){\n        array[i] = obj[i];\n    }\n    return array;\n}\nfunction classArray(node) {\n    if (node.classList) {\n        return toArray(node.classList);\n    } else {\n        return (node.getAttribute(\"class\") || \"\").split(\" \").filter((i)=>i);\n    }\n}\nfunction htmlEscape(str) {\n    return \"\".concat(str).replace(/&/g, \"&amp;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n}\nfunction joinAttributes(attributes) {\n    return Object.keys(attributes || {}).reduce((acc, attributeName)=>{\n        return acc + \"\".concat(attributeName, '=\"').concat(htmlEscape(attributes[attributeName]), '\" ');\n    }, \"\").trim();\n}\nfunction joinStyles(styles) {\n    return Object.keys(styles || {}).reduce((acc, styleName)=>{\n        return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n    }, \"\");\n}\nfunction transformIsMeaningful(transform) {\n    return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n    let { transform, containerWidth, iconWidth } = _ref;\n    const outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n    };\n    const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n    const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n    const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n    const inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n    };\n    const path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n    };\n    return {\n        outer,\n        inner,\n        path\n    };\n}\nfunction transformForCss(_ref2) {\n    let { transform, width = UNITS_IN_GRID, height = UNITS_IN_GRID, startCentered = false } = _ref2;\n    let val = \"\";\n    if (startCentered && IS_IE) {\n        val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n    } else if (startCentered) {\n        val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n    } else {\n        val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n    }\n    val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n    val += \"rotate(\".concat(transform.rotate, \"deg) \");\n    return val;\n}\nvar baseStyles = ':root, :host {\\n  --fa-font-solid: normal 900 1em/1 \"Font Awesome 6 Free\";\\n  --fa-font-regular: normal 400 1em/1 \"Font Awesome 6 Free\";\\n  --fa-font-light: normal 300 1em/1 \"Font Awesome 6 Pro\";\\n  --fa-font-thin: normal 100 1em/1 \"Font Awesome 6 Pro\";\\n  --fa-font-duotone: normal 900 1em/1 \"Font Awesome 6 Duotone\";\\n  --fa-font-brands: normal 400 1em/1 \"Font Awesome 6 Brands\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \"Font Awesome 6 Sharp\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \"Font Awesome 6 Sharp\";\\n  --fa-font-sharp-light: normal 300 1em/1 \"Font Awesome 6 Sharp\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \"Font Awesome 6 Sharp\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \"Font Awesome 6 Sharp Duotone\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\\n\\n.fad.fa-inverse,\\n.fa-duotone.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}';\nfunction css() {\n    const dcp = DEFAULT_CSS_PREFIX;\n    const drc = DEFAULT_REPLACEMENT_CLASS;\n    const fp = config.cssPrefix;\n    const rc = config.replacementClass;\n    let s = baseStyles;\n    if (fp !== dcp || rc !== drc) {\n        const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), \"g\");\n        const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), \"g\");\n        const rPatt = new RegExp(\"\\\\.\".concat(drc), \"g\");\n        s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n    }\n    return s;\n}\nlet _cssInserted = false;\nfunction ensureCss() {\n    if (config.autoAddCss && !_cssInserted) {\n        insertCss(css());\n        _cssInserted = true;\n    }\n}\nvar InjectCSS = {\n    mixout () {\n        return {\n            dom: {\n                css,\n                insertCss: ensureCss\n            }\n        };\n    },\n    hooks () {\n        return {\n            beforeDOMElementCreation () {\n                ensureCss();\n            },\n            beforeI2svg () {\n                ensureCss();\n            }\n        };\n    }\n};\nconst w$1 = WINDOW || {};\nif (!w$1[NAMESPACE_IDENTIFIER]) w$1[NAMESPACE_IDENTIFIER] = {};\nif (!w$1[NAMESPACE_IDENTIFIER].styles) w$1[NAMESPACE_IDENTIFIER].styles = {};\nif (!w$1[NAMESPACE_IDENTIFIER].hooks) w$1[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w$1[NAMESPACE_IDENTIFIER].shims) w$1[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w$1[NAMESPACE_IDENTIFIER];\nconst functions = [];\nconst listener = function() {\n    DOCUMENT.removeEventListener(\"DOMContentLoaded\", listener);\n    loaded = 1;\n    functions.map((fn)=>fn());\n};\nlet loaded = false;\nif (IS_DOM) {\n    loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n    if (!loaded) DOCUMENT.addEventListener(\"DOMContentLoaded\", listener);\n}\nfunction domready(fn) {\n    if (!IS_DOM) return;\n    loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\nfunction toHtml(abstractNodes) {\n    const { tag, attributes = {}, children = [] } = abstractNodes;\n    if (typeof abstractNodes === \"string\") {\n        return htmlEscape(abstractNodes);\n    } else {\n        return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(\"\"), \"</\").concat(tag, \">\");\n    }\n}\nfunction iconFromMapping(mapping, prefix, iconName) {\n    if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n        return {\n            prefix,\n            iconName,\n            icon: mapping[prefix][iconName]\n        };\n    }\n}\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */ var bindInternal4 = function bindInternal4(func, thisContext) {\n    return function(a, b, c, d) {\n        return func.call(thisContext, a, b, c, d);\n    };\n};\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */ var reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n    var keys = Object.keys(subject), length = keys.length, iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn, i, key, result;\n    if (initialValue === undefined) {\n        i = 1;\n        result = subject[keys[0]];\n    } else {\n        i = 0;\n        result = initialValue;\n    }\n    for(; i < length; i++){\n        key = keys[i];\n        result = iterator(result, subject[key], key, subject);\n    }\n    return result;\n};\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */ function ucs2decode(string) {\n    const output = [];\n    let counter = 0;\n    const length = string.length;\n    while(counter < length){\n        const value = string.charCodeAt(counter++);\n        if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n            const extra = string.charCodeAt(counter++);\n            if ((extra & 0xFC00) == 0xDC00) {\n                // eslint-disable-line eqeqeq\n                output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n            } else {\n                output.push(value);\n                counter--;\n            }\n        } else {\n            output.push(value);\n        }\n    }\n    return output;\n}\nfunction toHex(unicode) {\n    const decoded = ucs2decode(unicode);\n    return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n    const size = string.length;\n    let first = string.charCodeAt(index);\n    let second;\n    if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n        second = string.charCodeAt(index + 1);\n        if (second >= 0xDC00 && second <= 0xDFFF) {\n            return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n        }\n    }\n    return first;\n}\nfunction normalizeIcons(icons) {\n    return Object.keys(icons).reduce((acc, iconName)=>{\n        const icon = icons[iconName];\n        const expanded = !!icon.icon;\n        if (expanded) {\n            acc[icon.iconName] = icon.icon;\n        } else {\n            acc[iconName] = icon;\n        }\n        return acc;\n    }, {});\n}\nfunction defineIcons(prefix, icons) {\n    let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const { skipHooks = false } = params;\n    const normalized = normalizeIcons(icons);\n    if (typeof namespace.hooks.addPack === \"function\" && !skipHooks) {\n        namespace.hooks.addPack(prefix, normalizeIcons(icons));\n    } else {\n        namespace.styles[prefix] = {\n            ...namespace.styles[prefix] || {},\n            ...normalized\n        };\n    }\n    /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */ if (prefix === \"fas\") {\n        defineIcons(\"fa\", icons);\n    }\n}\nconst { styles, shims } = namespace;\nconst LONG_STYLE = {\n    [a]: Object.values(PREFIX_TO_LONG_STYLE[a]),\n    [r]: Object.values(PREFIX_TO_LONG_STYLE[r]),\n    [o]: Object.values(PREFIX_TO_LONG_STYLE[o])\n};\nlet _defaultUsablePrefix = null;\nlet _byUnicode = {};\nlet _byLigature = {};\nlet _byOldName = {};\nlet _byOldUnicode = {};\nlet _byAlias = {};\nconst PREFIXES = {\n    [a]: Object.keys(PREFIX_TO_STYLE[a]),\n    [r]: Object.keys(PREFIX_TO_STYLE[r]),\n    [o]: Object.keys(PREFIX_TO_STYLE[o])\n};\nfunction isReserved(name) {\n    return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n    const parts = cls.split(\"-\");\n    const prefix = parts[0];\n    const iconName = parts.slice(1).join(\"-\");\n    if (prefix === cssPrefix && iconName !== \"\" && !isReserved(iconName)) {\n        return iconName;\n    } else {\n        return null;\n    }\n}\nconst build = ()=>{\n    const lookup = (reducer)=>{\n        return reduce(styles, (o$$1, style, prefix)=>{\n            o$$1[prefix] = reduce(style, reducer, {});\n            return o$$1;\n        }, {});\n    };\n    _byUnicode = lookup((acc, icon, iconName)=>{\n        if (icon[3]) {\n            acc[icon[3]] = iconName;\n        }\n        if (icon[2]) {\n            const aliases = icon[2].filter((a$$1)=>{\n                return typeof a$$1 === \"number\";\n            });\n            aliases.forEach((alias)=>{\n                acc[alias.toString(16)] = iconName;\n            });\n        }\n        return acc;\n    });\n    _byLigature = lookup((acc, icon, iconName)=>{\n        acc[iconName] = iconName;\n        if (icon[2]) {\n            const aliases = icon[2].filter((a$$1)=>{\n                return typeof a$$1 === \"string\";\n            });\n            aliases.forEach((alias)=>{\n                acc[alias] = iconName;\n            });\n        }\n        return acc;\n    });\n    _byAlias = lookup((acc, icon, iconName)=>{\n        const aliases = icon[2];\n        acc[iconName] = iconName;\n        aliases.forEach((alias)=>{\n            acc[alias] = iconName;\n        });\n        return acc;\n    }); // If we have a Kit, we can't determine if regular is available since we\n    // could be auto-fetching it. We'll have to assume that it is available.\n    const hasRegular = \"far\" in styles || config.autoFetchSvg;\n    const shimLookups = reduce(shims, (acc, shim)=>{\n        const maybeNameMaybeUnicode = shim[0];\n        let prefix = shim[1];\n        const iconName = shim[2];\n        if (prefix === \"far\" && !hasRegular) {\n            prefix = \"fas\";\n        }\n        if (typeof maybeNameMaybeUnicode === \"string\") {\n            acc.names[maybeNameMaybeUnicode] = {\n                prefix,\n                iconName\n            };\n        }\n        if (typeof maybeNameMaybeUnicode === \"number\") {\n            acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n                prefix,\n                iconName\n            };\n        }\n        return acc;\n    }, {\n        names: {},\n        unicodes: {}\n    });\n    _byOldName = shimLookups.names;\n    _byOldUnicode = shimLookups.unicodes;\n    _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n        family: config.familyDefault\n    });\n};\nonChange((c$$1)=>{\n    _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n        family: config.familyDefault\n    });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n    return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n    return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n    return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n    return _byOldName[name] || {\n        prefix: null,\n        iconName: null\n    };\n}\nfunction byOldUnicode(unicode) {\n    const oldUnicode = _byOldUnicode[unicode];\n    const newUnicode = byUnicode(\"fas\", unicode);\n    return oldUnicode || (newUnicode ? {\n        prefix: \"fas\",\n        iconName: newUnicode\n    } : null) || {\n        prefix: null,\n        iconName: null\n    };\n}\nfunction getDefaultUsablePrefix() {\n    return _defaultUsablePrefix;\n}\nconst emptyCanonicalIcon = ()=>{\n    return {\n        prefix: null,\n        iconName: null,\n        rest: []\n    };\n};\nfunction getCanonicalPrefix(styleOrPrefix) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const { family = a } = params;\n    const style = PREFIX_TO_STYLE[family][styleOrPrefix];\n    const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n    const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n    const result = prefix || defined || null;\n    return result;\n}\nconst PREFIXES_FOR_FAMILY = {\n    [a]: Object.keys(PREFIX_TO_LONG_STYLE[a]),\n    [r]: Object.keys(PREFIX_TO_LONG_STYLE[r]),\n    [o]: Object.keys(PREFIX_TO_LONG_STYLE[o])\n};\nfunction getCanonicalIcon(values) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const { skipLookups = false } = params;\n    const famProps = {\n        [a]: \"\".concat(config.cssPrefix, \"-\").concat(a),\n        [r]: \"\".concat(config.cssPrefix, \"-\").concat(r),\n        [o]: \"\".concat(config.cssPrefix, \"-\").concat(o)\n    };\n    let givenPrefix = null;\n    let family = a;\n    const nonDuotoneFamilyIds = c.filter((familyId)=>familyId !== t);\n    nonDuotoneFamilyIds.forEach((familyId)=>{\n        if (values.includes(famProps[familyId]) || values.some((v$$1)=>PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\n            family = familyId;\n        }\n    });\n    const canonical = values.reduce((acc, cls)=>{\n        const iconName = getIconName(config.cssPrefix, cls);\n        if (styles[cls]) {\n            cls = LONG_STYLE[family].includes(cls) ? LONG_STYLE_TO_PREFIX[family][cls] : cls;\n            givenPrefix = cls;\n            acc.prefix = cls;\n        } else if (PREFIXES[family].indexOf(cls) > -1) {\n            givenPrefix = cls;\n            acc.prefix = getCanonicalPrefix(cls, {\n                family\n            });\n        } else if (iconName) {\n            acc.iconName = iconName;\n        } else if (cls !== config.replacementClass && !nonDuotoneFamilyIds.some((familyName)=>cls === famProps[familyName])) {\n            acc.rest.push(cls);\n        }\n        if (!skipLookups && acc.prefix && acc.iconName) {\n            const shim = givenPrefix === \"fa\" ? byOldName(acc.iconName) : {};\n            const aliasIconName = byAlias(acc.prefix, acc.iconName);\n            if (shim.prefix) {\n                givenPrefix = null;\n            }\n            acc.iconName = shim.iconName || aliasIconName || acc.iconName;\n            acc.prefix = shim.prefix || acc.prefix;\n            if (acc.prefix === \"far\" && !styles[\"far\"] && styles[\"fas\"] && !config.autoFetchSvg) {\n                // Allow a fallback from the regular style to solid if regular is not available\n                // but only if we aren't auto-fetching SVGs\n                acc.prefix = \"fas\";\n            }\n        }\n        return acc;\n    }, emptyCanonicalIcon());\n    if (values.includes(\"fa-brands\") || values.includes(\"fab\")) {\n        canonical.prefix = \"fab\";\n    }\n    if (values.includes(\"fa-duotone\") || values.includes(\"fad\")) {\n        canonical.prefix = \"fad\";\n    }\n    if (!canonical.prefix && family === r && (styles[\"fass\"] || config.autoFetchSvg)) {\n        canonical.prefix = \"fass\";\n        canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n    if (!canonical.prefix && family === o && (styles[\"fasds\"] || config.autoFetchSvg)) {\n        canonical.prefix = \"fasds\";\n        canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n    if (canonical.prefix === \"fa\" || givenPrefix === \"fa\") {\n        // The fa prefix is not canonical. So if it has made it through until this point\n        // we will shift it to the correct prefix.\n        canonical.prefix = getDefaultUsablePrefix() || \"fas\";\n    }\n    return canonical;\n}\nclass Library {\n    constructor(){\n        this.definitions = {};\n    }\n    add() {\n        for(var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++){\n            definitions[_key] = arguments[_key];\n        }\n        const additions = definitions.reduce(this._pullDefinitions, {});\n        Object.keys(additions).forEach((key)=>{\n            this.definitions[key] = {\n                ...this.definitions[key] || {},\n                ...additions[key]\n            };\n            defineIcons(key, additions[key]); // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n            const longPrefix = PREFIX_TO_LONG_STYLE[a][key];\n            if (longPrefix) defineIcons(longPrefix, additions[key]);\n            build();\n        });\n    }\n    reset() {\n        this.definitions = {};\n    }\n    _pullDefinitions(additions, definition) {\n        const normalized = definition.prefix && definition.iconName && definition.icon ? {\n            0: definition\n        } : definition;\n        Object.keys(normalized).map((key)=>{\n            const { prefix, iconName, icon } = normalized[key];\n            const aliases = icon[2];\n            if (!additions[prefix]) additions[prefix] = {};\n            if (aliases.length > 0) {\n                aliases.forEach((alias)=>{\n                    if (typeof alias === \"string\") {\n                        additions[prefix][alias] = icon;\n                    }\n                });\n            }\n            additions[prefix][iconName] = icon;\n        });\n        return additions;\n    }\n}\nlet _plugins = [];\nlet _hooks = {};\nconst providers = {};\nconst defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n    let { mixoutsTo: obj } = _ref;\n    _plugins = nextPlugins;\n    _hooks = {};\n    Object.keys(providers).forEach((k)=>{\n        if (defaultProviderKeys.indexOf(k) === -1) {\n            delete providers[k];\n        }\n    });\n    _plugins.forEach((plugin)=>{\n        const mixout = plugin.mixout ? plugin.mixout() : {};\n        Object.keys(mixout).forEach((tk)=>{\n            if (typeof mixout[tk] === \"function\") {\n                obj[tk] = mixout[tk];\n            }\n            if (typeof mixout[tk] === \"object\") {\n                Object.keys(mixout[tk]).forEach((sk)=>{\n                    if (!obj[tk]) {\n                        obj[tk] = {};\n                    }\n                    obj[tk][sk] = mixout[tk][sk];\n                });\n            }\n        });\n        if (plugin.hooks) {\n            const hooks = plugin.hooks();\n            Object.keys(hooks).forEach((hook)=>{\n                if (!_hooks[hook]) {\n                    _hooks[hook] = [];\n                }\n                _hooks[hook].push(hooks[hook]);\n            });\n        }\n        if (plugin.provides) {\n            plugin.provides(providers);\n        }\n    });\n    return obj;\n}\nfunction chainHooks(hook, accumulator) {\n    for(var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++){\n        args[_key - 2] = arguments[_key];\n    }\n    const hookFns = _hooks[hook] || [];\n    hookFns.forEach((hookFn)=>{\n        accumulator = hookFn.apply(null, [\n            accumulator,\n            ...args\n        ]); // eslint-disable-line no-useless-call\n    });\n    return accumulator;\n}\nfunction callHooks(hook) {\n    for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n        args[_key2 - 1] = arguments[_key2];\n    }\n    const hookFns = _hooks[hook] || [];\n    hookFns.forEach((hookFn)=>{\n        hookFn.apply(null, args);\n    });\n    return undefined;\n}\nfunction callProvided() {\n    const hook = arguments[0];\n    const args = Array.prototype.slice.call(arguments, 1);\n    return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\nfunction findIconDefinition(iconLookup) {\n    if (iconLookup.prefix === \"fa\") {\n        iconLookup.prefix = \"fas\";\n    }\n    let { iconName } = iconLookup;\n    const prefix = iconLookup.prefix || getDefaultUsablePrefix();\n    if (!iconName) return;\n    iconName = byAlias(prefix, iconName) || iconName;\n    return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nconst library = new Library();\nconst noAuto = ()=>{\n    config.autoReplaceSvg = false;\n    config.observeMutations = false;\n    callHooks(\"noAuto\");\n};\nconst dom = {\n    i2svg: function() {\n        let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        if (IS_DOM) {\n            callHooks(\"beforeI2svg\", params);\n            callProvided(\"pseudoElements2svg\", params);\n            return callProvided(\"i2svg\", params);\n        } else {\n            return Promise.reject(new Error(\"Operation requires a DOM of some kind.\"));\n        }\n    },\n    watch: function() {\n        let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        const { autoReplaceSvgRoot } = params;\n        if (config.autoReplaceSvg === false) {\n            config.autoReplaceSvg = true;\n        }\n        config.observeMutations = true;\n        domready(()=>{\n            autoReplace({\n                autoReplaceSvgRoot\n            });\n            callHooks(\"watch\", params);\n        });\n    }\n};\nconst parse = {\n    icon: (icon)=>{\n        if (icon === null) {\n            return null;\n        }\n        if (typeof icon === \"object\" && icon.prefix && icon.iconName) {\n            return {\n                prefix: icon.prefix,\n                iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\n            };\n        }\n        if (Array.isArray(icon) && icon.length === 2) {\n            const iconName = icon[1].indexOf(\"fa-\") === 0 ? icon[1].slice(3) : icon[1];\n            const prefix = getCanonicalPrefix(icon[0]);\n            return {\n                prefix,\n                iconName: byAlias(prefix, iconName) || iconName\n            };\n        }\n        if (typeof icon === \"string\" && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n            const canonicalIcon = getCanonicalIcon(icon.split(\" \"), {\n                skipLookups: true\n            });\n            return {\n                prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n                iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n            };\n        }\n        if (typeof icon === \"string\") {\n            const prefix = getDefaultUsablePrefix();\n            return {\n                prefix,\n                iconName: byAlias(prefix, icon) || icon\n            };\n        }\n    }\n};\nconst api = {\n    noAuto,\n    config,\n    dom,\n    parse,\n    library,\n    findIconDefinition,\n    toHtml\n};\nconst autoReplace = function() {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const { autoReplaceSvgRoot = DOCUMENT } = params;\n    if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n        node: autoReplaceSvgRoot\n    });\n};\nfunction domVariants(val, abstractCreator) {\n    Object.defineProperty(val, \"abstract\", {\n        get: abstractCreator\n    });\n    Object.defineProperty(val, \"html\", {\n        get: function() {\n            return val.abstract.map((a)=>toHtml(a));\n        }\n    });\n    Object.defineProperty(val, \"node\", {\n        get: function() {\n            if (!IS_DOM) return;\n            const container = DOCUMENT.createElement(\"div\");\n            container.innerHTML = val.html;\n            return container.children;\n        }\n    });\n    return val;\n}\nfunction asIcon(_ref) {\n    let { children, main, mask, attributes, styles, transform } = _ref;\n    if (transformIsMeaningful(transform) && main.found && !mask.found) {\n        const { width, height } = main;\n        const offset = {\n            x: width / height / 2,\n            y: 0.5\n        };\n        attributes[\"style\"] = joinStyles({\n            ...styles,\n            \"transform-origin\": \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n        });\n    }\n    return [\n        {\n            tag: \"svg\",\n            attributes,\n            children\n        }\n    ];\n}\nfunction asSymbol(_ref) {\n    let { prefix, iconName, children, attributes, symbol } = _ref;\n    const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n    return [\n        {\n            tag: \"svg\",\n            attributes: {\n                style: \"display: none;\"\n            },\n            children: [\n                {\n                    tag: \"symbol\",\n                    attributes: {\n                        ...attributes,\n                        id\n                    },\n                    children\n                }\n            ]\n        }\n    ];\n}\nfunction makeInlineSvgAbstract(params) {\n    const { icons: { main, mask }, prefix, iconName, transform, symbol, title, maskId, titleId, extra, watchable = false } = params;\n    const { width, height } = mask.found ? mask : main;\n    const isUploadedIcon = prefix === \"fak\";\n    const attrClass = [\n        config.replacementClass,\n        iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : \"\"\n    ].filter((c)=>extra.classes.indexOf(c) === -1).filter((c)=>c !== \"\" || !!c).concat(extra.classes).join(\" \");\n    let content = {\n        children: [],\n        attributes: {\n            ...extra.attributes,\n            \"data-prefix\": prefix,\n            \"data-icon\": iconName,\n            \"class\": attrClass,\n            \"role\": extra.attributes.role || \"img\",\n            \"xmlns\": \"http://www.w3.org/2000/svg\",\n            \"viewBox\": \"0 0 \".concat(width, \" \").concat(height)\n        }\n    };\n    const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf(\"fa-fw\") ? {\n        width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n    } : {};\n    if (watchable) {\n        content.attributes[DATA_FA_I2SVG] = \"\";\n    }\n    if (title) {\n        content.children.push({\n            tag: \"title\",\n            attributes: {\n                id: content.attributes[\"aria-labelledby\"] || \"title-\".concat(titleId || nextUniqueId())\n            },\n            children: [\n                title\n            ]\n        });\n        delete content.attributes.title;\n    }\n    const args = {\n        ...content,\n        prefix,\n        iconName,\n        main,\n        mask,\n        maskId,\n        transform,\n        symbol,\n        styles: {\n            ...uploadedIconWidthStyle,\n            ...extra.styles\n        }\n    };\n    const { children, attributes } = mask.found && main.found ? callProvided(\"generateAbstractMask\", args) || {\n        children: [],\n        attributes: {}\n    } : callProvided(\"generateAbstractIcon\", args) || {\n        children: [],\n        attributes: {}\n    };\n    args.children = children;\n    args.attributes = attributes;\n    if (symbol) {\n        return asSymbol(args);\n    } else {\n        return asIcon(args);\n    }\n}\nfunction makeLayersTextAbstract(params) {\n    const { content, width, height, transform, title, extra, watchable = false } = params;\n    const attributes = {\n        ...extra.attributes,\n        ...title ? {\n            \"title\": title\n        } : {},\n        \"class\": extra.classes.join(\" \")\n    };\n    if (watchable) {\n        attributes[DATA_FA_I2SVG] = \"\";\n    }\n    const styles = {\n        ...extra.styles\n    };\n    if (transformIsMeaningful(transform)) {\n        styles[\"transform\"] = transformForCss({\n            transform,\n            startCentered: true,\n            width,\n            height\n        });\n        styles[\"-webkit-transform\"] = styles[\"transform\"];\n    }\n    const styleString = joinStyles(styles);\n    if (styleString.length > 0) {\n        attributes[\"style\"] = styleString;\n    }\n    const val = [];\n    val.push({\n        tag: \"span\",\n        attributes,\n        children: [\n            content\n        ]\n    });\n    if (title) {\n        val.push({\n            tag: \"span\",\n            attributes: {\n                class: \"sr-only\"\n            },\n            children: [\n                title\n            ]\n        });\n    }\n    return val;\n}\nfunction makeLayersCounterAbstract(params) {\n    const { content, title, extra } = params;\n    const attributes = {\n        ...extra.attributes,\n        ...title ? {\n            \"title\": title\n        } : {},\n        \"class\": extra.classes.join(\" \")\n    };\n    const styleString = joinStyles(extra.styles);\n    if (styleString.length > 0) {\n        attributes[\"style\"] = styleString;\n    }\n    const val = [];\n    val.push({\n        tag: \"span\",\n        attributes,\n        children: [\n            content\n        ]\n    });\n    if (title) {\n        val.push({\n            tag: \"span\",\n            attributes: {\n                class: \"sr-only\"\n            },\n            children: [\n                title\n            ]\n        });\n    }\n    return val;\n}\nconst { styles: styles$1 } = namespace;\nfunction asFoundIcon(icon) {\n    const width = icon[0];\n    const height = icon[1];\n    const [vectorData] = icon.slice(4);\n    let element = null;\n    if (Array.isArray(vectorData)) {\n        element = {\n            tag: \"g\",\n            attributes: {\n                class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n            },\n            children: [\n                {\n                    tag: \"path\",\n                    attributes: {\n                        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n                        fill: \"currentColor\",\n                        d: vectorData[0]\n                    }\n                },\n                {\n                    tag: \"path\",\n                    attributes: {\n                        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n                        fill: \"currentColor\",\n                        d: vectorData[1]\n                    }\n                }\n            ]\n        };\n    } else {\n        element = {\n            tag: \"path\",\n            attributes: {\n                fill: \"currentColor\",\n                d: vectorData\n            }\n        };\n    }\n    return {\n        found: true,\n        width,\n        height,\n        icon: element\n    };\n}\nconst missingIconResolutionMixin = {\n    found: false,\n    width: 512,\n    height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n    if (!PRODUCTION && !config.showMissingIcons && iconName) {\n        console.error('Icon with name \"'.concat(iconName, '\" and prefix \"').concat(prefix, '\" is missing.'));\n    }\n}\nfunction findIcon(iconName, prefix) {\n    let givenPrefix = prefix;\n    if (prefix === \"fa\" && config.styleDefault !== null) {\n        prefix = getDefaultUsablePrefix();\n    }\n    return new Promise((resolve, reject)=>{\n        if (givenPrefix === \"fa\") {\n            const shim = byOldName(iconName) || {};\n            iconName = shim.iconName || iconName;\n            prefix = shim.prefix || prefix;\n        }\n        if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n            const icon = styles$1[prefix][iconName];\n            return resolve(asFoundIcon(icon));\n        }\n        maybeNotifyMissing(iconName, prefix);\n        resolve({\n            ...missingIconResolutionMixin,\n            icon: config.showMissingIcons && iconName ? callProvided(\"missingIconAbstract\") || {} : {}\n        });\n    });\n}\nconst noop$1 = ()=>{};\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n    mark: noop$1,\n    measure: noop$1\n};\nconst preamble = 'FA \"6.6.0\"';\nconst begin = (name)=>{\n    p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n    return ()=>end(name);\n};\nconst end = (name)=>{\n    p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n    p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n    begin,\n    end\n};\nconst noop$2 = ()=>{};\nfunction isWatched(node) {\n    const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n    return typeof i2svg === \"string\";\n}\nfunction hasPrefixAndIcon(node) {\n    const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n    const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n    return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n    return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n    if (config.autoReplaceSvg === true) {\n        return mutators.replace;\n    }\n    const mutator = mutators[config.autoReplaceSvg];\n    return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n    return DOCUMENT.createElementNS(\"http://www.w3.org/2000/svg\", tag);\n}\nfunction createElement(tag) {\n    return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const { ceFn = abstractObj.tag === \"svg\" ? createElementNS : createElement } = params;\n    if (typeof abstractObj === \"string\") {\n        return DOCUMENT.createTextNode(abstractObj);\n    }\n    const tag = ceFn(abstractObj.tag);\n    Object.keys(abstractObj.attributes || []).forEach(function(key) {\n        tag.setAttribute(key, abstractObj.attributes[key]);\n    });\n    const children = abstractObj.children || [];\n    children.forEach(function(child) {\n        tag.appendChild(convertSVG(child, {\n            ceFn\n        }));\n    });\n    return tag;\n}\nfunction nodeAsComment(node) {\n    let comment = \" \".concat(node.outerHTML, \" \");\n    /* BEGIN.ATTRIBUTION */ comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n    /* END.ATTRIBUTION */ return comment;\n}\nconst mutators = {\n    replace: function(mutation) {\n        const node = mutation[0];\n        if (node.parentNode) {\n            mutation[1].forEach((abstract)=>{\n                node.parentNode.insertBefore(convertSVG(abstract), node);\n            });\n            if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n                let comment = DOCUMENT.createComment(nodeAsComment(node));\n                node.parentNode.replaceChild(comment, node);\n            } else {\n                node.remove();\n            }\n        }\n    },\n    nest: function(mutation) {\n        const node = mutation[0];\n        const abstract = mutation[1]; // If we already have a replaced node we do not want to continue nesting within it.\n        // Short-circuit to the standard replacement\n        if (~classArray(node).indexOf(config.replacementClass)) {\n            return mutators.replace(mutation);\n        }\n        const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n        delete abstract[0].attributes.id;\n        if (abstract[0].attributes.class) {\n            const splitClasses = abstract[0].attributes.class.split(\" \").reduce((acc, cls)=>{\n                if (cls === config.replacementClass || cls.match(forSvg)) {\n                    acc.toSvg.push(cls);\n                } else {\n                    acc.toNode.push(cls);\n                }\n                return acc;\n            }, {\n                toNode: [],\n                toSvg: []\n            });\n            abstract[0].attributes.class = splitClasses.toSvg.join(\" \");\n            if (splitClasses.toNode.length === 0) {\n                node.removeAttribute(\"class\");\n            } else {\n                node.setAttribute(\"class\", splitClasses.toNode.join(\" \"));\n            }\n        }\n        const newInnerHTML = abstract.map((a)=>toHtml(a)).join(\"\\n\");\n        node.setAttribute(DATA_FA_I2SVG, \"\");\n        node.innerHTML = newInnerHTML;\n    }\n};\nfunction performOperationSync(op) {\n    op();\n}\nfunction perform(mutations, callback) {\n    const callbackFunction = typeof callback === \"function\" ? callback : noop$2;\n    if (mutations.length === 0) {\n        callbackFunction();\n    } else {\n        let frame = performOperationSync;\n        if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n            frame = WINDOW.requestAnimationFrame || performOperationSync;\n        }\n        frame(()=>{\n            const mutator = getMutator();\n            const mark = perf.begin(\"mutate\");\n            mutations.map(mutator);\n            mark();\n            callbackFunction();\n        });\n    }\n}\nlet disabled = false;\nfunction disableObservation() {\n    disabled = true;\n}\nfunction enableObservation() {\n    disabled = false;\n}\nlet mo$1 = null;\nfunction observe(options) {\n    if (!MUTATION_OBSERVER) {\n        return;\n    }\n    if (!config.observeMutations) {\n        return;\n    }\n    const { treeCallback = noop$2, nodeCallback = noop$2, pseudoElementsCallback = noop$2, observeMutationsRoot = DOCUMENT } = options;\n    mo$1 = new MUTATION_OBSERVER((objects)=>{\n        if (disabled) return;\n        const defaultPrefix = getDefaultUsablePrefix();\n        toArray(objects).forEach((mutationRecord)=>{\n            if (mutationRecord.type === \"childList\" && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n                if (config.searchPseudoElements) {\n                    pseudoElementsCallback(mutationRecord.target);\n                }\n                treeCallback(mutationRecord.target);\n            }\n            if (mutationRecord.type === \"attributes\" && mutationRecord.target.parentNode && config.searchPseudoElements) {\n                pseudoElementsCallback(mutationRecord.target.parentNode);\n            }\n            if (mutationRecord.type === \"attributes\" && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n                if (mutationRecord.attributeName === \"class\" && hasPrefixAndIcon(mutationRecord.target)) {\n                    const { prefix, iconName } = getCanonicalIcon(classArray(mutationRecord.target));\n                    mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n                    if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n                } else if (hasBeenReplaced(mutationRecord.target)) {\n                    nodeCallback(mutationRecord.target);\n                }\n            }\n        });\n    });\n    if (!IS_DOM) return;\n    mo$1.observe(observeMutationsRoot, {\n        childList: true,\n        attributes: true,\n        characterData: true,\n        subtree: true\n    });\n}\nfunction disconnect() {\n    if (!mo$1) return;\n    mo$1.disconnect();\n}\nfunction styleParser(node) {\n    const style = node.getAttribute(\"style\");\n    let val = [];\n    if (style) {\n        val = style.split(\";\").reduce((acc, style)=>{\n            const styles = style.split(\":\");\n            const prop = styles[0];\n            const value = styles.slice(1);\n            if (prop && value.length > 0) {\n                acc[prop] = value.join(\":\").trim();\n            }\n            return acc;\n        }, {});\n    }\n    return val;\n}\nfunction classParser(node) {\n    const existingPrefix = node.getAttribute(\"data-prefix\");\n    const existingIconName = node.getAttribute(\"data-icon\");\n    const innerText = node.innerText !== undefined ? node.innerText.trim() : \"\";\n    let val = getCanonicalIcon(classArray(node));\n    if (!val.prefix) {\n        val.prefix = getDefaultUsablePrefix();\n    }\n    if (existingPrefix && existingIconName) {\n        val.prefix = existingPrefix;\n        val.iconName = existingIconName;\n    }\n    if (val.iconName && val.prefix) {\n        return val;\n    }\n    if (val.prefix && innerText.length > 0) {\n        val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n    }\n    if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n        val.iconName = node.firstChild.data;\n    }\n    return val;\n}\nfunction attributesParser(node) {\n    const extraAttributes = toArray(node.attributes).reduce((acc, attr)=>{\n        if (acc.name !== \"class\" && acc.name !== \"style\") {\n            acc[attr.name] = attr.value;\n        }\n        return acc;\n    }, {});\n    const title = node.getAttribute(\"title\");\n    const titleId = node.getAttribute(\"data-fa-title-id\");\n    if (config.autoA11y) {\n        if (title) {\n            extraAttributes[\"aria-labelledby\"] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n        } else {\n            extraAttributes[\"aria-hidden\"] = \"true\";\n            extraAttributes[\"focusable\"] = \"false\";\n        }\n    }\n    return extraAttributes;\n}\nfunction blankMeta() {\n    return {\n        iconName: null,\n        title: null,\n        titleId: null,\n        prefix: null,\n        transform: meaninglessTransform,\n        symbol: false,\n        mask: {\n            iconName: null,\n            prefix: null,\n            rest: []\n        },\n        maskId: null,\n        extra: {\n            classes: [],\n            styles: {},\n            attributes: {}\n        }\n    };\n}\nfunction parseMeta(node) {\n    let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        styleParser: true\n    };\n    const { iconName, prefix, rest: extraClasses } = classParser(node);\n    const extraAttributes = attributesParser(node);\n    const pluginMeta = chainHooks(\"parseNodeAttributes\", {}, node);\n    let extraStyles = parser.styleParser ? styleParser(node) : [];\n    return {\n        iconName,\n        title: node.getAttribute(\"title\"),\n        titleId: node.getAttribute(\"data-fa-title-id\"),\n        prefix,\n        transform: meaninglessTransform,\n        mask: {\n            iconName: null,\n            prefix: null,\n            rest: []\n        },\n        maskId: null,\n        symbol: false,\n        extra: {\n            classes: extraClasses,\n            styles: extraStyles,\n            attributes: extraAttributes\n        },\n        ...pluginMeta\n    };\n}\nconst { styles: styles$2 } = namespace;\nfunction generateMutation(node) {\n    const nodeMeta = config.autoReplaceSvg === \"nest\" ? parseMeta(node, {\n        styleParser: false\n    }) : parseMeta(node);\n    if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n        return callProvided(\"generateLayersText\", node, nodeMeta);\n    } else {\n        return callProvided(\"generateSvgReplacementMutation\", node, nodeMeta);\n    }\n}\nlet knownPrefixes = new Set();\nFAMILIES.map((family)=>{\n    knownPrefixes.add(\"fa-\".concat(family));\n});\nObject.keys(PREFIX_TO_STYLE[a]).map(knownPrefixes.add.bind(knownPrefixes));\nObject.keys(PREFIX_TO_STYLE[r]).map(knownPrefixes.add.bind(knownPrefixes));\nObject.keys(PREFIX_TO_STYLE[o]).map(knownPrefixes.add.bind(knownPrefixes));\nknownPrefixes = [\n    ...knownPrefixes\n];\nfunction onTree(root) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    if (!IS_DOM) return Promise.resolve();\n    const htmlClassList = DOCUMENT.documentElement.classList;\n    const hclAdd = (suffix)=>htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n    const hclRemove = (suffix)=>htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n    const prefixes = config.autoFetchSvg ? knownPrefixes : FAMILIES.map((f$$1)=>\"fa-\".concat(f$$1)).concat(Object.keys(styles$2));\n    if (!prefixes.includes(\"fa\")) {\n        prefixes.push(\"fa\");\n    }\n    const prefixesDomQuery = [\n        \".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")\n    ].concat(prefixes.map((p$$1)=>\".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(\", \");\n    if (prefixesDomQuery.length === 0) {\n        return Promise.resolve();\n    }\n    let candidates = [];\n    try {\n        candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n    } catch (e$$1) {}\n    if (candidates.length > 0) {\n        hclAdd(\"pending\");\n        hclRemove(\"complete\");\n    } else {\n        return Promise.resolve();\n    }\n    const mark = perf.begin(\"onTree\");\n    const mutations = candidates.reduce((acc, node)=>{\n        try {\n            const mutation = generateMutation(node);\n            if (mutation) {\n                acc.push(mutation);\n            }\n        } catch (e$$1) {\n            if (!PRODUCTION) {\n                if (e$$1.name === \"MissingIcon\") {\n                    console.error(e$$1);\n                }\n            }\n        }\n        return acc;\n    }, []);\n    return new Promise((resolve, reject)=>{\n        Promise.all(mutations).then((resolvedMutations)=>{\n            perform(resolvedMutations, ()=>{\n                hclAdd(\"active\");\n                hclAdd(\"complete\");\n                hclRemove(\"pending\");\n                if (typeof callback === \"function\") callback();\n                mark();\n                resolve();\n            });\n        }).catch((e$$1)=>{\n            mark();\n            reject(e$$1);\n        });\n    });\n}\nfunction onNode(node) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    generateMutation(node).then((mutation)=>{\n        if (mutation) {\n            perform([\n                mutation\n            ], callback);\n        }\n    });\n}\nfunction resolveIcons(next) {\n    return function(maybeIconDefinition) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n        let { mask } = params;\n        if (mask) {\n            mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n        }\n        return next(iconDefinition, {\n            ...params,\n            mask\n        });\n    };\n}\nconst render = function(iconDefinition) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const { transform = meaninglessTransform, symbol = false, mask = null, maskId = null, title = null, titleId = null, classes = [], attributes = {}, styles = {} } = params;\n    if (!iconDefinition) return;\n    const { prefix, iconName, icon } = iconDefinition;\n    return domVariants({\n        type: \"icon\",\n        ...iconDefinition\n    }, ()=>{\n        callHooks(\"beforeDOMElementCreation\", {\n            iconDefinition,\n            params\n        });\n        if (config.autoA11y) {\n            if (title) {\n                attributes[\"aria-labelledby\"] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n            } else {\n                attributes[\"aria-hidden\"] = \"true\";\n                attributes[\"focusable\"] = \"false\";\n            }\n        }\n        return makeInlineSvgAbstract({\n            icons: {\n                main: asFoundIcon(icon),\n                mask: mask ? asFoundIcon(mask.icon) : {\n                    found: false,\n                    width: null,\n                    height: null,\n                    icon: {}\n                }\n            },\n            prefix,\n            iconName,\n            transform: {\n                ...meaninglessTransform,\n                ...transform\n            },\n            symbol,\n            title,\n            maskId,\n            titleId,\n            extra: {\n                attributes,\n                styles,\n                classes\n            }\n        });\n    });\n};\nvar ReplaceElements = {\n    mixout () {\n        return {\n            icon: resolveIcons(render)\n        };\n    },\n    hooks () {\n        return {\n            mutationObserverCallbacks (accumulator) {\n                accumulator.treeCallback = onTree;\n                accumulator.nodeCallback = onNode;\n                return accumulator;\n            }\n        };\n    },\n    provides (providers$$1) {\n        providers$$1.i2svg = function(params) {\n            const { node = DOCUMENT, callback = ()=>{} } = params;\n            return onTree(node, callback);\n        };\n        providers$$1.generateSvgReplacementMutation = function(node, nodeMeta) {\n            const { iconName, title, titleId, prefix, transform, symbol, mask, maskId, extra } = nodeMeta;\n            return new Promise((resolve, reject)=>{\n                Promise.all([\n                    findIcon(iconName, prefix),\n                    mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n                        found: false,\n                        width: 512,\n                        height: 512,\n                        icon: {}\n                    })\n                ]).then((_ref)=>{\n                    let [main, mask] = _ref;\n                    resolve([\n                        node,\n                        makeInlineSvgAbstract({\n                            icons: {\n                                main,\n                                mask\n                            },\n                            prefix,\n                            iconName,\n                            transform,\n                            symbol,\n                            maskId,\n                            title,\n                            titleId,\n                            extra,\n                            watchable: true\n                        })\n                    ]);\n                }).catch(reject);\n            });\n        };\n        providers$$1.generateAbstractIcon = function(_ref2) {\n            let { children, attributes, main, transform, styles } = _ref2;\n            const styleString = joinStyles(styles);\n            if (styleString.length > 0) {\n                attributes[\"style\"] = styleString;\n            }\n            let nextChild;\n            if (transformIsMeaningful(transform)) {\n                nextChild = callProvided(\"generateAbstractTransformGrouping\", {\n                    main,\n                    transform,\n                    containerWidth: main.width,\n                    iconWidth: main.width\n                });\n            }\n            children.push(nextChild || main.icon);\n            return {\n                children,\n                attributes\n            };\n        };\n    }\n};\nvar Layers = {\n    mixout () {\n        return {\n            layer (assembler) {\n                let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n                const { classes = [] } = params;\n                return domVariants({\n                    type: \"layer\"\n                }, ()=>{\n                    callHooks(\"beforeDOMElementCreation\", {\n                        assembler,\n                        params\n                    });\n                    let children = [];\n                    assembler((args)=>{\n                        Array.isArray(args) ? args.map((a)=>{\n                            children = children.concat(a.abstract);\n                        }) : children = children.concat(args.abstract);\n                    });\n                    return [\n                        {\n                            tag: \"span\",\n                            attributes: {\n                                class: [\n                                    \"\".concat(config.cssPrefix, \"-layers\"),\n                                    ...classes\n                                ].join(\" \")\n                            },\n                            children\n                        }\n                    ];\n                });\n            }\n        };\n    }\n};\nvar LayersCounter = {\n    mixout () {\n        return {\n            counter (content) {\n                let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n                const { title = null, classes = [], attributes = {}, styles = {} } = params;\n                return domVariants({\n                    type: \"counter\",\n                    content\n                }, ()=>{\n                    callHooks(\"beforeDOMElementCreation\", {\n                        content,\n                        params\n                    });\n                    return makeLayersCounterAbstract({\n                        content: content.toString(),\n                        title,\n                        extra: {\n                            attributes,\n                            styles,\n                            classes: [\n                                \"\".concat(config.cssPrefix, \"-layers-counter\"),\n                                ...classes\n                            ]\n                        }\n                    });\n                });\n            }\n        };\n    }\n};\nvar LayersText = {\n    mixout () {\n        return {\n            text (content) {\n                let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n                const { transform = meaninglessTransform, title = null, classes = [], attributes = {}, styles = {} } = params;\n                return domVariants({\n                    type: \"text\",\n                    content\n                }, ()=>{\n                    callHooks(\"beforeDOMElementCreation\", {\n                        content,\n                        params\n                    });\n                    return makeLayersTextAbstract({\n                        content,\n                        transform: {\n                            ...meaninglessTransform,\n                            ...transform\n                        },\n                        title,\n                        extra: {\n                            attributes,\n                            styles,\n                            classes: [\n                                \"\".concat(config.cssPrefix, \"-layers-text\"),\n                                ...classes\n                            ]\n                        }\n                    });\n                });\n            }\n        };\n    },\n    provides (providers$$1) {\n        providers$$1.generateLayersText = function(node, nodeMeta) {\n            const { title, transform, extra } = nodeMeta;\n            let width = null;\n            let height = null;\n            if (IS_IE) {\n                const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n                const boundingClientRect = node.getBoundingClientRect();\n                width = boundingClientRect.width / computedFontSize;\n                height = boundingClientRect.height / computedFontSize;\n            }\n            if (config.autoA11y && !title) {\n                extra.attributes[\"aria-hidden\"] = \"true\";\n            }\n            return Promise.resolve([\n                node,\n                makeLayersTextAbstract({\n                    content: node.innerHTML,\n                    width,\n                    height,\n                    transform,\n                    title,\n                    extra,\n                    watchable: true\n                })\n            ]);\n        };\n    }\n};\nconst CLEAN_CONTENT_PATTERN = new RegExp('\"', \"ug\");\nconst SECONDARY_UNICODE_RANGE = [\n    1105920,\n    1112319\n];\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = {\n    ...{\n        FontAwesome: {\n            normal: \"fas\",\n            400: \"fas\"\n        }\n    },\n    ...eo,\n    ...ao,\n    ...mo\n};\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key)=>{\n    acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n    return acc;\n}, {});\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily)=>{\n    const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n    acc[fontFamily] = weights[900] || [\n        ...Object.entries(weights)\n    ][0][1];\n    return acc;\n}, {});\nfunction hexValueFromContent(content) {\n    const cleaned = content.replace(CLEAN_CONTENT_PATTERN, \"\");\n    const codePoint = codePointAt(cleaned, 0);\n    const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n    const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n    return {\n        value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n        isSecondary: isPrependTen || isDoubled\n    };\n}\nfunction getPrefix(fontFamily, fontWeight) {\n    const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, \"\").toLowerCase();\n    const fontWeightInteger = parseInt(fontWeight);\n    const fontWeightSanitized = isNaN(fontWeightInteger) ? \"normal\" : fontWeightInteger;\n    return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n    const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(\":\", \"-\"));\n    return new Promise((resolve, reject)=>{\n        if (node.getAttribute(pendingAttribute) !== null) {\n            // This node is already being processed\n            return resolve();\n        }\n        const children = toArray(node.children);\n        const alreadyProcessedPseudoElement = children.filter((c)=>c.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\n        const styles = WINDOW.getComputedStyle(node, position);\n        const fontFamily = styles.getPropertyValue(\"font-family\");\n        const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n        const fontWeight = styles.getPropertyValue(\"font-weight\");\n        const content = styles.getPropertyValue(\"content\");\n        if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n            // If we've already processed it but the current computed style does not result in a font-family,\n            // that probably means that a class name that was previously present to make the icon has been\n            // removed. So we now should delete the icon.\n            node.removeChild(alreadyProcessedPseudoElement);\n            return resolve();\n        } else if (fontFamilyMatch && content !== \"none\" && content !== \"\") {\n            const content = styles.getPropertyValue(\"content\");\n            let prefix = getPrefix(fontFamily, fontWeight);\n            const { value: hexValue, isSecondary } = hexValueFromContent(content);\n            const isV4 = fontFamilyMatch[0].startsWith(\"FontAwesome\");\n            let iconName = byUnicode(prefix, hexValue);\n            let iconIdentifier = iconName;\n            if (isV4) {\n                const iconName4 = byOldUnicode(hexValue);\n                if (iconName4.iconName && iconName4.prefix) {\n                    iconName = iconName4.iconName;\n                    prefix = iconName4.prefix;\n                }\n            } // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n            // already done so with the same prefix and iconName\n            if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n                node.setAttribute(pendingAttribute, iconIdentifier);\n                if (alreadyProcessedPseudoElement) {\n                    // Delete the old one, since we're replacing it with a new one\n                    node.removeChild(alreadyProcessedPseudoElement);\n                }\n                const meta = blankMeta();\n                const { extra } = meta;\n                extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n                findIcon(iconName, prefix).then((main)=>{\n                    const abstract = makeInlineSvgAbstract({\n                        ...meta,\n                        icons: {\n                            main,\n                            mask: emptyCanonicalIcon()\n                        },\n                        prefix,\n                        iconName: iconIdentifier,\n                        extra,\n                        watchable: true\n                    });\n                    const element = DOCUMENT.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n                    if (position === \"::before\") {\n                        node.insertBefore(element, node.firstChild);\n                    } else {\n                        node.appendChild(element);\n                    }\n                    element.outerHTML = abstract.map((a)=>toHtml(a)).join(\"\\n\");\n                    node.removeAttribute(pendingAttribute);\n                    resolve();\n                }).catch(reject);\n            } else {\n                resolve();\n            }\n        } else {\n            resolve();\n        }\n    });\n}\nfunction replace(node) {\n    return Promise.all([\n        replaceForPosition(node, \"::before\"),\n        replaceForPosition(node, \"::after\")\n    ]);\n}\nfunction processable(node) {\n    return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== \"svg\");\n}\nfunction searchPseudoElements(root) {\n    if (!IS_DOM) return;\n    return new Promise((resolve, reject)=>{\n        const operations = toArray(root.querySelectorAll(\"*\")).filter(processable).map(replace);\n        const end = perf.begin(\"searchPseudoElements\");\n        disableObservation();\n        Promise.all(operations).then(()=>{\n            end();\n            enableObservation();\n            resolve();\n        }).catch(()=>{\n            end();\n            enableObservation();\n            reject();\n        });\n    });\n}\nvar PseudoElements = {\n    hooks () {\n        return {\n            mutationObserverCallbacks (accumulator) {\n                accumulator.pseudoElementsCallback = searchPseudoElements;\n                return accumulator;\n            }\n        };\n    },\n    provides (providers) {\n        providers.pseudoElements2svg = function(params) {\n            const { node = DOCUMENT } = params;\n            if (config.searchPseudoElements) {\n                searchPseudoElements(node);\n            }\n        };\n    }\n};\nlet _unwatched = false;\nvar MutationObserver$1 = {\n    mixout () {\n        return {\n            dom: {\n                unwatch () {\n                    disableObservation();\n                    _unwatched = true;\n                }\n            }\n        };\n    },\n    hooks () {\n        return {\n            bootstrap () {\n                observe(chainHooks(\"mutationObserverCallbacks\", {}));\n            },\n            noAuto () {\n                disconnect();\n            },\n            watch (params) {\n                const { observeMutationsRoot } = params;\n                if (_unwatched) {\n                    enableObservation();\n                } else {\n                    observe(chainHooks(\"mutationObserverCallbacks\", {\n                        observeMutationsRoot\n                    }));\n                }\n            }\n        };\n    }\n};\nconst parseTransformString = (transformString)=>{\n    let transform = {\n        size: 16,\n        x: 0,\n        y: 0,\n        flipX: false,\n        flipY: false,\n        rotate: 0\n    };\n    return transformString.toLowerCase().split(\" \").reduce((acc, n)=>{\n        const parts = n.toLowerCase().split(\"-\");\n        const first = parts[0];\n        let rest = parts.slice(1).join(\"-\");\n        if (first && rest === \"h\") {\n            acc.flipX = true;\n            return acc;\n        }\n        if (first && rest === \"v\") {\n            acc.flipY = true;\n            return acc;\n        }\n        rest = parseFloat(rest);\n        if (isNaN(rest)) {\n            return acc;\n        }\n        switch(first){\n            case \"grow\":\n                acc.size = acc.size + rest;\n                break;\n            case \"shrink\":\n                acc.size = acc.size - rest;\n                break;\n            case \"left\":\n                acc.x = acc.x - rest;\n                break;\n            case \"right\":\n                acc.x = acc.x + rest;\n                break;\n            case \"up\":\n                acc.y = acc.y - rest;\n                break;\n            case \"down\":\n                acc.y = acc.y + rest;\n                break;\n            case \"rotate\":\n                acc.rotate = acc.rotate + rest;\n                break;\n        }\n        return acc;\n    }, transform);\n};\nvar PowerTransforms = {\n    mixout () {\n        return {\n            parse: {\n                transform: (transformString)=>{\n                    return parseTransformString(transformString);\n                }\n            }\n        };\n    },\n    hooks () {\n        return {\n            parseNodeAttributes (accumulator, node) {\n                const transformString = node.getAttribute(\"data-fa-transform\");\n                if (transformString) {\n                    accumulator.transform = parseTransformString(transformString);\n                }\n                return accumulator;\n            }\n        };\n    },\n    provides (providers) {\n        providers.generateAbstractTransformGrouping = function(_ref) {\n            let { main, transform, containerWidth, iconWidth } = _ref;\n            const outer = {\n                transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n            };\n            const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n            const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n            const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n            const inner = {\n                transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n            };\n            const path = {\n                transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n            };\n            const operations = {\n                outer,\n                inner,\n                path\n            };\n            return {\n                tag: \"g\",\n                attributes: {\n                    ...operations.outer\n                },\n                children: [\n                    {\n                        tag: \"g\",\n                        attributes: {\n                            ...operations.inner\n                        },\n                        children: [\n                            {\n                                tag: main.icon.tag,\n                                children: main.icon.children,\n                                attributes: {\n                                    ...main.icon.attributes,\n                                    ...operations.path\n                                }\n                            }\n                        ]\n                    }\n                ]\n            };\n        };\n    }\n};\nconst ALL_SPACE = {\n    x: 0,\n    y: 0,\n    width: \"100%\",\n    height: \"100%\"\n};\nfunction fillBlack(abstract) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    if (abstract.attributes && (abstract.attributes.fill || force)) {\n        abstract.attributes.fill = \"black\";\n    }\n    return abstract;\n}\nfunction deGroup(abstract) {\n    if (abstract.tag === \"g\") {\n        return abstract.children;\n    } else {\n        return [\n            abstract\n        ];\n    }\n}\nvar Masks = {\n    hooks () {\n        return {\n            parseNodeAttributes (accumulator, node) {\n                const maskData = node.getAttribute(\"data-fa-mask\");\n                const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(\" \").map((i)=>i.trim()));\n                if (!mask.prefix) {\n                    mask.prefix = getDefaultUsablePrefix();\n                }\n                accumulator.mask = mask;\n                accumulator.maskId = node.getAttribute(\"data-fa-mask-id\");\n                return accumulator;\n            }\n        };\n    },\n    provides (providers) {\n        providers.generateAbstractMask = function(_ref) {\n            let { children, attributes, main, mask, maskId: explicitMaskId, transform } = _ref;\n            const { width: mainWidth, icon: mainPath } = main;\n            const { width: maskWidth, icon: maskPath } = mask;\n            const trans = transformForSvg({\n                transform,\n                containerWidth: maskWidth,\n                iconWidth: mainWidth\n            });\n            const maskRect = {\n                tag: \"rect\",\n                attributes: {\n                    ...ALL_SPACE,\n                    fill: \"white\"\n                }\n            };\n            const maskInnerGroupChildrenMixin = mainPath.children ? {\n                children: mainPath.children.map(fillBlack)\n            } : {};\n            const maskInnerGroup = {\n                tag: \"g\",\n                attributes: {\n                    ...trans.inner\n                },\n                children: [\n                    fillBlack({\n                        tag: mainPath.tag,\n                        attributes: {\n                            ...mainPath.attributes,\n                            ...trans.path\n                        },\n                        ...maskInnerGroupChildrenMixin\n                    })\n                ]\n            };\n            const maskOuterGroup = {\n                tag: \"g\",\n                attributes: {\n                    ...trans.outer\n                },\n                children: [\n                    maskInnerGroup\n                ]\n            };\n            const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n            const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n            const maskTag = {\n                tag: \"mask\",\n                attributes: {\n                    ...ALL_SPACE,\n                    id: maskId,\n                    maskUnits: \"userSpaceOnUse\",\n                    maskContentUnits: \"userSpaceOnUse\"\n                },\n                children: [\n                    maskRect,\n                    maskOuterGroup\n                ]\n            };\n            const defs = {\n                tag: \"defs\",\n                children: [\n                    {\n                        tag: \"clipPath\",\n                        attributes: {\n                            id: clipId\n                        },\n                        children: deGroup(maskPath)\n                    },\n                    maskTag\n                ]\n            };\n            children.push(defs, {\n                tag: \"rect\",\n                attributes: {\n                    fill: \"currentColor\",\n                    \"clip-path\": \"url(#\".concat(clipId, \")\"),\n                    mask: \"url(#\".concat(maskId, \")\"),\n                    ...ALL_SPACE\n                }\n            });\n            return {\n                children,\n                attributes\n            };\n        };\n    }\n};\nvar MissingIconIndicator = {\n    provides (providers) {\n        let reduceMotion = false;\n        if (WINDOW.matchMedia) {\n            reduceMotion = WINDOW.matchMedia(\"(prefers-reduced-motion: reduce)\").matches;\n        }\n        providers.missingIconAbstract = function() {\n            const gChildren = [];\n            const FILL = {\n                fill: \"currentColor\"\n            };\n            const ANIMATION_BASE = {\n                attributeType: \"XML\",\n                repeatCount: \"indefinite\",\n                dur: \"2s\"\n            }; // Ring\n            gChildren.push({\n                tag: \"path\",\n                attributes: {\n                    ...FILL,\n                    d: \"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z\"\n                }\n            });\n            const OPACITY_ANIMATE = {\n                ...ANIMATION_BASE,\n                attributeName: \"opacity\"\n            };\n            const dot = {\n                tag: \"circle\",\n                attributes: {\n                    ...FILL,\n                    cx: \"256\",\n                    cy: \"364\",\n                    r: \"28\"\n                },\n                children: []\n            };\n            if (!reduceMotion) {\n                dot.children.push({\n                    tag: \"animate\",\n                    attributes: {\n                        ...ANIMATION_BASE,\n                        attributeName: \"r\",\n                        values: \"28;14;28;28;14;28;\"\n                    }\n                }, {\n                    tag: \"animate\",\n                    attributes: {\n                        ...OPACITY_ANIMATE,\n                        values: \"1;0;1;1;0;1;\"\n                    }\n                });\n            }\n            gChildren.push(dot);\n            gChildren.push({\n                tag: \"path\",\n                attributes: {\n                    ...FILL,\n                    opacity: \"1\",\n                    d: \"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z\"\n                },\n                children: reduceMotion ? [] : [\n                    {\n                        tag: \"animate\",\n                        attributes: {\n                            ...OPACITY_ANIMATE,\n                            values: \"1;0;0;0;0;1;\"\n                        }\n                    }\n                ]\n            });\n            if (!reduceMotion) {\n                // Exclamation\n                gChildren.push({\n                    tag: \"path\",\n                    attributes: {\n                        ...FILL,\n                        opacity: \"0\",\n                        d: \"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z\"\n                    },\n                    children: [\n                        {\n                            tag: \"animate\",\n                            attributes: {\n                                ...OPACITY_ANIMATE,\n                                values: \"0;0;1;1;0;0;\"\n                            }\n                        }\n                    ]\n                });\n            }\n            return {\n                tag: \"g\",\n                attributes: {\n                    \"class\": \"missing\"\n                },\n                children: gChildren\n            };\n        };\n    }\n};\nvar SvgSymbols = {\n    hooks () {\n        return {\n            parseNodeAttributes (accumulator, node) {\n                const symbolData = node.getAttribute(\"data-fa-symbol\");\n                const symbol = symbolData === null ? false : symbolData === \"\" ? true : symbolData;\n                accumulator[\"symbol\"] = symbol;\n                return accumulator;\n            }\n        };\n    }\n};\nvar plugins = [\n    InjectCSS,\n    ReplaceElements,\n    Layers,\n    LayersCounter,\n    LayersText,\n    PseudoElements,\n    MutationObserver$1,\n    PowerTransforms,\n    Masks,\n    MissingIconIndicator,\n    SvgSymbols\n];\nregisterPlugins(plugins, {\n    mixoutsTo: api\n});\nconst noAuto$1 = api.noAuto;\nconst config$1 = api.config;\nconst library$1 = api.library;\nconst dom$1 = api.dom;\nconst parse$1 = api.parse;\nconst findIconDefinition$1 = api.findIconDefinition;\nconst toHtml$1 = api.toHtml;\nconst icon = api.icon;\nconst layer = api.layer;\nconst text = api.text;\nconst counter = api.counter;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\n");

/***/ })

};
;