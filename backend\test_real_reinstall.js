/**
 * Script de test pour la réinstallation réelle avec l'API Contabo
 * ⚠️ ATTENTION: Ce script effectue une VRAIE réinstallation sur Contabo
 * Utilisez uniquement avec des instances de test !
 */

const axios = require('axios');
const ContaboProvider = require('./services/providers/ContaboProvider');
require('dotenv').config();

const BASE_URL = 'http://localhost:5002';

async function testRealReinstall() {
  console.log('🚀 Test de réinstallation réelle avec l\'API Contabo\n');
  console.log('⚠️  ATTENTION: Ce test effectue une VRAIE réinstallation !');
  console.log('📋 Utilisez uniquement avec des instances de test\n');

  try {
    // 1. Initialiser le provider Contabo
    console.log('1️⃣ Initialisation du provider Contabo...');
    const provider = new ContaboProvider();
    await provider.authenticate();
    console.log('✅ Authentification Contabo réussie\n');

    // 2. Récupérer les instances VPS disponibles
    console.log('2️⃣ Récupération des instances VPS...');
    const instances = await provider.getCustomerVPS('test-user');
    console.log(`✅ ${instances.length} instances trouvées\n`);

    if (instances.length === 0) {
      console.log('❌ Aucune instance VPS trouvée pour les tests');
      return;
    }

    // 3. Afficher les instances disponibles
    console.log('📋 Instances disponibles:');
    instances.forEach((instance, index) => {
      console.log(`   ${index + 1}. ${instance.name} (ID: ${instance.id}) - Status: ${instance.status}`);
    });
    console.log('');

    // 4. Récupérer les images disponibles
    console.log('3️⃣ Récupération des images disponibles...');
    const imagesResult = await provider.getAvailableImages();
    const images = imagesResult.data || [];
    console.log(`✅ ${images.length} images disponibles\n`);

    if (images.length === 0) {
      console.log('❌ Aucune image disponible pour les tests');
      return;
    }

    // 5. Afficher quelques images populaires
    console.log('📋 Images populaires disponibles:');
    const popularImages = images.filter(img => 
      img.name && (
        img.name.toLowerCase().includes('ubuntu') ||
        img.name.toLowerCase().includes('centos') ||
        img.name.toLowerCase().includes('debian')
      )
    ).slice(0, 5);

    popularImages.forEach((image, index) => {
      console.log(`   ${index + 1}. ${image.name} (ID: ${image.imageId || image.id})`);
    });
    console.log('');

    // 6. Simulation de réinstallation (commenté pour sécurité)
    console.log('4️⃣ Simulation de réinstallation...');
    console.log('⚠️  La réinstallation réelle est commentée pour sécurité');
    console.log('');

    const testInstanceId = instances[0].id;
    const testImageId = popularImages[0]?.imageId || popularImages[0]?.id;

    console.log(`📋 Instance de test: ${testInstanceId}`);
    console.log(`📋 Image de test: ${testImageId}`);
    console.log('');

    // DÉCOMMENTEZ CETTE SECTION POUR EFFECTUER UNE VRAIE RÉINSTALLATION
    /*
    const reinstallData = {
      imageId: testImageId,
      password: 'TestPassword123!',
      enableRootUser: true
    };

    console.log('🔄 Démarrage de la réinstallation...');
    console.log('📤 Données:', JSON.stringify(reinstallData, null, 2));

    const result = await provider.reinstallVPS(testInstanceId, reinstallData);
    console.log('✅ Réinstallation initiée avec succès !');
    console.log('📋 Résultat:', result);
    */

    console.log('✅ Test terminé avec succès !');
    console.log('');
    console.log('📝 Pour effectuer une vraie réinstallation:');
    console.log('   1. Décommentez la section de réinstallation dans ce script');
    console.log('   2. Vérifiez que vous utilisez une instance de test');
    console.log('   3. Relancez le script');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    if (error.response) {
      console.error('📋 Détails de l\'erreur:', error.response.data);
    }
  }
}

// Test via l'API REST (comme le frontend)
async function testViaAPI() {
  console.log('\n' + '='.repeat(50));
  console.log('🌐 Test via l\'API REST (comme le frontend)\n');

  try {
    // Test avec des données de réinstallation valides
    const reinstallData = {
      imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb', // Ubuntu 22.04 LTS
      password: 'SecurePassword123!',
      enableRootUser: true
    };

    console.log('📤 Test de validation des données...');
    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/test-instance-id/reinstall`,
      reinstallData,
      {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      }
    );

    console.log('✅ Réponse API:', response.status);
    console.log('📋 Message:', response.data.message || response.data);

  } catch (error) {
    console.error('❌ Erreur API:', error.message);
  }
}

// Exécuter les tests
if (require.main === module) {
  testRealReinstall()
    .then(() => testViaAPI())
    .catch(console.error);
}

module.exports = { testRealReinstall, testViaAPI };
