#!/usr/bin/env node

/**
 * Script to list recent orders for testing
 * Usage: node listRecentOrders.js [limit]
 * Example: node listRecentOrders.js 10
 */

const mongoose = require("mongoose");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../.env") });

// Import models
const Order = require("../models/Order");
const User = require("../models/User");
const Package = require("../models/Package");
const SubOrder = require("../models/SubOrder");

// Connect to database
async function connectDB() {
  try {
    // Use local MongoDB with zn_tech database
    const localMongoUri = "mongodb://localhost:27017/zn_ztech";

    await mongoose.connect(localMongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to local MongoDB (zn_ztech database)");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    console.error("Make sure MongoDB is running locally on port 27017");
    process.exit(1);
  }
}

// Function to list recent orders
async function listRecentOrders(limit = 20) {
  try {
    console.log(`📋 Fetching ${limit} most recent orders...\n`);

    const orders = await Order.find()
      .populate("user", "firstName lastName email")
      .populate({
        path: "subOrders",
        populate: { path: "package", model: "Package" },
      })
      .sort({ createdAt: -1 })
      .limit(limit);

    if (orders.length === 0) {
      console.log("No orders found");
      return;
    }

    console.log(`Found ${orders.length} orders:\n`);
    console.log("═".repeat(120));

    orders.forEach((order, index) => {
      const hasVPS = order.subOrders.some((subOrder) => subOrder.vps);
      const vpsIcon = hasVPS ? "🖥️ " : "📦";
      const paidIcon = order.isPaid ? "✅" : "❌";

      console.log(`${index + 1}. ${vpsIcon} ${order.identifiant} ${paidIcon}`);
      console.log(`   ID: ${order._id}`);
      console.log(
        `   User: ${order.user.firstName} ${order.user.lastName} (${order.user.email})`
      );
      console.log(
        `   Status: ${order.status} | Paid: ${order.isPaid} | Price: ${
          order.totalPrice
        } ${order.currency || "MAD"}`
      );
      console.log(`   Created: ${order.createdAt.toLocaleString()}`);

      if (order.isPaid && order.datePaid) {
        console.log(`   Paid Date: ${order.datePaid.toLocaleString()}`);
      }

      if (order.transactionId) {
        console.log(`   Transaction: ${order.transactionId}`);
      }

      // Show suborders info
      if (order.subOrders.length > 0) {
        console.log(`   SubOrders (${order.subOrders.length}):`);
        order.subOrders.forEach((subOrder, subIndex) => {
          const packageName = subOrder.package?.name || "Unknown Package";
          console.log(
            `     ${subIndex + 1}. ${packageName} - ${subOrder.status}`
          );

          if (subOrder.vps) {
            console.log(
              `        VPS: ${subOrder.vps.provider} | ${subOrder.vps.planId} | ${subOrder.vps.status}`
            );
            console.log(
              `        Region: ${subOrder.vps.region} | OS: ${subOrder.vps.operatingSystem}`
            );
            if (subOrder.vps.ipAddress) {
              console.log(`        IP: ${subOrder.vps.ipAddress}`);
            }
          }
        });
      }

      console.log("─".repeat(120));
    });

    console.log("\n💡 To mark an order as paid, use:");
    console.log("   node markOrderAsPaid.js <ORDER_ID_OR_IDENTIFIANT>");
    console.log("   node quickPayOrder.js <ORDER_ID_OR_IDENTIFIANT>");

    console.log("\n🖥️  VPS Orders | 📦 Regular Orders | ✅ Paid | ❌ Unpaid");
  } catch (error) {
    console.error("❌ Error listing orders:", error);
    return false;
  }
}

// Main execution
async function main() {
  const limit = parseInt(process.argv[2]) || 20;

  if (limit < 1 || limit > 100) {
    console.error("❌ Limit must be between 1 and 100");
    process.exit(1);
  }

  await connectDB();

  await listRecentOrders(limit);

  // Close database connection
  await mongoose.connection.close();
  console.log("\n🔌 Database connection closed");
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script execution failed:", error);
    process.exit(1);
  });
}

module.exports = { listRecentOrders };
