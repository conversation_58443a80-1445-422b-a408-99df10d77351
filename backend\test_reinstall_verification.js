/**
 * Test complet de vérification de réinstallation
 * Vérifie si la réinstallation s'est bien effectuée
 */

const ContaboProvider = require('./services/providers/ContaboProvider');
const { exec } = require('child_process');
const { promisify } = require('util');
require('dotenv').config();

const execAsync = promisify(exec);

async function testReinstallVerification() {
  console.log('🧪 TEST COMPLET DE VÉRIFICATION DE RÉINSTALLATION');
  console.log('=' .repeat(60));
  console.log('');

  const INSTANCE_ID = '202718127';
  const NEW_PASSWORD = 'bpri,O1Fm?r9u@VI';
  const EXPECTED_IMAGE = 'afecbb85-e2fc-46f0-9684-b46b1faf00bb'; // Image utilisée
  const EXPECTED_APP = '6097fd2c-378d-4132-ada4-f3e6ab20a2a5'; // Application utilisée

  try {
    // 1. Vérifier le statut via API Contabo
    console.log('1️⃣ VÉRIFICATION DU STATUT VIA API CONTABO');
    console.log('-'.repeat(50));
    
    const provider = new ContaboProvider();
    await provider.authenticate();
    console.log('✅ Authentification Contabo réussie');

    const instances = await provider.getCustomerVPS('test-user');
    const instance = instances.find(vps => vps.id.toString() === INSTANCE_ID);

    if (!instance) {
      console.log('❌ Instance non trouvée');
      return false;
    }

    console.log(`📋 Nom: ${instance.name}`);
    console.log(`📋 Statut: ${instance.status}`);
    console.log(`📋 IP: ${instance.ip}`);
    console.log(`📋 Région: ${instance.region}`);
    console.log('');

    // 2. Analyser le statut
    console.log('2️⃣ ANALYSE DU STATUT');
    console.log('-'.repeat(50));

    const status = instance.status.toLowerCase();
    let isReinstallComplete = false;

    switch (status) {
      case 'running':
        console.log('✅ Instance en cours d\'exécution');
        console.log('✅ Réinstallation probablement terminée');
        isReinstallComplete = true;
        break;
      case 'creating':
      case 'installing':
      case 'provisioning':
        console.log('⏳ Réinstallation encore en cours...');
        console.log('⏳ Attendez quelques minutes et relancez le test');
        isReinstallComplete = false;
        break;
      case 'stopped':
        console.log('⚠️ Instance arrêtée');
        console.log('⚠️ Peut nécessiter un redémarrage');
        isReinstallComplete = false;
        break;
      default:
        console.log(`❓ Statut inconnu: ${status}`);
        isReinstallComplete = false;
    }
    console.log('');

    // 3. Test de connectivité réseau
    console.log('3️⃣ TEST DE CONNECTIVITÉ RÉSEAU');
    console.log('-'.repeat(50));

    if (instance.ip) {
      try {
        console.log(`📡 Test de ping vers ${instance.ip}...`);
        
        // Test ping (Windows)
        const pingResult = await execAsync(`ping -n 4 ${instance.ip}`, { timeout: 10000 });
        
        if (pingResult.stdout.includes('TTL=') || pingResult.stdout.includes('temps=')) {
          console.log('✅ Ping réussi - Serveur accessible');
        } else {
          console.log('⚠️ Ping échoué - Serveur peut-être en cours de démarrage');
        }
      } catch (error) {
        console.log('⚠️ Test de ping échoué:', error.message);
      }
    }
    console.log('');

    // 4. Vérification des paramètres de réinstallation
    console.log('4️⃣ VÉRIFICATION DES PARAMÈTRES');
    console.log('-'.repeat(50));
    console.log(`📋 Image demandée: ${EXPECTED_IMAGE}`);
    console.log(`📋 Application demandée: ${EXPECTED_APP}`);
    console.log(`📋 Mot de passe: ${NEW_PASSWORD}`);
    console.log('');

    // 5. Recommandations finales
    console.log('5️⃣ RECOMMANDATIONS FINALES');
    console.log('-'.repeat(50));

    if (isReinstallComplete && status === 'running') {
      console.log('🎉 RÉINSTALLATION TERMINÉE AVEC SUCCÈS !');
      console.log('');
      console.log('📝 PROCHAINES ÉTAPES:');
      console.log(`   1. Testez SSH: ssh root@${instance.ip}`);
      console.log(`   2. Utilisez le mot de passe: ${NEW_PASSWORD}`);
      console.log('   3. Vérifiez l\'OS installé: cat /etc/os-release');
      console.log('   4. Vérifiez l\'application installée si applicable');
      console.log('');
      console.log('🔧 COMMANDES DE TEST:');
      console.log(`   ping ${instance.ip}`);
      console.log(`   ssh root@${instance.ip}`);
      console.log('   # Une fois connecté:');
      console.log('   cat /etc/os-release');
      console.log('   uname -a');
      console.log('   df -h');
      console.log('   free -h');
    } else {
      console.log('⏳ RÉINSTALLATION EN COURS OU PROBLÈME DÉTECTÉ');
      console.log('');
      console.log('📝 ACTIONS RECOMMANDÉES:');
      console.log('   1. Attendez 5-10 minutes supplémentaires');
      console.log('   2. Relancez ce script de vérification');
      console.log('   3. Vérifiez le panel Contabo si le problème persiste');
      console.log('   4. Contactez le support Contabo si nécessaire');
    }

    console.log('');
    console.log('🔗 LIENS UTILES:');
    console.log('   • Panel Contabo: https://my.contabo.com');
    console.log('   • Documentation SSH: https://docs.contabo.com');
    console.log('');

    return {
      success: true,
      isComplete: isReinstallComplete,
      status: status,
      instance: instance,
      recommendations: isReinstallComplete ? 'test_ssh' : 'wait_and_retry'
    };

  } catch (error) {
    console.error('❌ ERREUR LORS DE LA VÉRIFICATION:', error.message);
    console.log('');
    console.log('📝 ACTIONS EN CAS D\'ERREUR:');
    console.log('   1. Vérifiez votre connexion internet');
    console.log('   2. Vérifiez les credentials Contabo dans .env');
    console.log('   3. Consultez directement le panel Contabo');
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Fonction pour tester SSH (simulation)
function getSSHTestInstructions(ip, password) {
  return `
🔧 INSTRUCTIONS POUR TESTER SSH:

1. Ouvrez un terminal/PowerShell
2. Exécutez: ssh root@${ip}
3. Entrez le mot de passe: ${password}
4. Si la connexion réussit, exécutez:
   - cat /etc/os-release (pour voir l'OS)
   - uname -a (pour voir le kernel)
   - df -h (pour voir l'espace disque)
   - free -h (pour voir la RAM)

Si SSH échoue:
- Attendez quelques minutes (le service SSH peut mettre du temps à démarrer)
- Vérifiez que le port 22 est ouvert
- Consultez le panel Contabo pour les logs
`;
}

// Exécuter le test
if (require.main === module) {
  testReinstallVerification()
    .then(result => {
      if (result.success && result.isComplete) {
        console.log('🎯 RÉSULTAT: Réinstallation TERMINÉE et VÉRIFIÉE !');
      } else if (result.success && !result.isComplete) {
        console.log('🕐 RÉSULTAT: Réinstallation EN COURS...');
      } else {
        console.log('❌ RÉSULTAT: Erreur lors de la vérification');
      }
    })
    .catch(console.error);
}

module.exports = { testReinstallVerification };
