'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Edit,
  MapPin,
  Server,
  Globe
} from 'lucide-react';
import { motion } from 'framer-motion';
import regionsService from '../../services/regionsService';
import packageService from '../../services/packageService';
import vpsService from '../../services/vpsService';

export default function VPSRegionsAdminNew() {
  const [availablePackages, setAvailablePackages] = useState([]);
  const [availableRegions, setAvailableRegions] = useState([]);
  const [vpsRegions, setVpsRegions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [editingPrice, setEditingPrice] = useState(null); // { regionId, packageId, currentPrice }
  const [newPrice, setNewPrice] = useState('');

  const [searchTerm, setSearchTerm] = useState('');

  // Charger les données
  const loadData = async () => {
    try {
      setLoading(true);
      setError('');

      // Charger les packages VPS
      const packagesResponse = await packageService.getPackages("VPS Hosting");
      if (packagesResponse.data) {
        setAvailablePackages(packagesResponse.data);
      }

      // Charger les régions dynamiques de Contabo
      const regionsResponse = await vpsService.getRegions('contabo');
      if (regionsResponse.data) {
        setAvailableRegions(regionsResponse.data);
      }

      // Charger les régions configurées avec prix
      try {
        const vpsRegionsResponse = await regionsService.getAllVPSRegions();
        let regions = [];
        if (vpsRegionsResponse?.data?.data) {
          regions = vpsRegionsResponse.data.data;
          setVpsRegions(regions);
        } else if (vpsRegionsResponse?.data) {
          regions = vpsRegionsResponse.data;
          setVpsRegions(regions);
        }



      } catch (regionError) {
        console.warn('Could not load VPS regions:', regionError);
        setVpsRegions([]);
      }

    } catch (err) {
      console.error('Error loading data:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Sélectionner le premier package par défaut
  useEffect(() => {
    if (availablePackages.length > 0 && !selectedPackage) {
      setSelectedPackage(availablePackages[0]);
    }
  }, [availablePackages, selectedPackage]);





  // Obtenir le prix configuré pour une région et un package
  const getPriceForRegion = (regionId, packageId) => {
    const region = vpsRegions.find(r => r.regionId.toLowerCase() === regionId.toLowerCase());
    if (!region || !region.pricing) return 0;

    const pricing = region.pricing.find(p => {
      const pId = typeof p.packageId === 'object' ? p.packageId._id : p.packageId;
      return pId === packageId;
    });

    return pricing ? pricing.additionalPrice : 0;
  };

  // Ouvrir le modal d'édition
  const startEditing = (regionId, packageId, currentPrice) => {
    setEditingPrice({ regionId, packageId, currentPrice });
    setNewPrice(currentPrice.toString());
  };

  // Sauvegarder le nouveau prix
  const savePrice = async () => {
    if (!editingPrice) return;

    try {
      setSaving(true);
      setError('');

      await regionsService.createVPSRegion({
        regionId: editingPrice.regionId,
        packageId: editingPrice.packageId,
        additionalPrice: parseFloat(newPrice) || 0
      });

      setSuccess('Prix sauvegardé avec succès !');
      setTimeout(() => setSuccess(''), 3000);

      // Fermer le modal et recharger
      setEditingPrice(null);
      setNewPrice('');
      await loadData();

    } catch (error) {
      console.error('Error saving price:', error);
      setError('Erreur lors de la sauvegarde du prix');
    } finally {
      setSaving(false);
    }
  };

  // Annuler l'édition
  const cancelEditing = () => {
    setEditingPrice(null);
    setNewPrice('');
  };



  // Filtrer les régions selon la recherche seulement
  const filteredRegions = availableRegions.filter(region => {
    // Filtre par recherche
    if (searchTerm && !region.regionName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header moderne */}
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between mb-8">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="h-8 w-8 text-blue-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                Gestion des Prix VPS par Région
              </h1>
            </div>
            <p className="text-gray-600">
              Configuration des prix supplémentaires par région pour chaque package VPS
            </p>
          </div>


        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Total Régions</p>
                <p className="text-2xl font-bold text-blue-900">{availableRegions.length}</p>
              </div>
              <Globe className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">Packages VPS</p>
                <p className="text-2xl font-bold text-purple-900">{availablePackages.length}</p>
              </div>
              <Server className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 text-red-700 px-4 py-3 rounded-r-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border-l-4 border-green-400 text-green-700 px-4 py-3 rounded-r-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {success}
            </div>
          </div>
        )}

        {/* Sélection Package et Filtres */}
        <div className="bg-white rounded-xl shadow-sm border overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
              {/* Sélection Package VPS */}
              <div className="flex-1 max-w-md">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Package VPS
                </label>
                <div className="relative">
                  <select
                    value={selectedPackage?._id || ''}
                    onChange={(e) => {
                      const pkg = availablePackages.find(p => p._id === e.target.value);
                      setSelectedPackage(pkg);
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-medium appearance-none"
                  >
                    <option value="">Sélectionner un package...</option>
                    {availablePackages.map(pkg => (
                      <option key={pkg._id} value={pkg._id}>
                        {pkg.name} - {pkg.price} MAD/mois
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Filtres */}
              <div className="flex gap-4">
                {/* Recherche */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Rechercher une région..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                  />
                </div>


              </div>
            </div>

            {/* Package sélectionné */}
            {selectedPackage && (
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Server className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-blue-900">{selectedPackage.name}</h3>
                      <p className="text-sm text-blue-700">{selectedPackage.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{selectedPackage.price} MAD</div>
                    <div className="text-sm text-blue-500">Prix de base/mois</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Liste des régions moderne */}
        {selectedPackage && (
          <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
            {/* En-tête */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Régions pour {selectedPackage.name}
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {filteredRegions.length} région(s) • Prix de base: {selectedPackage.price} MAD/mois
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">
                    {filteredRegions.length} région(s)
                  </span>
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Liste des régions */}
            <div className="divide-y divide-gray-200">
              {filteredRegions.map(region => {
                const regionId = region.regionSlug || region.id;
                const currentPrice = getPriceForRegion(regionId, selectedPackage._id);
                const totalPrice = selectedPackage.price + currentPrice;

                return (
                  <motion.div
                    key={regionId}
                    className="px-6 py-4 hover:bg-gray-50 transition-colors"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                            <MapPin className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {region.regionName}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {region.city}, {region.country} • {region.continent}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6">
                        {/* Prix */}
                        <div className="text-right">
                          <div>
                            <div className="text-sm text-gray-600">
                              +{currentPrice} MAD
                            </div>
                            <div className="text-lg font-semibold text-blue-600">
                              {totalPrice.toFixed(2)} MAD
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => startEditing(regionId, selectedPackage._id, currentPrice)}
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="Modifier le prix"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}

              {filteredRegions.length === 0 && (
                <div className="px-6 py-12 text-center">
                  <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Aucune région trouvée</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Essayez de modifier vos filtres de recherche
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Modal Modifier Prix - Version Simple */}
        {editingPrice && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-sm w-full mx-4">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Modifier le Prix
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {availableRegions.find(r => (r.regionSlug || r.id) === editingPrice.regionId)?.regionName}
                </p>
              </div>

              <div className="p-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Prix supplémentaire (MAD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={newPrice}
                    onChange={(e) => setNewPrice(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-xl font-semibold"
                    placeholder="0.00"
                    autoFocus
                  />
                </div>
              </div>

              <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={cancelEditing}
                  disabled={saving}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                >
                  Annuler
                </button>
                <button
                  onClick={savePrice}
                  disabled={saving}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  {saving && (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  <span>{saving ? 'Sauvegarde...' : 'Sauvegarder'}</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
