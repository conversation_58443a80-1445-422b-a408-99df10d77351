const mongoose = require('mongoose');

const vpsOSSchema = new mongoose.Schema({
  osId: {
    type: String,
    required: true,
    unique: true,
    index: true,
    validate: {
      validator: function(v) {
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(v);
      },
      message: 'osId must be a valid UUID format'
    }
  },
  name: {
    type: String,
    required: true,
    index: true
  },
  version: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['published', 'draft'],
    default: 'draft',
    index: true
  },
  customIcon: {
    type: String,
    default: null
  },
  iconType: {
    type: String,
    enum: ['ubuntu', 'windows', 'debian', 'centos', 'almalinux', 'rockylinux', 'fedora', 'opensuse', 'freebsd', 'archlinux', 'custom'],
    default: 'custom'
  },
  category: {
    type: String,
    enum: ['os'],
    default: 'os',
    index: true
  },
  isPopular: {
    type: Boolean,
    default: false,
    index: true
  },
  price: {
    type: String,
    default: 'Inclus'
  },
  osType: {
    type: String,
    enum: ['Linux', 'Windows'],
    default: 'Linux',
    index: true
  },
  isLTS: {
    type: Boolean,
    default: false
  },
  releaseDate: {
    type: Date,
    default: null
  },
  displayOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  collection: 'vps_os'
});

vpsOSSchema.index({ name: 1 });
vpsOSSchema.index({ osId: 1 });

vpsOSSchema.statics.findByOsId = function(osId) {
  return this.findOne({ osId: osId });
};

vpsOSSchema.statics.findByName = function(name) {
  return this.find({ name: name });
};

vpsOSSchema.statics.getAllOS = function() {
  return this.find({}).sort({ name: 1 });
};

vpsOSSchema.methods.getDisplayName = function() {
  return this.description || this.name;
};

vpsOSSchema.methods.getFullName = function() {
  return `${this.name} ${this.version}`;
};

const VpsOS = mongoose.model('VpsOS', vpsOSSchema);

module.exports = VpsOS;
