const mongoose = require('mongoose');
const Package = require('./models/Package');
const VPSRegion = require('./models/VPSRegion');

async function debugPackages() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/hosting-website');
    console.log('✅ Connected to MongoDB');

    // Get all VPS packages
    const vpsPackages = await Package.find({
      name: { $in: ['CLOUD VPS 10', 'CLOUD VPS 20', 'CLOUD VPS 30'] }
    }).select('_id name vpsConfig');

    console.log('\n=== VPS PACKAGES ===');
    vpsPackages.forEach(pkg => {
      console.log(`${pkg.name}: ${pkg._id} (Provider ID: ${pkg.vpsConfig?.providerProductId})`);
    });

    // Get a sample region to see pricing structure
    const sampleRegion = await VPSRegion.findOne().populate('pricing.packageId', 'name');
    
    if (sampleRegion) {
      console.log('\n=== SAMPLE REGION PRICING ===');
      console.log(`Region: ${sampleRegion.name} (${sampleRegion.regionId})`);
      sampleRegion.pricing.forEach(p => {
        console.log(`  Package: ${p.packageName} (ID: ${p.packageId}) - Price: +${p.additionalPrice} MAD`);
      });
    }

    // Check for Asia Japan specifically
    const asiaJapan = await VPSRegion.findOne({ regionId: 'jpn' }).populate('pricing.packageId', 'name');
    
    if (asiaJapan) {
      console.log('\n=== ASIA JAPAN PRICING ===');
      console.log(`Region: ${asiaJapan.name} (${asiaJapan.regionId})`);
      asiaJapan.pricing.forEach(p => {
        console.log(`  Package: ${p.packageName} (ID: ${p.packageId}) - Price: +${p.additionalPrice} MAD`);
      });
    }

    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

debugPackages();
