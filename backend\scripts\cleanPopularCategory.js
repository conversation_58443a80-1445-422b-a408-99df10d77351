const mongoose = require('mongoose');
const VpsOS = require('../models/VpsOS');
const VpsApp = require('../models/VpsApp');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev';

async function cleanPopularCategory() {
  try {
    console.log('🔄 Connexion à MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('🧹 Nettoyage des catégories "popular"...');
    
    // Trouver toutes les images avec category: 'popular'
    const popularOS = await VpsOS.find({ category: 'popular' });
    const popularApps = await VpsApp.find({ category: 'popular' });
    
    console.log(`📊 Trouvé ${popularOS.length} OS avec category: 'popular'`);
    console.log(`📊 Trouvé ${popularApps.length} Apps avec category: 'popular'`);

    // Nettoyer les OS
    for (const os of popularOS) {
      console.log(`🔄 Nettoyage OS: ${os.name} ${os.version}`);
      await VpsOS.updateOne(
        { _id: os._id },
        { 
          $set: { 
            category: 'os',
            isPopular: true 
          }
        }
      );
    }

    // Nettoyer les Apps
    for (const app of popularApps) {
      console.log(`🔄 Nettoyage App: ${app.name} ${app.version}`);
      await VpsApp.updateOne(
        { _id: app._id },
        { 
          $set: { 
            category: 'apps',
            isPopular: true 
          }
        }
      );
    }

    // Vérification finale
    const remainingPopularOS = await VpsOS.countDocuments({ category: 'popular' });
    const remainingPopularApps = await VpsApp.countDocuments({ category: 'popular' });
    
    console.log('\n✅ NETTOYAGE TERMINÉ :');
    console.log(`OS avec category: 'popular' restants: ${remainingPopularOS}`);
    console.log(`Apps avec category: 'popular' restants: ${remainingPopularApps}`);
    
    // Statistiques finales
    const popularOSCount = await VpsOS.countDocuments({ isPopular: true });
    const popularAppsCount = await VpsApp.countDocuments({ isPopular: true });
    
    console.log(`\n📊 STATISTIQUES FINALES :`);
    console.log(`OS populaires (isPopular: true): ${popularOSCount}`);
    console.log(`Apps populaires (isPopular: true): ${popularAppsCount}`);

  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  } finally {
    console.log('🔌 Fermeture de la connexion MongoDB...');
    await mongoose.connection.close();
    console.log('✅ Connexion fermée');
  }
}

// Exécuter le nettoyage
cleanPopularCategory();
