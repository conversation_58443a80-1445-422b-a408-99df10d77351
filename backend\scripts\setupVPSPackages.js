/**
 * <PERSON>ript to setup VPS packages with Contabo configuration
 * This script creates VPS packages in the database with proper Contabo product IDs
 */

const mongoose = require('mongoose');
const Package = require('../models/Package');
const Brand = require('../models/Brand');
const Category = require('../models/Category');
const Specification = require('../models/Specification');
const { v4: uuidv4 } = require('uuid');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// VPS packages data with Contabo configuration
const vpsPackagesData = [
  {
    name: 'CLOUD VPS 10',
    name_fr: 'CLOUD VPS 10',
    description: '1 CPU, 4GB RAM, 75GB NVMe Storage',
    description_fr: '1 CPU, 4GB RAM, 75GB Stockage NVMe',
    price: 46,
    regularPrice: 52,
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V91',
      providerPlanName: 'VPS 10 NVMe'
    },
    specifications: [
      '1 vCPU Core',
      '4 GB RAM',
      '75 GB NVMe SSD',
      '32 TB Traffic',
      '99.9% Uptime',
      '24/7 Support'
    ]
  },
  {
    name: 'CLOUD VPS 20',
    name_fr: 'CLOUD VPS 20',
    description: '2 CPU, 8GB RAM, 100GB NVMe Storage',
    description_fr: '2 CPU, 8GB RAM, 100GB Stockage NVMe',
    price: 71,
    regularPrice: 79,
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V92',
      providerPlanName: 'VPS 20 NVMe'
    },
    specifications: [
      '2 vCPU Cores',
      '8 GB RAM',
      '100 GB NVMe SSD',
      '32 TB Traffic',
      '99.9% Uptime',
      '24/7 Support'
    ]
  },
  {
    name: 'CLOUD VPS 30',
    name_fr: 'CLOUD VPS 30',
    description: '4 CPU, 16GB RAM, 200GB NVMe Storage',
    description_fr: '4 CPU, 16GB RAM, 200GB Stockage NVMe',
    price: 142,
    regularPrice: 158,
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V97',
      providerPlanName: 'VPS 30 NVMe'
    },
    specifications: [
      '4 vCPU Cores',
      '16 GB RAM',
      '200 GB NVMe SSD',
      '32 TB Traffic',
      '99.9% Uptime',
      '24/7 Support'
    ]
  },
  {
    name: 'VDS S',
    name_fr: 'VDS S',
    description: '2 CPU, 8GB RAM, 180GB NVMe Storage',
    description_fr: '2 CPU, 8GB RAM, 180GB Stockage NVMe',
    price: 200,
    regularPrice: 220,
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V8',
      providerPlanName: 'VDS S'
    },
    specifications: [
      '2 vCPU Cores',
      '8 GB RAM',
      '180 GB NVMe SSD',
      '32 TB Traffic',
      '99.9% Uptime',
      '24/7 Support'
    ]
  }
];

// Setup function
const setupVPSPackages = async () => {
  try {
    console.log('Starting VPS packages setup...');

    // Find or create VPS category
    let vpsCategory = await Category.findOne({ name: 'VPS' });
    if (!vpsCategory) {
      vpsCategory = new Category({
        name: 'VPS',
        name_fr: 'VPS',
        description: 'Virtual Private Servers',
        description_fr: 'Serveurs Privés Virtuels'
      });
      await vpsCategory.save();
      console.log('Created VPS category');
    }

    // Find or create VPS Hosting brand
    let vpsBrand = await Brand.findOne({ name: 'VPS Hosting' });
    if (!vpsBrand) {
      vpsBrand = new Brand({
        name: 'VPS Hosting',
        name_fr: 'Hébergement VPS',
        category: vpsCategory._id,
        packages: []
      });
      await vpsBrand.save();
      console.log('Created VPS Hosting brand');
    }

    // Create VPS packages
    for (const packageData of vpsPackagesData) {
      // Check if package already exists
      const existingPackage = await Package.findOne({ 
        name: packageData.name,
        brand: vpsBrand._id 
      });

      if (existingPackage) {
        console.log(`Package ${packageData.name} already exists, skipping...`);
        continue;
      }

      // Create specifications
      const specificationIds = [];
      for (const specText of packageData.specifications) {
        const spec = new Specification({
          value: specText,
          value_fr: specText // For now, using same text for French
        });
        await spec.save();
        specificationIds.push(spec._id);
      }

      // Create package
      const newPackage = new Package({
        reference: uuidv4().slice(0, 8),
        name: packageData.name,
        name_fr: packageData.name_fr,
        description: packageData.description,
        description_fr: packageData.description_fr,
        price: packageData.price,
        regularPrice: packageData.regularPrice,
        category: vpsCategory._id,
        brand: vpsBrand._id,
        specifications: specificationIds,
        vpsConfig: packageData.vpsConfig,
        status: 'PUBLISHED'
      });

      await newPackage.save();

      // Update brand with package reference
      await Brand.findByIdAndUpdate(
        vpsBrand._id,
        { $push: { packages: newPackage._id } }
      );

      console.log(`Created VPS package: ${packageData.name} with Contabo ID: ${packageData.vpsConfig.providerProductId}`);
    }

    console.log('VPS packages setup completed successfully!');
    console.log('\nPackages created with Contabo configuration:');
    
    const createdPackages = await Package.find({ brand: vpsBrand._id })
      .populate('specifications');
    
    createdPackages.forEach(pkg => {
      console.log(`- ${pkg.name}: ${pkg.vpsConfig.providerProductId} (${pkg.price} MAD)`);
    });

  } catch (error) {
    console.error('Error setting up VPS packages:', error);
  }
};

// Run the setup
const run = async () => {
  await connectDB();
  await setupVPSPackages();
  await mongoose.connection.close();
  console.log('\nDatabase connection closed.');
};

// Execute if run directly
if (require.main === module) {
  run();
}

module.exports = { setupVPSPackages };
