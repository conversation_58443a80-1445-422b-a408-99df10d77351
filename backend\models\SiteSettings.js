const mongoose = require('mongoose');

/**
 * Schema for site settings
 * Stores general configuration and SEO settings for the website
 * Using Mixed type to avoid complex validation issues
 */
const siteSettingsSchema = new mongoose.Schema({
  // General Settings - using Mixed type for flexibility
  general: {
    type: mongoose.Schema.Types.Mixed,
    default: {
      siteName: 'Ztechengineering',
      siteDescription: 'ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success',
      contactEmail: '<EMAIL>',
      phone: '+*********** 605',
      address: 'No. 5 First Floor, Idriss II Boulevard Kennedy Safi, Morocco',
      socialLinks: {
        linkedin: 'https://www.linkedin.com/company/ztechengineering',
        instagram: 'https://www.instagram.com/ztechengineering',
        facebook: 'https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr'
      }
    }
  },

  // SEO Settings - using Mixed type for flexibility
  seo: {
    type: mongoose.Schema.Types.Mixed,
    default: {
      defaultTitle: 'ZtechEngineering | Web & Mobile Development, Cloud Hosting',
      defaultDescription: 'ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success',
      defaultKeywords: 'web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco',
      favicon: '',
      googleTagManagerId: '',
      googleSiteVerification: '',
      bingVerification: '',
      robotsTxt: 'User-agent: *\nAllow: /',
      sitemapEnabled: true
    }
  },

  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, { 
  timestamps: true,
  // Ensure only one settings document exists
  collection: 'sitesettings'
});

// Pre-save middleware to update the updatedAt field and validate
siteSettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date();

  // Custom validation for email if provided
  if (this.general && this.general.contactEmail) {
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(this.general.contactEmail)) {
      return next(new Error('Please enter a valid email address'));
    }
  }

  // Custom validation for Google Analytics ID if provided
  if (this.seo && this.seo.googleTagManagerId) {
    const gaRegex = /^(GTM-[A-Z0-9]+|UA-[0-9]+-[0-9]+)$/;
    if (this.seo.googleTagManagerId && !gaRegex.test(this.seo.googleTagManagerId)) {
      return next(new Error('Please enter a valid Google Analytics ID (GTM-XXXXXXXXXX or UA-XXXXXXXX-X)'));
    }
  }

  next();
});

// Static method to get or create settings
siteSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  
  if (!settings) {
    // Create default settings if none exist
    settings = await this.create({
      general: {
        siteName: 'Ztechengineering',
        siteDescription: 'ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success',
        contactEmail: '<EMAIL>',
        phone: '',
        address: 'No. 5 First Floor, Idriss II Boulevard Kennedy Safi, Morocco',
        socialLinks: {
          linkedin: 'https://www.linkedin.com/company/ztechengineering',
          instagram: 'https://www.instagram.com/ztechengineering',
          facebook: 'https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr'
        }
      },
      seo: {
        defaultTitle: 'ZtechEngineering | Web & Mobile Development, Cloud Hosting',
        defaultDescription: 'ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success',
        defaultKeywords: 'web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco',
        favicon: '',
        googleTagManagerId: '',
        googleSiteVerification: '',
        bingVerification: '',
        robotsTxt: 'User-agent: *\nAllow: /',
        sitemapEnabled: true
      }
    });
  }
  
  return settings;
};

// Create a model from the schema
const SiteSettings = mongoose.model('SiteSettings', siteSettingsSchema);

module.exports = SiteSettings;
