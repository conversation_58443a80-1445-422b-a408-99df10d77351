const ContaboProvider = require('./services/providers/ContaboProvider');

async function testVPSStatusAndConnection() {
  console.log('🔍 Testing VPS Status and Connection Details...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const instanceId = 202718127;

    // Get VPS details
    console.log('📊 Getting VPS status...');

    // Get raw response to debug
    const response = await contaboProvider.makeRequest("GET", `/compute/instances/${instanceId}`);
    console.log('\n🔍 Raw API Response:', JSON.stringify(response, null, 2));

    const vpsData = response.data?.[0] || response.data || response;

    console.log('\n📋 VPS Details:');
    console.log('- Status:', vpsData.status);
    console.log('- Name:', vpsData.name || vpsData.displayName);
    console.log('- IP Address:', vpsData.ipConfig?.v4?.ip || vpsData.ipv4 || 'Not available');
    console.log('- Image:', vpsData.imageId);
    console.log('- Created:', vpsData.createdDate);
    
    const ipAddress = vpsData.ipConfig?.v4?.ip || vpsData.ipv4;
    if (ipAddress) {
      
      console.log('\n🔐 SSH Connection Testing Guide:');
      console.log('=====================================');
      
      console.log('\n1. Test with ROOT user:');
      console.log(`   ssh root@${ipAddress}`);
      console.log('   Password: StandardTest123!');
      
      console.log('\n2. Test with UBUNTU user (common for Ubuntu images):');
      console.log(`   ssh ubuntu@${ipAddress}`);
      console.log('   Password: StandardTest123!');
      
      console.log('\n3. Test with ADMIN user:');
      console.log(`   ssh admin@${ipAddress}`);
      console.log('   Password: StandardTest123!');
      
      console.log('\n4. Alternative connection methods:');
      console.log(`   - Try: ssh -o PreferredAuthentications=password -o PubkeyAuthentication=no root@${ipAddress}`);
      console.log(`   - Try: ssh -o PasswordAuthentication=yes root@${ipAddress}`);
      
      console.log('\n🔍 Troubleshooting steps:');
      console.log('- Wait 5-10 minutes after reinstallation before testing');
      console.log('- Check VPS status is "running" not "creating"');
      console.log('- Try ping first to ensure network connectivity');
      console.log('- Some images might disable password auth by default');
      
      console.log('\n📝 Image-specific notes:');
      if (vpsData.imageId && vpsData.imageId.includes('ubuntu')) {
        console.log('- Ubuntu images often use "ubuntu" user instead of "root"');
        console.log('- Root login might be disabled by default');
        console.log('- Try: sudo su - (after logging in as ubuntu)');
      }
      if (vpsData.imageId && vpsData.imageId.includes('cpanel')) {
        console.log('- cPanel images might have special user configurations');
        console.log('- Check cPanel documentation for default users');
      }
      
      console.log('\n🧪 Quick connectivity test:');
      console.log(`ping -c 4 ${ipAddress}`);
      
    } else {
      console.log('\n⚠️ No IP address found for VPS. Check if reinstallation is complete.');
    }

    // Check if VPS is ready
    if (vpsData.status === 'running') {
      console.log('\n✅ VPS is running - ready for SSH testing');
    } else if (vpsData.status === 'creating') {
      console.log('\n⏳ VPS is still being created - wait before testing SSH');
    } else {
      console.log(`\n⚠️ VPS status: ${vpsData.status} - check Contabo panel`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
}

// Run the test
testVPSStatusAndConnection();
