const ContaboProvider = require('./services/providers/ContaboProvider');

async function testCompleteAdvancedReinstall() {
  console.log('🧪 Testing Complete Advanced Reinstallation with SSH Key...\n');

  try {
    const contaboProvider = new ContaboProvider();

    // Real VPS instance ID (replace with your actual VPS ID)
    const instanceId = 202718127;

    // Test SSH key (example ED25519 key format)
    const testSSHKey = 'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGQw7+X9J5K8H2L3M4N5O6P7Q8R9S0T1U2V3W4X5Y6Z7 <EMAIL>';

    console.log('📝 Test SSH Key:', testSSHKey.substring(0, 50) + '...');

    // Get available images
    console.log('\n📀 Getting available images...');
    const images = await contaboProvider.getImages();
    const ubuntuImage = images.find(img => img.name.toLowerCase().includes('ubuntu') && img.name.includes('22.04'));
    
    if (!ubuntuImage) {
      throw new Error('Ubuntu 22.04 image not found');
    }
    
    console.log(`✅ Selected image: ${ubuntuImage.name} (ID: ${ubuntuImage.imageId})`);

    // Get available applications
    console.log('\n📦 Getting available applications...');
    const applications = await contaboProvider.getApplications();
    const dockerApp = applications.find(app => app.name.toLowerCase().includes('docker'));
    
    console.log(`✅ Selected application: ${dockerApp ? dockerApp.name : 'None'} (ID: ${dockerApp ? dockerApp.applicationId : 'N/A'})`);

    // Prepare advanced reinstall data
    const advancedReinstallData = {
      imageId: ubuntuImage.imageId,
      password: 'AdvancedSSHTest123!',
      enableRootUser: true,
      sshKeys: [testSSHKey], // Raw SSH key - will be converted to ID automatically
      userData: `#cloud-config
# Configuration Cloud-Init pour installation avancée
packages:
  - htop
  - curl
  - git
  - vim
  - ufw

# Configuration utilisateur (SSH keys handled by Contabo API)
users:
  - name: admin
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

# Configuration firewall
runcmd:
  - ufw --force enable
  - ufw allow ssh
  - systemctl enable ssh
  - echo "Advanced installation completed" >> /root/install.log`,
      // applicationId: dockerApp ? dockerApp.applicationId : undefined, // Disabled - requires UUID
      customScript: `#!/bin/bash
echo "=== Advanced Installation Custom Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
echo "SSH Key configured for secure access" >> /root/custom_install.log
echo "Docker application: ${dockerApp ? dockerApp.name : 'Not installed'}" >> /root/custom_install.log

# Install additional tools
apt-get update
apt-get install -y tree ncdu

echo "Custom script execution completed" >> /root/custom_install.log`
    };

    console.log('\n📤 Advanced reinstall data prepared:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', advancedReinstallData.password ? '***' : 'Not set');
    console.log('- Enable Root User:', advancedReinstallData.enableRootUser);
    console.log('- SSH Keys:', advancedReinstallData.sshKeys.length, 'key(s)');
    console.log('- Application:', dockerApp ? dockerApp.name : 'None');
    console.log('- User Data:', advancedReinstallData.userData ? 'Set (Cloud-Init)' : 'Not set');
    console.log('- Custom Script:', advancedReinstallData.customScript ? 'Set' : 'Not set');

    console.log('\n🔄 Starting advanced reinstallation...');
    const result = await contaboProvider.reinstallVPS(instanceId, advancedReinstallData);

    console.log('\n✅ Advanced reinstallation completed successfully!');
    console.log('Result:', result.message);
    console.log('Instance ID:', result.instanceId);

    console.log('\n🔍 Next steps:');
    console.log('1. Wait for the VPS to complete reinstallation (5-10 minutes)');
    console.log('2. Test SSH connection with the registered key');
    console.log('3. Verify Cloud-Init configuration was applied');
    console.log('4. Check custom script execution logs in /root/');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testCompleteAdvancedReinstall();
