const ContaboProvider = require('./services/providers/ContaboProvider');

async function testAlphanumericPassword() {
  console.log('🧪 Testing Alphanumeric Password Installation...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const instanceId = 202718127;

    // Generate alphanumeric password (like frontend)
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const allChars = lowercase + uppercase + numbers;
    
    let password = '';
    // Assurer au moins un caractère de chaque type
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    
    // Compléter avec des caractères aléatoires (12 caractères total)
    for (let i = 3; i < 12; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Mélanger le mot de passe
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    console.log('🔐 Generated alphanumeric password:', password);
    console.log('📊 Password analysis:');
    console.log('- Length:', password.length);
    console.log('- Has lowercase:', /[a-z]/.test(password));
    console.log('- Has uppercase:', /[A-Z]/.test(password));
    console.log('- Has numbers:', /[0-9]/.test(password));
    console.log('- Has special chars:', /[^A-Za-z0-9]/.test(password));
    console.log('- SSH-safe:', !/[^A-Za-z0-9]/.test(password) ? '✅ Yes' : '❌ No');

    // Test standard installation with alphanumeric password
    const reinstallData = {
      imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb', // ubuntu-22.04
      password: password,
      enableRootUser: true,
      userData: `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${password}
chpasswd:
  expire: false

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Alphanumeric password test: ${password}" >> /root/install.log
  - echo "SSH authentication enabled" >> /root/install.log`
    };

    console.log('\n📤 Testing standard installation with alphanumeric password...');
    console.log('Payload:', JSON.stringify(reinstallData, null, 2));

    const result = await contaboProvider.reinstallVPS(instanceId, reinstallData);
    
    if (result.success) {
      console.log('\n✅ Standard installation with alphanumeric password successful!');
      console.log('🔍 Next steps:');
      console.log('1. Wait 5-10 minutes for installation to complete');
      console.log('2. Test SSH connection:');
      console.log(`   ssh root@185.217.125.69`);
      console.log(`   Password: ${password}`);
      console.log('3. Check logs: cat /root/install.log');
      console.log('\n💡 This should resolve the "Permission denied" SSH issue!');
    } else {
      console.error('❌ Installation failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
}

// Run the test
testAlphanumericPassword();
