'use client'

import React, { useState } from 'react';
import {
  X,
  Monitor,
  Package,
  Settings,
  Database,
  Eye,
  Smartphone,
  Globe,
  CheckCircle,
  Star,
  Shield,
  Zap
} from 'lucide-react';
import {
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Button,
  Typography,
  Card,
  Chip,
  Badge,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel
} from "@material-tailwind/react";
import { motion } from 'framer-motion';

export default function PreviewModal({
  isOpen,
  onClose,
  osImages = [],
  appImages = []
}) {
  const [activePreviewTab, setActivePreviewTab] = useState('popular');

  // Organiser les images par catégorie comme dans Contabo
  const organizeImagesByCategory = () => {
    const popular = [...osImages, ...appImages].filter(img => img.category === 'popular' && img.status === 'published');
    const os = osImages.filter(img => img.category === 'os' && img.status === 'published');
    const apps = appImages.filter(img => img.category === 'apps' && img.status === 'published');
    const blockchain = appImages.filter(img => img.category === 'blockchain' && img.status === 'published');

    return { popular, os, apps, blockchain };
  };

  const { popular, os, apps, blockchain } = organizeImagesByCategory();

  // Obtenir l'icône pour le type d'image
  const getImageIcon = (image) => {
    const iconMap = {
      ubuntu: <Monitor className="h-8 w-8 text-orange-500" />,
      windows: <Monitor className="h-8 w-8 text-blue-500" />,
      debian: <Monitor className="h-8 w-8 text-red-500" />,
      centos: <Monitor className="h-8 w-8 text-purple-500" />,
      almalinux: <Monitor className="h-8 w-8 text-blue-600" />,
      rockylinux: <Monitor className="h-8 w-8 text-green-600" />,
      fedora: <Monitor className="h-8 w-8 text-blue-700" />,
      cpanel: <Settings className="h-8 w-8 text-blue-600" />,
      plesk: <Settings className="h-8 w-8 text-purple-600" />,
      blockchain: <Database className="h-8 w-8 text-yellow-600" />
    };
    
    return iconMap[image.iconType] || <Package className="h-8 w-8 text-gray-500" />;
  };

  // Composant de carte d'image (comme dans Contabo)
  const ImageCard = ({ image, isApp = false }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group"
    >
      <div className="flex items-start gap-3">
        {/* Icône */}
        <div className="flex-shrink-0">
          {getImageIcon(image)}
        </div>

        {/* Contenu */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {image.name}
              </h3>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {image.description}
              </p>
              
              {/* Version et badges */}
              <div className="flex items-center gap-2 mt-2">
                <span className="text-xs text-gray-500">v{image.version}</span>
                {image.isLTS && (
                  <Badge color="blue" className="text-xs">LTS</Badge>
                )}
                {image.category === 'popular' && (
                  <Badge color="purple" className="text-xs">Popular</Badge>
                )}
              </div>

              {/* OS de base pour les apps */}
              {isApp && image.supportedOs && image.supportedOs.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs text-gray-500">Compatible avec: </span>
                  <span className="text-xs text-blue-600">
                    {image.supportedOs.length} OS
                  </span>
                </div>
              )}

              {/* Variantes pour les apps */}
              {isApp && image.variants && image.variants.length > 0 && (
                <div className="mt-2">
                  <div className="flex flex-wrap gap-1">
                    {image.variants.slice(0, 2).map((variant, index) => (
                      <Chip
                        key={index}
                        value={`${variant.name}: ${variant.price} MAD`}
                        color="blue"
                        className="text-xs"
                      />
                    ))}
                    {image.variants.length > 2 && (
                      <span className="text-xs text-gray-500">
                        +{image.variants.length - 2} autres
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Prix */}
            <div className="text-right flex-shrink-0 ml-3">
              <div className={`font-semibold ${
                image.price === 'Inclus' ? 'text-green-600' : 'text-gray-900'
              }`}>
                {image.price}
              </div>
              {image.price !== 'Inclus' && (
                <div className="text-xs text-gray-500">/mois</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bouton de sélection (simulé) */}
      <div className="mt-4 pt-3 border-t border-gray-100">
        <Button
          size="sm"
          className="w-full bg-blue-600 hover:bg-blue-700"
          disabled
        >
          Sélectionner
        </Button>
      </div>
    </motion.div>
  );

  // Onglets de prévisualisation
  const previewTabs = [
    { value: 'popular', label: 'Popular', count: popular.length, icon: Star },
    { value: 'os', label: 'OS', count: os.length, icon: Monitor },
    { value: 'apps', label: 'Apps & Panels', count: apps.length, icon: Settings },
    { value: 'blockchain', label: 'Blockchain', count: blockchain.length, icon: Database }
  ];

  return (
    <Dialog
      open={isOpen}
      handler={onClose}
      size="xxl"
      className="max-h-[90vh] overflow-y-auto"
    >
      <DialogHeader className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Eye className="h-6 w-6 text-blue-600" />
          <Typography variant="h5">
            Prévisualisation - Page Configuration VPS
          </Typography>
        </div>
        <Button variant="text" color="gray" onClick={onClose} className="p-2">
          <X className="h-5 w-5" />
        </Button>
      </DialogHeader>

      <DialogBody className="space-y-6">
        {/* Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <Typography variant="small" className="text-blue-900 font-medium">
            Aperçu de l'affichage dans la page de configuration VPS
          </Typography>
          <Typography variant="small" className="text-blue-700 mt-1">
            Seules les images avec le statut "Publiée" sont affichées ici
          </Typography>
        </div>

        {/* Section Images comme dans Contabo */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            2. Choisir une image
          </Typography>

          {/* Onglets */}
          <Tabs value={activePreviewTab} onChange={setActivePreviewTab}>
            <TabsHeader className="grid w-full grid-cols-4 bg-gray-100">
              {previewTabs.map(({ value, label, count, icon: Icon }) => (
                <Tab key={value} value={value} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span>{label}</span>
                  <Badge color="gray" className="ml-1">{count}</Badge>
                </Tab>
              ))}
            </TabsHeader>

            <TabsBody className="mt-6">
              {/* Onglet Popular */}
              <TabPanel value="popular" className="p-0">
                {popular.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {popular.map((image) => (
                      <ImageCard
                        key={image.osId || image.appId}
                        image={image}
                        isApp={!!image.appId}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500">
                      Aucune image populaire publiée
                    </Typography>
                  </div>
                )}
              </TabPanel>

              {/* Onglet OS */}
              <TabPanel value="os" className="p-0">
                {os.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {os.map((image) => (
                      <ImageCard
                        key={image.osId}
                        image={image}
                        isApp={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500">
                      Aucune image OS publiée
                    </Typography>
                  </div>
                )}
              </TabPanel>

              {/* Onglet Apps */}
              <TabPanel value="apps" className="p-0">
                {apps.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {apps.map((image) => (
                      <ImageCard
                        key={image.appId}
                        image={image}
                        isApp={true}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500">
                      Aucune application publiée
                    </Typography>
                  </div>
                )}
              </TabPanel>

              {/* Onglet Blockchain */}
              <TabPanel value="blockchain" className="p-0">
                {blockchain.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {blockchain.map((image) => (
                      <ImageCard
                        key={image.appId}
                        image={image}
                        isApp={true}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500">
                      Aucune application blockchain publiée
                    </Typography>
                  </div>
                )}
              </TabPanel>
            </TabsBody>
          </Tabs>
        </Card>
      </DialogBody>

      <DialogFooter>
        <Button variant="text" color="gray" onClick={onClose}>
          Fermer
        </Button>
      </DialogFooter>
    </Dialog>
  );
}
