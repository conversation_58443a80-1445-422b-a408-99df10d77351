const ContaboProvider = require('./services/providers/ContaboProvider');

async function testAllReinstallTypes() {
  console.log('🧪 Testing All 3 Reinstallation Types...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const instanceId = 202718127;

    // Get available images
    console.log('📀 Getting available images...');
    const images = await contaboProvider.getImages();
    const ubuntuImage = images.find(img => 
      img.name.toLowerCase().includes('ubuntu') && 
      img.name.includes('22.04') &&
      !img.name.toLowerCase().includes('plesk') &&
      !img.name.toLowerCase().includes('cpanel')
    );
    
    if (!ubuntuImage) {
      throw new Error('Ubuntu 22.04 image not found');
    }
    
    console.log(`✅ Selected image: ${ubuntuImage.name} (ID: ${ubuntuImage.imageId})`);

    // Get available applications
    console.log('📦 Getting available applications...');
    const applications = await contaboProvider.getAvailableApplications();
    const dockerApp = applications.data.find(app => app.name.toLowerCase().includes('docker'));
    
    console.log(`✅ Found ${applications.data.length} applications`);
    if (dockerApp) {
      console.log(`✅ Docker application: ${dockerApp.name} (ID: ${dockerApp.applicationId})`);
    }

    // Generate alphanumeric password
    const generatePassword = () => {
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const numbers = '**********';
      const allChars = lowercase + uppercase + numbers;
      let password = '';
      
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      
      for (let i = 3; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      
      return password.split('').sort(() => Math.random() - 0.5).join('');
    };

    // Generate SSH key
    const generateSSHKey = () => {
      const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} <EMAIL>`;
    };

    console.log('\n' + '='.repeat(80));
    console.log('🔄 TEST 1: INSTALLATION STANDARD');
    console.log('='.repeat(80));

    const standardPassword = generatePassword();
    const standardData = {
      imageId: ubuntuImage.imageId,
      password: standardPassword,
      enableRootUser: true,
      selectedApplication: dockerApp ? dockerApp.applicationId : null,
      userData: `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${standardPassword}
chpasswd:
  expire: false

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Standard installation completed" >> /root/install.log`
    };

    console.log('📤 Standard installation data:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', standardPassword);
    console.log('- Application:', dockerApp ? dockerApp.name : 'None');
    console.log('- Enable Root User:', true);

    const result1 = await contaboProvider.reinstallVPS(instanceId, standardData);
    console.log('✅ Standard installation result:', result1.message);

    // Wait a bit before next test
    console.log('\n⏳ Waiting 30 seconds before next test...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    console.log('\n' + '='.repeat(80));
    console.log('🔄 TEST 2: INSTALLATION AVANCÉE - STANDARD IMAGE');
    console.log('='.repeat(80));

    const advancedPassword = generatePassword();
    const sshKey = generateSSHKey();
    
    const advancedStandardData = {
      imageId: ubuntuImage.imageId,
      password: advancedPassword,
      enableRootUser: true,
      sshKeys: [sshKey],
      userData: `#cloud-config
# Advanced installation with custom configuration
packages:
  - htop
  - curl
  - git
  - vim

users:
  - name: admin
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

runcmd:
  - systemctl enable ssh
  - echo "Advanced standard installation completed" >> /root/install.log`,
      customScript: `#!/bin/bash
echo "=== Advanced Standard Installation Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
apt-get update
apt-get install -y tree ncdu
echo "Custom script execution completed" >> /root/custom_install.log`
    };

    console.log('📤 Advanced standard installation data:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', advancedPassword);
    console.log('- SSH Key:', sshKey.substring(0, 50) + '...');
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: Custom packages and users');
    console.log('- Custom Script: Additional tools installation');

    const result2 = await contaboProvider.reinstallVPS(instanceId, advancedStandardData);
    console.log('✅ Advanced standard installation result:', result2.message);

    // Wait a bit before next test
    console.log('\n⏳ Waiting 30 seconds before next test...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    console.log('\n' + '='.repeat(80));
    console.log('🔄 TEST 3: INSTALLATION AVANCÉE - CUSTOM IMAGE');
    console.log('='.repeat(80));

    const customPassword = generatePassword();
    const customSSHKey = generateSSHKey();
    
    // For this test, we'll simulate a custom image URL
    const customImageData = {
      customImageUrl: 'https://example.com/my-custom-ubuntu.iso',
      password: customPassword,
      enableRootUser: true,
      sshKeys: [customSSHKey],
      userData: `#cloud-config
# Custom image installation
packages:
  - nginx
  - docker.io

users:
  - name: deploy
    groups: sudo, docker
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

runcmd:
  - systemctl enable docker
  - systemctl start docker
  - systemctl enable nginx
  - echo "Custom image installation completed" >> /root/install.log`
    };

    console.log('📤 Custom image installation data:');
    console.log('- Custom Image URL:', customImageData.customImageUrl);
    console.log('- Password:', customPassword);
    console.log('- SSH Key:', customSSHKey.substring(0, 50) + '...');
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: Docker and Nginx setup');

    // Note: For custom image, we would need to create the image first via API
    console.log('📝 Note: Custom image would need to be created via /api/custom-images first');
    console.log('📝 Then use the returned imageId for reinstallation');

    console.log('\n' + '='.repeat(80));
    console.log('✅ ALL TESTS COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(80));

    console.log('\n📋 Summary:');
    console.log('1. ✅ Standard Installation - Working');
    console.log('2. ✅ Advanced Standard Image - Working');
    console.log('3. ✅ Advanced Custom Image - Ready (needs custom image creation)');

    console.log('\n🔍 Next steps for testing:');
    console.log('1. Wait for VPS reinstallation to complete (5-10 minutes)');
    console.log('2. Test SSH connection with the latest password');
    console.log('3. Verify Cloud-Init and custom scripts executed');
    console.log('4. Check logs: /root/install.log and /root/custom_install.log');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testAllReinstallTypes();
