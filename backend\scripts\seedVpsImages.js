const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const VpsOS = require('../models/VpsOS');
const VpsApp = require('../models/VpsApp');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev';

// Données de test pour les images OS
const osImagesData = [
  {
    osId: uuidv4(),
    name: 'Ubuntu',
    version: '22.04',
    description: 'Ubuntu 22.04 (LTS)',
    status: 'published',
    category: 'os',
    isPopular: true,
    price: 'Inclus',
    iconType: 'ubuntu',
    displayOrder: 1,
    osType: 'Linux',
    isLTS: true,
    releaseDate: new Date('2022-04-21')
  },
  {
    osId: uuidv4(),
    name: 'Windows Server',
    version: '2025-de',
    description: 'Windows Server 2025 Datacenter',
    status: 'published',
    category: 'os',
    price: '205 MAD',
    iconType: 'windows',
    displayOrder: 1,
    osType: 'Windows',
    isLTS: false,
    releaseDate: new Date('2025-04-30')
  },
  {
    osId: uuidv4(),
    name: 'Debian',
    version: '12',
    description: 'Debian 12 (bookworm)',
    status: 'published',
    category: 'os',
    price: 'Inclus',
    iconType: 'debian',
    displayOrder: 2,
    osType: 'Linux',
    isLTS: true,
    releaseDate: new Date('2023-06-10')
  },
  {
    osId: uuidv4(),
    name: 'AlmaLinux',
    version: '9',
    description: 'AlmaLinux 9',
    status: 'published',
    category: 'os',
    isPopular: true,
    price: 'Inclus',
    iconType: 'almalinux',
    displayOrder: 2,
    osType: 'Linux',
    isLTS: false,
    releaseDate: new Date('2022-05-26')
  },
  {
    osId: uuidv4(),
    name: 'Rocky Linux',
    version: '9',
    description: 'Rocky Linux 9',
    status: 'published',
    category: 'os',
    price: 'Inclus',
    iconType: 'rockylinux',
    displayOrder: 3,
    osType: 'Linux',
    isLTS: false,
    releaseDate: new Date('2022-07-14')
  },
  {
    osId: uuidv4(),
    name: 'Fedora',
    version: '42',
    description: 'Fedora 42',
    status: 'draft',
    category: 'os',
    price: 'Inclus',
    iconType: 'fedora',
    displayOrder: 4,
    osType: 'Linux',
    isLTS: false,
    releaseDate: new Date('2024-04-23')
  },
  {
    osId: uuidv4(),
    name: 'CentOS Stream',
    version: '9',
    description: 'CentOS Stream 9',
    status: 'published',
    category: 'os',
    price: 'Inclus',
    iconType: 'centos',
    displayOrder: 5,
    osType: 'Linux',
    isLTS: false,
    releaseDate: new Date('2021-12-03')
  }
];

// Fonction pour créer les données d'applications
const createAppImagesData = (osImages) => {
  const ubuntuId = osImages.find(os => os.name === 'Ubuntu')?.osId;
  const windowsId = osImages.find(os => os.name === 'Windows Server')?.osId;
  const debianId = osImages.find(os => os.name === 'Debian')?.osId;
  const rockyId = osImages.find(os => os.name === 'Rocky Linux')?.osId;
  const almaId = osImages.find(os => os.name === 'AlmaLinux')?.osId;

  return [
    {
      appId: uuidv4(),
      name: 'cPanel',
      description: 'cPanel control panel for web hosting management',
      status: 'published',
      category: 'apps',
      type: 'control_panel',
      iconType: 'cpanel',
      displayOrder: 1,
      supportedOs: [ubuntuId, rockyId, almaId].filter(Boolean),
      variants: [
        {
          variantId: uuidv4(),
          name: '5 Accounts',
          price: 270.00,
          description: 'cPanel with 5 user accounts'
        },
        {
          variantId: uuidv4(),
          name: 'Unlimited Accounts',
          price: 350.00,
          description: 'cPanel with unlimited user accounts'
        }
      ],
      dependencies: []
    },
    {
      appId: uuidv4(),
      name: 'Plesk',
      description: 'Plesk control panel for web hosting',
      status: 'published',
      category: 'apps',
      type: 'control_panel',
      iconType: 'plesk',
      displayOrder: 2,
      supportedOs: [ubuntuId, windowsId, debianId].filter(Boolean),
      variants: [
        {
          variantId: uuidv4(),
          name: 'Web Admin',
          price: 152.00,
          description: 'Plesk Web Admin Edition'
        },
        {
          variantId: uuidv4(),
          name: 'Web Pro',
          price: 205.00,
          description: 'Plesk Web Pro Edition'
        }
      ],
      dependencies: []
    },
    {
      appId: uuidv4(),
      name: 'LAMP Stack',
      description: 'A standard LAMP stack for web development',
      status: 'published',
      category: 'apps',
      type: 'lamp',
      iconType: 'custom',
      displayOrder: 3,
      supportedOs: [ubuntuId, debianId].filter(Boolean),
      variants: [
        {
          variantId: uuidv4(),
          name: 'Standard',
          price: 0.00,
          description: 'Standard LAMP stack (Linux, Apache, MySQL, PHP)'
        }
      ],
      dependencies: []
    },
    {
      appId: uuidv4(),
      name: 'Ethereum Node',
      description: 'A node for the Ethereum blockchain',
      status: 'published',
      category: 'blockchain',
      type: 'blockchain',
      iconType: 'blockchain',
      displayOrder: 1,
      supportedOs: [ubuntuId, debianId].filter(Boolean),
      variants: [
        {
          variantId: uuidv4(),
          name: 'Standard',
          price: 0.00,
          description: 'Ethereum node standard setup'
        }
      ],
      dependencies: []
    },
    {
      appId: uuidv4(),
      name: 'Docker',
      description: 'Docker containerization platform',
      status: 'draft',
      category: 'apps',
      type: 'lamp',
      iconType: 'custom',
      displayOrder: 4,
      supportedOs: [ubuntuId, debianId, rockyId].filter(Boolean),
      variants: [
        {
          variantId: uuidv4(),
          name: 'Community Edition',
          price: 0.00,
          description: 'Docker Community Edition'
        }
      ],
      dependencies: []
    }
  ];
};

// Fonction principale de seeding
async function seedVpsImages() {
  try {
    console.log('🌱 Début du seeding des images VPS...');

    // Connexion à MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connexion à MongoDB établie');

    // Supprimer les données existantes
    await VpsOS.deleteMany({});
    await VpsApp.deleteMany({});
    console.log('🗑️ Données existantes supprimées');

    // Insérer les images OS
    const osImages = await VpsOS.insertMany(osImagesData);
    console.log(`✅ ${osImages.length} images OS insérées`);

    // Créer et insérer les applications
    const appImagesData = createAppImagesData(osImages);
    const appImages = await VpsApp.insertMany(appImagesData);
    console.log(`✅ ${appImages.length} applications insérées`);

    // Afficher un résumé
    console.log('\n📊 RÉSUMÉ DU SEEDING :');
    console.log('='.repeat(40));
    
    // Statistiques OS
    const osStats = await VpsOS.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    console.log('Images OS :');
    osStats.forEach(stat => {
      console.log(`  - ${stat._id}: ${stat.count}`);
    });

    // Statistiques Apps
    const appStats = await VpsApp.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    console.log('Applications :');
    appStats.forEach(stat => {
      console.log(`  - ${stat._id}: ${stat.count}`);
    });

    // Statistiques par catégorie
    const categoryStats = await Promise.all([
      VpsOS.countDocuments({ isPopular: true }),
      VpsOS.countDocuments({ category: 'os' }),
      VpsApp.countDocuments({ category: 'apps' }),
      VpsApp.countDocuments({ category: 'blockchain' })
    ]);

    console.log('\nPar catégorie :');
    console.log(`  - Popular: ${categoryStats[0]}`);
    console.log(`  - OS: ${categoryStats[1]}`);
    console.log(`  - Apps: ${categoryStats[2]}`);
    console.log(`  - Blockchain: ${categoryStats[3]}`);

    console.log('\n🎉 Seeding terminé avec succès !');
    console.log('Vous pouvez maintenant tester le dashboard admin.');

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Connexion MongoDB fermée');
  }
}

// Exécuter le seeding si le script est appelé directement
if (require.main === module) {
  seedVpsImages();
}

module.exports = { seedVpsImages };
