"use client";
import { useState, useEffect, useMemo } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Typo<PERSON>, <PERSON><PERSON>, Card, CardBody } from "@material-tailwind/react";
import vpsService from "@/app/services/vpsService";
import cartService from "@/app/services/cartService";
import regionsService from "@/app/services/regionsService";
import packageService from "@/app/services/packageService";
import { vpsImagesPublicService, fileUtils } from "@/app/services/vpsImagesService";
import { useAuth } from "@/app/context/AuthContext";
import { toast } from "react-toastify";
import {
  ServerIcon,
  CpuIcon,
  HardDriveIcon,
  GlobeIcon,
  ShieldIcon,
  ClockIcon,
  CheckIcon,
  ArrowLeftIcon,
  MonitorIcon,
  TerminalIcon,
  UserIcon,
  KeyIcon,
  EyeIcon,
  EyeOffIcon,
  RefreshCwIcon,
} from "lucide-react";

// Modern OS Icons Components
const UbuntuIcon = ({ className }) => (
  <div
    className={`${className} bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center`}
  >
    <svg
      viewBox="0 0 24 24"
      className="w-3/4 h-3/4 text-white"
      fill="currentColor"
    >
      <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z" />
    </svg>
  </div>
);

const CentOSIcon = ({ className }) => (
  <div
    className={`${className} bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center`}
  >
    <svg
      viewBox="0 0 24 24"
      className="w-3/4 h-3/4 text-white"
      fill="currentColor"
    >
      <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z" />
    </svg>
  </div>
);

const DebianIcon = ({ className }) => (
  <div
    className={`${className} bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center`}
  >
    <svg
      viewBox="0 0 24 24"
      className="w-3/4 h-3/4 text-white"
      fill="currentColor"
    >
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
    </svg>
  </div>
);

const WindowsIcon = ({ className }) => (
  <div
    className={`${className} bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center`}
  >
    <svg
      viewBox="0 0 24 24"
      className="w-3/4 h-3/4 text-white"
      fill="currentColor"
    >
      <path d="M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z" />
    </svg>
  </div>
);

export default function ConfigureVPSPage() {
  console.log("🚀🚀🚀 ConfigureVPSPage LOADED - DYNAMIC REGIONS FROM CONTABO API - VERSION 2.0 🚀🚀🚀");

  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations("vps_configure");
  const { setCartCount } = useAuth();

  // State management
  const [vpsPlans, setVpsPlans] = useState([]);
  const [osImages, setOsImages] = useState([]);
  const [regions, setRegions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orderLoading, setOrderLoading] = useState(false);

  // New states for organized images
  const [organizedImages, setOrganizedImages] = useState({
    popular: [],
    os: [],
    apps: [],
    blockchain: []
  });
  const [imagesLoading, setImagesLoading] = useState(true);

  // Initialize plan from URL params
  const planId = searchParams.get("plan");
  const autoBackup = searchParams.get("autobackup") === "true";

  console.log("🔍 URL params - planId:", planId, "autoBackup:", autoBackup);

  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isAutoBackup, setIsAutoBackup] = useState(autoBackup);

  // Function to parse specifications from database
  const parseSpecifications = (specifications, description) => {
    let cores = 0,
      ram = "0 GB",
      storage = "0 GB",
      traffic = "32 TB";

    // Parse from specifications array
    if (specifications && Array.isArray(specifications)) {
      specifications.forEach((spec) => {
        const value = spec.value || "";
        const lowerValue = value.toLowerCase();

        // Parse CPU cores
        if (
          lowerValue.includes("cpu") ||
          lowerValue.includes("core") ||
          lowerValue.includes("vcpu")
        ) {
          const cpuMatch = value.match(/(\d+)/);
          if (cpuMatch) cores = parseInt(cpuMatch[1]);
        }

        // Parse RAM
        if (
          lowerValue.includes("ram") ||
          lowerValue.includes("memory") ||
          lowerValue.includes("gb ram")
        ) {
          const ramMatch = value.match(/(\d+)\s*gb/i);
          if (ramMatch) ram = `${ramMatch[1]} GB`;
        }

        // Parse Storage
        if (
          lowerValue.includes("storage") ||
          lowerValue.includes("disk") ||
          lowerValue.includes("ssd") ||
          lowerValue.includes("nvme")
        ) {
          const storageMatch = value.match(/(\d+)\s*gb/i);
          if (storageMatch) {
            const storageType = lowerValue.includes("nvme")
              ? "NVMe"
              : lowerValue.includes("ssd")
              ? "SSD"
              : "";
            storage = `${storageMatch[1]} GB ${storageType}`.trim();
          }
        }

        // Parse Traffic/Bandwidth
        if (
          lowerValue.includes("traffic") ||
          lowerValue.includes("bandwidth") ||
          lowerValue.includes("transfer")
        ) {
          const trafficMatch = value.match(/(\d+)\s*(tb|gb)/i);
          if (trafficMatch) {
            traffic = `${trafficMatch[1]} ${trafficMatch[2].toUpperCase()}`;
          }
        }
      });
    }

    // Fallback: parse from description if specifications are empty
    if (cores === 0 && description) {
      const descLower = description.toLowerCase();
      const cpuMatch = description.match(/(\d+)\s*(cpu|core|vcpu)/i);
      if (cpuMatch) cores = parseInt(cpuMatch[1]);

      const ramMatch = description.match(/(\d+)\s*gb\s*ram/i);
      if (ramMatch) ram = `${ramMatch[1]} GB`;

      const storageMatch = description.match(
        /(\d+)\s*gb\s*(storage|disk|ssd|nvme)/i
      );
      if (storageMatch) {
        const storageType = descLower.includes("nvme")
          ? "NVMe"
          : descLower.includes("ssd")
          ? "SSD"
          : "";
        storage = `${storageMatch[1]} GB ${storageType}`.trim();
      }
    }

    return { cores, ram, storage, traffic };
  };

  // Fetch VPS packages and find the selected one
  useEffect(() => {
    console.log("🔄 useEffect triggered with planId:", planId);

    const fetchVPSPackages = async () => {
      try {
        console.log("🔄 Starting fetchVPSPackages...");
        setLoading(true);

        // Récupérer les packages VPS depuis la base de données
        console.log("🔄 Calling packageService.getPackages('VPS Hosting')...");
        const response = await packageService.getPackages("VPS Hosting");
        console.log("✅ VPS packages response:", response);

        let vpsPackages = [];
        if (response.data && Array.isArray(response.data)) {
          vpsPackages = response.data;
        } else if (
          response.data &&
          response.data.packages &&
          Array.isArray(response.data.packages)
        ) {
          vpsPackages = response.data.packages;
        } else if (Array.isArray(response)) {
          vpsPackages = response;
        }

        // Transformer les packages de la base de données
        const transformedPlans = vpsPackages.map((pkg) => {
          const specs = parseSpecifications(
            pkg.specifications,
            pkg.description
          );
          return {
            id: pkg._id,
            _id: pkg._id,
            name: pkg.name,
            price: pkg.price,
            cores: specs.cores,
            ram: specs.ram,
            storage: specs.storage,
            traffic: specs.traffic,
            description: pkg.description,
            specifications: pkg.specifications,
          };
        });

        setVpsPlans(transformedPlans);

        // Trouver le package sélectionné par son ID
        if (planId && transformedPlans.length > 0) {
          const foundPlan = transformedPlans.find(
            (plan) =>
              plan._id === planId ||
              plan.id === planId ||
              plan._id?.toString() === planId ||
              plan.id?.toString() === planId
          );
          if (foundPlan) {
            setSelectedPlan(foundPlan);
            console.log("Selected plan found:", foundPlan);
          } else {
            console.error("Plan not found with ID:", planId);
            console.log(
              "Available plans:",
              transformedPlans.map((p) => ({
                id: p.id,
                _id: p._id,
                name: p.name,
              }))
            );
            setError("Package VPS non trouvé");
          }
        }
      } catch (error) {
        console.error("❌ Error fetching VPS plans:", error);
        console.error("❌ Error details:", error.message, error.stack);
        setError("Erreur lors du chargement des plans VPS");
        setVpsPlans([]);
        setSelectedPlan(null);
      } finally {
        console.log("🔄 fetchVPSPackages completed, setting loading to false");
        setLoading(false);
      }
    };

    // Fetch VPS images from our new API
    const fetchVPSImages = async () => {
      try {
        setImagesLoading(true);
        console.log("🔍 Fetching VPS images from new API...");

        const response = await vpsImagesPublicService.getAll();
        console.log("✅ VPS Images response:", response);

        if (response.success && response.data) {
          // Set organized images for tabs
          setOrganizedImages(response.data.organized);

          // Set legacy osImages for backward compatibility (use OS images only)
          setOsImages(response.data.all.os || []);

          console.log("🔄 Images loaded:", {
            popular: response.data.organized.popular?.length || 0,
            os: response.data.organized.os?.length || 0,
            apps: response.data.organized.apps?.length || 0,
            blockchain: response.data.organized.blockchain?.length || 0
          });
        } else {
          console.warn("⚠️ No images data received");
          setOsImages([]);
          setOrganizedImages({ popular: [], os: [], apps: [], blockchain: [] });
        }
      } catch (error) {
        console.error("❌ Error fetching VPS images:", error);
        // Fallback to empty data
        setOsImages([]);
        setOrganizedImages({ popular: [], os: [], apps: [], blockchain: [] });
      } finally {
        setImagesLoading(false);
      }
    };

    // Fetch dynamic regions from our admin API
    const fetchRegions = async () => {
      try {
        console.log("🔍 Fetching regions from admin API...");
        console.log("🔍 Backend URL:", process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002');
        console.log("🔍 Making request to regionsService.getActiveRegions()...");

        // Try direct fetch first to debug
        console.log("🔍 Testing direct fetch...");
        try {
          const directResponse = await fetch(`http://localhost:5002/api/regions?t=${Date.now()}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include'
          });

          console.log("✅ Direct fetch response status:", directResponse.status);
          console.log("✅ Direct fetch response ok:", directResponse.ok);

          if (!directResponse.ok) {
            throw new Error(`HTTP ${directResponse.status}: ${directResponse.statusText}`);
          }

          const directData = await directResponse.json();
          console.log("✅ Direct fetch response data:", directData);

          // If direct fetch works, use its data
          if (directData && directData.success && Array.isArray(directData.data)) {
            console.log("🔄 Using direct fetch data instead of regionsService");
            const response = { data: directData };

            // Continue with the existing logic...
            console.log("✅ Regions response:", response);
            console.log("✅ Response type:", typeof response);
            console.log("✅ Response keys:", Object.keys(response || {}));

            let regions = [];
            console.log("🔍 Checking response.data:", response.data);
            console.log("🔍 Is response.data an array?", Array.isArray(response.data));

            if (response.data && Array.isArray(response.data)) {
              console.log("🔍 Raw regions from API:", response.data.length, "regions");
              response.data.forEach((region, index) => {
                console.log(`   ${index + 1}. ${region.name} (${region.regionId}) - Status: ${region.status}`);
              });

              // Filter regions that have pricing for the selected package
              regions = response.data
                .filter(region => {
                  // Check if this region has pricing configured for the selected package
                  if (!selectedPlan) {
                    console.log(`🔍 Region ${region.name}: No selected plan, skipping`);
                    return false;
                  }

                  const selectedPackageId = selectedPlan._id || selectedPlan.id;
                  const pricingForPackage = region.pricing?.find(p => {
                    const pId = typeof p.packageId === 'object' ? p.packageId._id || p.packageId.toString() : p.packageId;
                    return pId === selectedPackageId || pId === selectedPackageId?.toString();
                  });

                  const hasPricingForPackage = !!pricingForPackage;
                  console.log(`🔍 Region ${region.name}: Has pricing for ${selectedPlan.name}: ${hasPricingForPackage}`);

                  return hasPricingForPackage;
                })
                .sort((a, b) => {
                  // Sort by displayOrder first, then by name
                  if (a.displayOrder !== b.displayOrder) {
                    return (a.displayOrder || 0) - (b.displayOrder || 0);
                  }
                  return a.name.localeCompare(b.name);
                });

              console.log("🔍 Filtered regions:", regions.length, "regions");
            } else {
              console.log("❌ response.data is not an array or is missing");
            }

            // Transform API data to expected format
            console.log("🔄 Transforming regions, selectedPlan:", selectedPlan?._id || 'undefined');
            const transformedRegions = regions.map((region, index) => {
              const transformed = {
                id: region.regionId,
                name: region.name,
                country: region.country,
                city: region.city,
                flag: region.flag,
                continent: region.continent,
                description: `${region.city}, ${region.country}`,
                isPopular: region.isPopular,
                pricing: region.pricing || [],
                // Calculate additional price for current package if available
                additionalPrice: selectedPlan ? region.pricing?.find(p => {
                  const pId = typeof p.packageId === 'object' ? p.packageId._id || p.packageId.toString() : p.packageId;
                  const selectedId = selectedPlan._id || selectedPlan.id;
                  return pId === selectedId || pId === selectedId?.toString();
                })?.additionalPrice || 0 : 0
              };
              console.log(`🔄 Transformed region ${index + 1}:`, {
                id: transformed.id,
                name: transformed.name,
                country: transformed.country,
                city: transformed.city,
                flag: transformed.flag,
                additionalPrice: transformed.additionalPrice
              });
              return transformed;
            });

            console.log("🔄 Setting regions:", transformedRegions.length, "regions");
            setRegions(transformedRegions);

            // Add a visual indicator that regions were loaded
            if (transformedRegions.length > 0) {
              console.log("🎉 SUCCESS: Regions loaded successfully!");
              // Temporarily show an alert to confirm it's working
              // alert(`✅ Regions loaded: ${transformedRegions.length} regions found!`);
            }

            // Set default region if available (prefer popular regions first)
            if (transformedRegions.length > 0) {
              const popularRegion = transformedRegions.find(region => region.isPopular);
              const defaultRegion = popularRegion || transformedRegions[0];
              console.log("🔄 Setting default region:", defaultRegion.name, defaultRegion.id);
              setSelectedLocation(defaultRegion.id);
            } else {
              console.log("⚠️ No transformed regions to set as default");
            }

            return; // Skip the regionsService call
          }
        } catch (directError) {
          console.error("❌ Direct fetch failed:", directError);
        }

        console.log("🔍 Falling back to regionsService...");
        // This fallback is for debugging - normally regionsService should work
        console.log("⚠️ Direct fetch worked but regionsService might have issues");
      } catch (error) {
        console.error("❌ Error fetching regions:", error);
        console.error("❌ Error type:", error.constructor.name);
        console.error("❌ Error message:", error.message);
        console.error("❌ Error response:", error.response);
        console.error("❌ Error response status:", error.response?.status);
        console.error("❌ Error response data:", error.response?.data);
        console.error("❌ Error config:", error.config);

        // Check if it's a CORS or network error
        if (error.code === 'ERR_NETWORK' || error.message.includes('CORS')) {
          console.error("❌ NETWORK/CORS ERROR: Cannot reach backend at", error.config?.baseURL);
        }

        // Pas de fallback - les régions doivent être configurées par l'admin
        setRegions([]);
        console.log("⚠️ Setting regions to empty array due to error");
      }
    };

    // Appeler les fonctions pour récupérer les données
    console.log("🔄 Calling fetchVPSPackages...");
    fetchVPSPackages();
    console.log("🔄 Calling fetchVPSImages...");
    fetchVPSImages();
    console.log("🔄 Calling fetchRegions...");
    fetchRegions();


  }, [planId]);

  // Fetch all active regions with pricing (dynamic from Contabo API + configured prices)
  // Load regions independently of selected plan, but get pricing for current plan
  useEffect(() => {
    const fetchAllActiveRegionsWithPricing = async () => {
      try {

        // First, get dynamic regions from Contabo API
        console.log("🔍 Calling vpsService.getRegions('contabo')...");
        const dynamicResponse = await vpsService.getRegions('contabo');
        console.log("🔍 Dynamic regions response:", dynamicResponse);

        // Then, get configured regions with pricing from admin
        console.log("🔍 Calling regionsService.getActiveRegions()...");
        const configuredResponse = await regionsService.getActiveRegions();
        console.log("🔍 Configured regions response:", configuredResponse);

        // Extract dynamic regions
        let dynamicRegions = [];
        if (Array.isArray(dynamicResponse.data)) {
          dynamicRegions = dynamicResponse.data;
        } else if (dynamicResponse.data && Array.isArray(dynamicResponse.data.data)) {
          dynamicRegions = dynamicResponse.data.data;
        }

        // Extract configured regions with pricing
        let configuredRegions = [];
        console.log("🔍 Configured response structure:", {
          hasData: !!configuredResponse.data,
          dataType: typeof configuredResponse.data,
          hasDataData: !!(configuredResponse.data && configuredResponse.data.data),
          dataDataType: configuredResponse.data && typeof configuredResponse.data.data,
          isDataArray: Array.isArray(configuredResponse.data),
          isDataDataArray: configuredResponse.data && Array.isArray(configuredResponse.data.data),
          fullResponse: configuredResponse.data
        });

        // L'API retourne { success: true, data: [...] }
        // Donc nous devons accéder à configuredResponse.data.data
        if (configuredResponse.data && configuredResponse.data.success && Array.isArray(configuredResponse.data.data)) {
          configuredRegions = configuredResponse.data.data;
          console.log("✅ Found configured regions:", configuredRegions.length);
        } else if (Array.isArray(configuredResponse.data)) {
          configuredRegions = configuredResponse.data;
          console.log("✅ Found configured regions (direct array):", configuredRegions.length);
        } else {
          console.log("❌ No configured regions found");
        }

        console.log("🔍 Found", dynamicRegions.length, "dynamic regions and", configuredRegions.length, "configured regions");
        console.log("🔍 Configured region IDs:", configuredRegions.map(cr => cr.regionId));
        console.log("🔍 Configured regions details:", configuredRegions.map(cr => ({
          regionId: cr.regionId,
          name: cr.name,
          hasPricing: cr.pricing?.length > 0,
          pricingDetails: cr.pricing?.map(p => ({
            packageId: typeof p.packageId === 'object' ? p.packageId._id : p.packageId,
            packageName: p.packageName,
            additionalPrice: p.additionalPrice
          }))
        })));
        console.log("🔍 Selected plan details:", {
          id: selectedPlan?._id,
          name: selectedPlan?.name,
          price: selectedPlan?.price
        });

        if (dynamicRegions.length > 0) {
          // Transform dynamic regions and merge with configured pricing
          const transformedRegions = dynamicRegions.map((dynamicRegion, index) => {
            const regionId = dynamicRegion.regionSlug || dynamicRegion.regionId || dynamicRegion.id;
            // Convert to lowercase to match database format (regionId is stored in lowercase)
            const normalizedRegionId = regionId.toLowerCase();

            console.log(`🔍 Dynamic region ${index + 1}:`, {
              name: dynamicRegion.name,
              originalId: regionId,
              normalizedId: normalizedRegionId,
              regionSlug: dynamicRegion.regionSlug,
              regionIdField: dynamicRegion.regionId,
              id: dynamicRegion.id
            });

            // Find configured pricing for this region using normalized ID
            const configuredRegion = configuredRegions.find(cr => cr.regionId === normalizedRegionId);
            console.log(`🔍 Looking for configured region with ID "${normalizedRegionId}":`, configuredRegion ? 'FOUND ✅' : 'NOT FOUND ❌');

            // Get pricing for current selected plan
            let additionalPrice = 0;
            if (selectedPlan && configuredRegion && configuredRegion.pricing) {
              console.log(`🔍 Matching packages for region ${normalizedRegionId}:`);
              console.log(`   Selected plan ID: "${selectedPlan._id || selectedPlan.id}"`);
              console.log(`   Selected plan name: "${selectedPlan.name}"`);
              console.log(`   Available pricing entries:`, configuredRegion.pricing.map(p => ({
                packageId: typeof p.packageId === 'object' ? p.packageId._id || p.packageId.toString() : p.packageId,
                packageName: p.packageName,
                additionalPrice: p.additionalPrice
              })));

              const packagePricing = configuredRegion.pricing.find(p => {
                const pId = typeof p.packageId === 'object' ? p.packageId._id || p.packageId.toString() : p.packageId;
                const selectedId = selectedPlan._id || selectedPlan.id;
                const match = pId === selectedId || pId === selectedId?.toString();
                console.log(`   Comparing "${pId}" === "${selectedId}": ${match}`);
                return match;
              });
              additionalPrice = packagePricing?.additionalPrice || 0;
              console.log(`💰 Region ${normalizedRegionId} - Package ${selectedPlan.name} - Additional price: ${additionalPrice} MAD ${additionalPrice > 0 ? '✅' : '⚠️'}`);
            } else {
              console.log(`💰 Region ${normalizedRegionId} - No pricing found (selectedPlan: ${!!selectedPlan}, configuredRegion: ${!!configuredRegion}, hasPricing: ${!!(configuredRegion?.pricing)})`);
              if (configuredRegion?.pricing) {
                console.log(`   Pricing array length: ${configuredRegion.pricing.length}`);
              }
            }

            const transformed = {
              id: regionId, // Keep original ID for Contabo API compatibility
              name: dynamicRegion.regionName || dynamicRegion.name || dynamicRegion.displayName,
              country: dynamicRegion.country || configuredRegion?.country || 'Unknown',
              city: dynamicRegion.city || dynamicRegion.regionName || dynamicRegion.name,
              flag: configuredRegion?.flag || dynamicRegion.flag || '🌍',
              continent: dynamicRegion.continent || configuredRegion?.continent || 'Unknown',
              description: dynamicRegion.description || `${dynamicRegion.city || dynamicRegion.regionName || dynamicRegion.name}`,
              isPopular: configuredRegion?.isPopular || dynamicRegion.isPopular || false,
              additionalPrice: additionalPrice, // Use configured pricing
              dataCenters: dynamicRegion.dataCenters || [],
              capabilities: dynamicRegion.capabilities || [],
              supportsVPS: dynamicRegion.supportsVPS !== false,
              hasConfiguredPricing: !!configuredRegion // Flag to show if pricing is configured
            };

            console.log(`🔄 Region ${index + 1}: ${transformed.name} (${transformed.id}) - Additional price: ${transformed.additionalPrice} MAD`);
            return transformed;
          });

          setRegions(transformedRegions);

          // Set default region if available (prefer popular regions first)
          if (transformedRegions.length > 0) {
            const popularRegion = transformedRegions.find(region => region.isPopular);
            const defaultRegion = popularRegion || transformedRegions[0];
            console.log("🔄 Setting default region:", defaultRegion.name);
            setSelectedLocation(defaultRegion.id);
          }

          console.log("🎉 SUCCESS: Dynamic regions with pricing loaded!");
        } else {
          console.log("⚠️ No dynamic regions found from Contabo API");
          setRegions([]);
        }
      } catch (error) {
        console.error("❌ Error fetching dynamic regions with pricing:", error);
        console.error("❌ Error stack:", error.stack);
        setRegions([]);
      }
    };

    console.log("🚀 About to call fetchAllActiveRegionsWithPricing...");

    // Call the function directly now that we know it works
    fetchAllActiveRegionsWithPricing()
      .then(() => {
        console.log("🚀 fetchAllActiveRegionsWithPricing completed successfully");
      })
      .catch((error) => {
        console.error("🚀 fetchAllActiveRegionsWithPricing failed:", error);
      });

    console.log("🚀 useEffect for regions is ending...");
  }, [selectedPlan]); // Re-run when selectedPlan changes to recalculate pricing

  // Configuration state
  const [selectedOS, setSelectedOS] = useState("ubuntu-20.04");
  const [selectedImageTab, setSelectedImageTab] = useState('popular'); // popular, os, apps, blockchain
  const [selectedLocation, setSelectedLocation] = useState("france");
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [additionalIPs, setAdditionalIPs] = useState(0);
  const [backupEnabled, setBackupEnabled] = useState(autoBackup);
  const [quantity, setQuantity] = useState(1);

  // New Contabo-style options
  const [autoBackupOption, setAutoBackupOption] = useState("none");
  const [privateNetworking, setPrivateNetworking] = useState("none");
  const [ipv4Addresses, setIpv4Addresses] = useState(1);
  const [objectStorage, setObjectStorage] = useState("none");
  const [serverManagement, setServerManagement] = useState("unmanaged");
  const [monitoring, setMonitoring] = useState("none");
  const [ssl, setSsl] = useState("none");

  // Server login credentials
  const [serverUsername, setServerUsername] = useState("root");
  const [serverPassword, setServerPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // Generate random password function
  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setServerPassword(password);
  };

  // Handle adding VPS to cart
  const handleAddToCart = async () => {
    if (!selectedPlan) {
      toast.error("Veuillez sélectionner un plan VPS");
      return;
    }

    try {
      setOrderLoading(true);

      // Vérifier si nous avons un ID valide
      const packageId = selectedPlan._id || selectedPlan.id;

      if (!packageId) {
        toast.error("ID du package VPS manquant. Veuillez réessayer.");
        return;
      }

      // Map frontend selections to Contabo API format
      const contaboRegionMap = {
        france: "EU",
        EU: "EU",
        germany: "EU",
        "US-central": "US-east",
        usa: "US-east",
        SG: "SIN",
        singapore: "SIN",
        asia: "SIN",
      };

      const contaboOSMap = {
        "ubuntu-20.04": "ubuntu-20.04",
        "ubuntu-22.04": "ubuntu-22.04",
        "ubuntu-24.04": "ubuntu-24.04",
        "centos-7": "centos-7",
        "centos-8": "centos-8",
        "debian-10": "debian-10",
        "debian-11": "debian-11",
        "windows-2019": "windows-server-2019",
        "windows-2022": "windows-server-2022",
      };

      // Generate display name if not provided
      const displayName = `${selectedPlan.name}-${Date.now()}`;

      // Préparer les données pour l'ajout au panier avec configuration Contabo
      const cartData = {
        packageId: packageId,
        quantity: quantity,
        period:
          selectedPeriod === "monthly"
            ? 1
            : selectedPeriod === "6months"
            ? 6
            : 12,
        // Configuration personnalisée pour Contabo VPS
        customConfiguration: {
          // Contabo API fields
          planId: selectedPlan.vpsConfig?.providerProductId || selectedPlan.id, // V91, V92, etc.
          provider: "contabo",
          region: contaboRegionMap[selectedLocation] || "EU",
          operatingSystem: contaboOSMap[selectedOS] || selectedOS,
          displayName: displayName,
          sshKeys: [], // Will be added later if user provides
          userData: "", // Cloud-init script if needed
          addons: {
            privatenetworking: privateNetworking !== "none",
            autobackup: autoBackupOption !== "none",
            monitoring: monitoring !== "none",
          },
          // Plan specifications for reference
          cpu: selectedPlan.cores || selectedPlan.cpu,
          ram: selectedPlan.ram,
          storage: selectedPlan.storage,
          bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,
          // Frontend-specific fields for display
          frontendConfig: {
            operatingSystem: selectedOS,
            location: selectedLocation,
            additionalIPs: additionalIPs,
            backup: isAutoBackup || backupEnabled,
            planName: selectedPlan.name,
            autoBackupOption: autoBackupOption,
            privateNetworking: privateNetworking,
            ipv4Addresses: ipv4Addresses,
            objectStorage: objectStorage,
            serverManagement: serverManagement,
            monitoring: monitoring,
            ssl: ssl,
          },
        },
      };

      console.log("Adding VPS to cart:", cartData);
      console.log("Selected plan:", selectedPlan);

      // Ajouter au panier via le service
      const response = await cartService.addItemToCart(cartData);

      console.log("Cart response:", response);

      // Mettre à jour le compteur du panier
      if (response.data?.cart?.cartCount) {
        setCartCount(response.data.cart.cartCount);
      }

      // Afficher le message de succès
      toast.success(`${selectedPlan.name} ajouté au panier avec succès!`);

      // Rediriger vers le panier
      router.push("/client/cart");
    } catch (error) {
      console.error("Error adding VPS to cart:", error);
      console.error("Error response:", error.response);
      console.error("Error data:", error.response?.data);

      // Gestion des erreurs spécifiques
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.response?.status === 404) {
        toast.error("Package VPS non trouvé. Veuillez contacter le support.");
      } else if (error.response?.status === 401) {
        toast.error("Veuillez vous connecter pour ajouter au panier.");
        router.push("/auth/login");
      } else if (error.response?.status === 400) {
        toast.error("Données invalides. Veuillez vérifier votre sélection.");
      } else {
        toast.error("Erreur lors de l'ajout au panier. Veuillez réessayer.");
      }
    } finally {
      setOrderLoading(false);
    }
  };

  // Use dynamic OS images data with fallback to static data
  const operatingSystems =
    osImages.length > 0
      ? osImages.map((os) => ({
          id: os.id,
          name: os.name,
          icon: getOSIcon(os.type || os.osType),
          type: os.type || os.osType || "linux",
          description: os.description,
          version: os.version,
          provider: os.provider,
        }))
      : [
          {
            id: "ubuntu-20.04",
            name: "Ubuntu 20.04 LTS",
            icon: UbuntuIcon,
            type: "linux",
          },
          {
            id: "ubuntu-22.04",
            name: "Ubuntu 22.04 LTS",
            icon: UbuntuIcon,
            type: "linux",
          },
          { id: "centos-8", name: "CentOS 8", icon: CentOSIcon, type: "linux" },
          {
            id: "debian-11",
            name: "Debian 11",
            icon: DebianIcon,
            type: "linux",
          },
        ];

  // Helper function to get OS icon based on type or custom icon
  function getOSIcon(image) {
    // If image has a custom icon URL, use it
    if (image.iconUrl) {
      return ({ className }) => (
        <div className={`${className} rounded-lg overflow-hidden flex items-center justify-center bg-gray-100`}>
          <img
            src={fileUtils.getIconUrl(image.iconUrl)}
            alt={image.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to default icon if image fails to load
              e.target.style.display = 'none';
              e.target.parentNode.innerHTML = '<div class="w-full h-full bg-gray-300 flex items-center justify-center text-gray-600 text-xs">Icon</div>';
            }}
          />
        </div>
      );
    }

    // Default icon mapping based on iconType
    const iconMap = {
      'ubuntu': UbuntuIcon,
      'centos': CentOSIcon,
      'debian': DebianIcon,
      'windows': WindowsIcon,
      'almalinux': CentOSIcon, // Similar to CentOS
      'rockylinux': CentOSIcon, // Similar to CentOS
      'fedora': UbuntuIcon, // Similar to Ubuntu
      'cpanel': ({ className }) => (
        <div className={`${className} bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center text-white font-bold text-xs`}>
          cPanel
        </div>
      ),
      'plesk': ({ className }) => (
        <div className={`${className} bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center text-white font-bold text-xs`}>
          Plesk
        </div>
      ),
      'blockchain': ({ className }) => (
        <div className={`${className} bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center text-white`}>
          ⛓️
        </div>
      ),
      'custom': ({ className }) => (
        <div className={`${className} bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white`}>
          📦
        </div>
      )
    };

    return iconMap[image.iconType] || iconMap.custom;
  }

  // Transform API data to expected format
  const transformImageForDisplay = (image) => {
    const isApp = !!image.appId;

    return {
      id: image.osId || image.appId,
      name: image.name,
      icon: getOSIcon(image),
      version: isApp ? `${image.name} v${image.version || '1.0'}` : `${image.name} ${image.version}`,
      price: image.price || 'Inclus',
      description: image.description,
      type: image.osType?.toLowerCase() || 'linux',
      isLTS: image.isLTS || false,
      baseOS: isApp && image.supportedOsDetails?.length > 0 ?
        image.supportedOsDetails[0].name + ' ' + image.supportedOsDetails[0].version :
        undefined,
      variants: image.variants || [],
      supportedOs: image.supportedOs || [],
      category: image.category,
      iconUrl: image.iconUrl
    };
  };

  // Get dynamic image categories from API data
  const getImageCategories = () => {
    if (imagesLoading) {
      return { popular: [], os: [], apps: [], blockchain: [] };
    }

    return {
      popular: organizedImages.popular?.map(transformImageForDisplay) || [],
      os: organizedImages.os?.map(transformImageForDisplay) || [],
      apps: organizedImages.apps?.map(transformImageForDisplay) || [],
      blockchain: organizedImages.blockchain?.map(transformImageForDisplay) || []
    };
  };

  // Get current image categories (dynamic or fallback to static)
  const imageCategories = getImageCategories();



  // Use dynamic regions data from Contabo API with configured pricing
  console.log("🔄 Creating locations from regions:", regions.length, "regions");
  console.log("🔄 Selected plan for pricing:", selectedPlan?.name, selectedPlan?._id);

  const locations = useMemo(() => {
    console.log(`🔄 Recalculating locations for package: ${selectedPlan?.name} (ID: ${selectedPlan?._id})`);

    return regions.map((region, index) => {
      // Use configured pricing from admin panel - this should already be calculated for the selected plan
      const additionalPrice = region.additionalPrice || 0;

      const location = {
        id: region.id,
        name: region.name,
        flag: region.flag || getRegionFlag(region.country),
        ping: getRegionPing(region.id),
        description: region.description,
        additionalPrice: additionalPrice,
        isPopular: region.isPopular || false,
        continent: region.continent,
        city: region.city,
        country: region.country,
        dataCenters: region.dataCenters || [],
        capabilities: region.capabilities || [],
        supportsVPS: region.supportsVPS !== false,
        hasConfiguredPricing: region.hasConfiguredPricing || false
      };
      console.log(`🔄 Location ${index + 1}:`, {
        id: location.id,
        name: location.name,
        flag: location.flag,
        description: location.description,
        additionalPrice: location.additionalPrice,
        packageId: selectedPlan?._id
      });
      return location;
    });
  }, [regions, selectedPlan]) // Recalculate when regions or selectedPlan changes;

  console.log("🔄 Final locations array:", locations.length, "locations");

  // Helper functions for region display
  function getRegionFlag(country) {
    const flagMap = {
      Germany: "🇩🇪",
      "United States": "🇺🇸",
      Singapore: "🇸🇬",
      France: "🇫🇷",
      Netherlands: "🇳🇱",
    };
    return flagMap[country] || "🌍";
  }

  function getRegionPing(regionId) {
    const pingMap = {
      EU: "15ms",
      "US-central": "120ms",
      "US-east": "110ms",
      "US-west": "130ms",
      SG: "200ms",
    };
    return pingMap[regionId] || "50ms";
  }

  const calculateTotal = () => {
    if (!selectedPlan) return 0;
    let total = selectedPlan.price;

    // Additional IPs cost
    total += additionalIPs * 15; // 15 MAD per additional IP

    // Backup cost (only if not auto backup plan)
    if (!isAutoBackup && backupEnabled) {
      total += 20; // 20 MAD for backup
    }

    // Auto Backup cost (Contabo style)
    if (autoBackupOption === "auto") {
      total += 18; // €1.79 ≈ 18 MAD per month
    }

    // Private Networking cost
    if (privateNetworking === "enabled") {
      total += 28; // 28 MAD per month
    }

    // IPv4 additional addresses cost
    if (ipv4Addresses === 2) {
      total += 42; // 42 MAD per additional IP
    }

    // Object Storage cost
    const objectStorageCosts = {
      "250gb": 50,
      "500gb": 95,
      "750gb": 140,
      "1tb": 180,
    };
    if (objectStorage !== "none" && objectStorageCosts[objectStorage]) {
      total += objectStorageCosts[objectStorage];
    }

    // Server Management cost
    if (serverManagement === "managed") {
      total += 1340; // 1340 MAD per month for managed
    }

    // Monitoring cost
    if (monitoring === "full") {
      total += 140; // 140 MAD per month for full monitoring
    }

    // SSL cost (one-time charges, but we'll add monthly equivalent)
    const sslCosts = {
      basic: 76, // 914 MAD / 12 months ≈ 76 MAD per month
      wildcard: 228, // 2740 MAD / 12 months ≈ 228 MAD per month
    };
    if (ssl !== "none" && sslCosts[ssl]) {
      total += sslCosts[ssl];
    }

    // Region additional cost
    const selectedRegion = locations.find(loc => loc.id === selectedLocation);
    if (selectedRegion && selectedRegion.additionalPrice > 0) {
      total += selectedRegion.additionalPrice; // Price is already in MAD
    }

    // Apply quantity
    total *= quantity;

    // Period multiplier
    const multipliers = {
      monthly: 1,
      "6months": 6 * 0.97, // 3% discount
      annually: 12 * 0.9, // 10% discount
    };

    return total * multipliers[selectedPeriod];
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <Typography className="text-gray-600">
            Loading VPS configuration...
          </Typography>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Typography variant="h6" className="text-red-600 mb-2">
            Error loading VPS configuration
          </Typography>
          <Typography className="text-gray-600 mb-4">{error}</Typography>
          <Button
            onClick={() => window.location.reload()}
            color="blue"
            size="sm"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Mobile Optimized */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
              <Button
                variant="outlined"
                size="sm"
                onClick={() => window.history.back()}
                className="border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0"
              >
                <ArrowLeftIcon className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">{t("back")}</span>
              </Button>
              <div className="min-w-0 flex-1">
                <Typography
                  variant="h4"
                  className="text-lg sm:text-2xl text-gray-900 font-bold truncate"
                >
                  {t("page_title")}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 truncate">
                  {selectedPlan?.name || "Loading..."}
                </Typography>
              </div>
            </div>
            <div className="text-left sm:text-right w-full sm:w-auto flex-shrink-0">
              <Typography className="text-xs sm:text-sm text-gray-500">
                {t("price_from")}
              </Typography>
              <Typography
                variant="h3"
                className="text-lg sm:text-2xl text-blue-600 font-bold"
              >
                {selectedPlan?.price || 0} MAD{t("per_month")}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Configuration Panel - Mobile Optimized */}
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">
            {/* Plan Summary - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                  <div className="w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                    <ServerIcon className="w-5 sm:w-6 h-5 sm:h-6 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <Typography
                      variant="h5"
                      className="text-lg sm:text-xl text-gray-900 font-bold truncate"
                    >
                      {selectedPlan?.name || "Loading..."}
                    </Typography>
                    {isAutoBackup && (
                      <div className="flex items-center gap-2 mt-1">
                        <ShieldIcon className="w-3 sm:w-4 h-3 sm:h-4 text-green-600" />
                        <span className="text-xs sm:text-sm text-green-600 font-medium">
                          Auto Backup Inclus
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <CpuIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">
                      {t("vcpu_cores")}
                    </div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">
                      {selectedPlan?.cores || 0}
                    </div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2"></div>
                    <div className="text-xs sm:text-sm text-gray-600">
                      {t("ram")}
                    </div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">
                      {selectedPlan?.ram || "0 GB"}
                    </div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <HardDriveIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">
                      {t("storage")}
                    </div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">
                      {selectedPlan?.storage || "0 GB"}
                    </div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <GlobeIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">
                      {t("traffic")}
                    </div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">
                      {selectedPlan?.traffic || "0 TB"}
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Billing Period - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography
                  variant="h6"
                  className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4"
                >
                  1. {t("billing_period")}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t("billing_period_desc")}
                </Typography>
                <div className="space-y-3">
                  {[
                    {
                      id: "monthly",
                      label: "1 mois",
                      discount: "",
                      price: selectedPlan?.price || 0,
                    },
                    {
                      id: "6months",
                      label: "6 mois",
                      discount: "3% de réduction",
                      price: Math.round((selectedPlan?.price || 0) * 6 * 0.97),
                    },
                    {
                      id: "annually",
                      label: "12 mois",
                      discount: "10% de réduction",
                      price: Math.round((selectedPlan?.price || 0) * 12 * 0.9),
                    },
                  ].map((period) => (
                    <div
                      key={period.id}
                      onClick={() => setSelectedPeriod(period.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedPeriod === period.id
                          ? "border-blue-600 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                          <div
                            className={`w-4 h-4 rounded-full border-2 flex-shrink-0 ${
                              selectedPeriod === period.id
                                ? "border-blue-600 bg-blue-600"
                                : "border-gray-300"
                            }`}
                          >
                            {selectedPeriod === period.id && (
                              <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-sm sm:text-base text-gray-900">
                              {period.label}
                            </div>
                            {period.discount && (
                              <div className="text-xs sm:text-sm text-green-600 font-medium">
                                {period.discount}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0">
                          <div className="font-bold text-sm sm:text-base text-gray-900">
                            {period.price} MAD
                          </div>
                          <div className="text-xs sm:text-sm text-gray-500">
                            {period.id === "monthly"
                              ? "/mois"
                              : period.id === "6months"
                              ? "/6 mois"
                              : "/an"}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Image Selection - Contabo Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  2. Image
                </Typography>

                {/* Tabs */}
                <div className="flex border-b border-gray-200 mb-6">
                  {[
                    { id: 'popular', label: 'Popular' },
                    { id: 'os', label: 'OS' },
                    { id: 'apps', label: 'Apps & Panels' },
                    { id: 'blockchain', label: 'Blockchain' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setSelectedImageTab(tab.id)}
                      className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                        selectedImageTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      {tab.label}
                      <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {imageCategories[tab.id]?.length || 0}
                      </span>
                    </button>
                  ))}
                </div>

                {/* Images Grid - Exactly like Contabo */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {imageCategories[selectedImageTab]?.map((image) => (
                    <div
                      key={image.id}
                      onClick={() => setSelectedOS(image.id)}
                      className={`relative p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedOS === image.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {/* Radio button */}
                      <div className="absolute top-4 left-4">
                        <div className={`w-5 h-5 rounded-full border ${
                          selectedOS === image.id
                            ? 'border-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {selectedOS === image.id && (
                            <div className="w-3 h-3 bg-blue-500 rounded-full m-1"></div>
                          )}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="text-center pt-8">
                        {/* Icon */}
                        <div className="flex justify-center mb-4">
                          <image.icon className="w-16 h-16" />
                        </div>

                        {/* Name */}
                        <div className="font-medium text-gray-900 mb-3" title={image.name}>
                          {image.name}
                        </div>

                        {/* Version dropdown */}
                        <div className="mb-4">
                          <select className="w-full px-3 py-2 border border-gray-300 rounded text-sm bg-white">
                            <option title={image.version}>{image.version}</option>
                          </select>
                        </div>

                        {/* Price */}
                        <div className={`font-medium text-base ${
                          image.price === 'Inclus' ? 'text-gray-600' : 'text-gray-900'
                        }`}>
                          {image.price}
                        </div>

                        {/* Base OS for apps */}
                        {image.baseOS && (
                          <div className="mt-3">
                            <select className="w-full px-3 py-2 border border-gray-300 rounded text-sm bg-white">
                              <option>{image.baseOS}</option>
                            </select>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>


              </CardBody>
            </Card>

            {/* Location Selection - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography
                  variant="h6"
                  className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4"
                >
                  3. {t("choose_location")}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t("choose_location_desc")}
                </Typography>
                {(() => {
                  console.log("🔄 Render check - regions.length:", regions.length, "locations.length:", locations.length);
                  return locations.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-gray-500">⚠️ Aucune région disponible</div>
                      <div className="text-xs text-gray-400 mt-1">L'administrateur doit configurer les régions dans le panneau admin</div>
                    </div>
                  ) : null;
                })()}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {locations.map((location) => (
                    <div
                      key={location.id}
                      onClick={() => setSelectedLocation(location.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedLocation === location.id
                          ? "border-blue-600 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <div className="font-medium text-sm sm:text-base text-gray-900 truncate">{location.name}</div>
                              {location.isPopular && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                  Popular
                                </span>
                              )}
                            </div>
                            <div className="text-xs sm:text-sm text-gray-500 truncate">{location.description}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
                          {location.additionalPrice > 0 && (
                            <div className="text-right">
                              <div className="text-sm sm:text-base font-semibold text-gray-900">
                                +{location.additionalPrice.toFixed(2)} MAD
                              </div>
                            </div>
                          )}
                          {selectedLocation === location.id && (
                            <CheckIcon className="w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* 4. Data Protection with Auto Backup - Improved Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography
                  variant="h6"
                  className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4"
                >
                  4. Data Protection with Auto Backup
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Auto Backup Option */}
                  <div
                    onClick={() => setAutoBackupOption("auto")}
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      autoBackupOption === "auto"
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-blue-300 hover:shadow-sm"
                    }`}
                  >
                    {autoBackupOption === "auto" && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg
                            className="w-4 h-4 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3">
                        Notre Recommandation
                      </div>
                      <div className="font-bold text-xl text-gray-900 mb-2">
                        Auto Backup
                      </div>
                      <div className="text-blue-600 font-bold text-lg mb-3">
                        18 MAD/mois
                      </div>
                      <div className="text-gray-600 mb-2 font-medium">
                        Set it and forget it.
                      </div>
                      <div className="text-gray-500 text-sm mb-4">
                        Data security with no effort
                      </div>

                      <div className="bg-white rounded-lg p-4 space-y-3 text-sm border">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Mode</span>
                          <span className="font-semibold text-gray-900">
                            automated
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Frequency</span>
                          <span className="font-semibold text-gray-900">
                            daily
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Recovery</span>
                          <span className="font-semibold text-gray-900">
                            1-Click Recovery
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">
                            Backup Retention
                          </span>
                          <span className="font-semibold text-gray-900">
                            10 days
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* None Option */}
                  <div
                    onClick={() => setAutoBackupOption("none")}
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      autoBackupOption === "none"
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-blue-300 hover:shadow-sm"
                    }`}
                  >
                    {autoBackupOption === "none" && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg
                            className="w-4 h-4 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="font-bold text-xl text-gray-900 mb-2 mt-8">
                        None
                      </div>
                      <div className="text-green-600 font-bold text-lg mb-6">
                        Free
                      </div>

                      <div className="bg-white rounded-lg p-4 space-y-3 text-sm border">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Mode</span>
                          <span className="font-semibold text-gray-900">
                            manual
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Frequency</span>
                          <span className="font-semibold text-gray-900">
                            on demand
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Recovery</span>
                          <span className="font-semibold text-gray-900">
                            manual
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">
                            Backup Retention
                          </span>
                          <span className="font-semibold text-gray-900">-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* 5. Networking - Contabo Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography
                  variant="h6"
                  className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4"
                >
                  5. Networking
                </Typography>

                <div className="space-y-4">
                  {/* Private Networking */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      Private Networking
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={privateNetworking}
                        onChange={(e) => setPrivateNetworking(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">No Private Networking</option>
                        <option value="enabled">
                          Private Networking Enabled
                        </option>
                      </select>
                      <div className="min-w-[60px] text-right">
                        {privateNetworking === "enabled" ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            28 MAD
                          </span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Bandwidth */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      Bandwidth
                    </span>
                    <div className="text-right">
                      <div className="font-medium text-sm sm:text-base text-gray-900">
                        32 TB Out + Unlimited In
                      </div>
                      <div className="text-xs sm:text-sm text-purple-600">
                        200 Mbit/s Connection
                      </div>
                    </div>
                  </div>

                  {/* IPv4 */}
                  <div className="flex items-center justify-between py-2">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      IPv4
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={ipv4Addresses}
                        onChange={(e) =>
                          setIpv4Addresses(parseInt(e.target.value))
                        }
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value={1}>1 IP Address</option>
                        <option value={2}>
                          1 IP Address + 1 Additional IP
                        </option>
                      </select>
                      <div className="min-w-[60px] text-right">
                        {ipv4Addresses === 2 ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            42 MAD
                          </span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* 6. Add-Ons - Contabo Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography
                  variant="h6"
                  className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4"
                >
                  6. Add-Ons
                </Typography>

                <div className="space-y-4">
                  {/* Object Storage */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      Object Storage
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={objectStorage}
                        onChange={(e) => setObjectStorage(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="250gb">250 GB Object Storage</option>
                        <option value="500gb">500 GB Object Storage</option>
                        <option value="750gb">750 GB Object Storage</option>
                        <option value="1tb">1 TB Object Storage</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {objectStorage === "250gb" && (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            50 MAD
                          </span>
                        )}
                        {objectStorage === "500gb" && (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            95 MAD
                          </span>
                        )}
                        {objectStorage === "750gb" && (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            140 MAD
                          </span>
                        )}
                        {objectStorage === "1tb" && (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            180 MAD
                          </span>
                        )}
                        {objectStorage === "none" && (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Server Management */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      Server Management
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={serverManagement}
                        onChange={(e) => setServerManagement(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="unmanaged">Unmanaged</option>
                        <option value="managed">Managed</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {serverManagement === "unmanaged" ? (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            1340 MAD
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Monitoring */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      Monitoring
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={monitoring}
                        onChange={(e) => setMonitoring(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="full">Full Monitoring</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {monitoring === "full" ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">
                            140 MAD
                          </span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* SSL */}
                  <div className="flex items-center justify-between py-2">
                    <span className="font-medium text-sm sm:text-base text-gray-900">
                      SSL
                    </span>
                    <div className="flex items-center gap-3">
                      <select
                        value={ssl}
                        onChange={(e) => setSsl(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="basic">SSL certificate</option>
                        <option value="wildcard">
                          SSL certificate (wildcard)
                        </option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {ssl === "basic" && (
                          <div className="text-right">
                            <div className="text-sm sm:text-base font-bold text-gray-900">
                              914 MAD
                            </div>
                            <div className="text-xs sm:text-sm text-gray-500">
                              One off charge
                            </div>
                          </div>
                        )}
                        {ssl === "wildcard" && (
                          <div className="text-right">
                            <div className="text-sm sm:text-base font-bold text-gray-900">
                              2740 MAD
                            </div>
                            <div className="text-xs sm:text-sm text-gray-500">
                              One off charge
                            </div>
                          </div>
                        )}
                        {ssl === "none" && (
                          <span className="text-sm sm:text-base font-bold text-green-600">
                            Free
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* 7. Login & password for your server */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  7. Login & password for your server
                </Typography>

                <div className="space-y-4">
                  {/* Username */}
                  <div>
                    <Typography className="text-sm font-medium text-gray-700 mb-2">
                      Username
                    </Typography>
                    <input
                      type="text"
                      value={serverUsername}
                      onChange={(e) => setServerUsername(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="root"
                    />
                  </div>

                  {/* Password */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Typography className="text-sm font-medium text-gray-700">
                        Password
                      </Typography>
                      <button
                        type="button"
                        onClick={generatePassword}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium underline"
                      >
                        Generate new password
                      </button>
                    </div>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        value={serverPassword}
                        onChange={(e) => setServerPassword(e.target.value)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="8-30 alphanumeric characters (no special characters)"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showPassword ? (
                          <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                    {(!serverPassword || serverPassword.length < 8) && (
                      <div className="flex items-center gap-1 mt-2">
                        <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <Typography className="text-sm text-red-600">
                          Please enter a valid password
                        </Typography>
                      </div>
                    )}
                  </div>

                  {/* Information text */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <Typography className="text-sm text-gray-600 leading-relaxed">
                      In order to use SSH Keys you can add them in the Customer Control Panel later.
                      Your password will not be sent via email. Be sure to remember it for Windows access.
                      If you forget the password, you will need to reinstall your server.
                    </Typography>
                  </div>
                </div>
              </CardBody>
            </Card>

          </div>

          {/* Order Summary - Fixed Position */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-20 lg:max-h-[calc(100vh-6rem)] lg:overflow-hidden">
              <Card className="shadow-lg h-full flex flex-col">
                <CardBody className="p-4 sm:p-6 flex flex-col h-full">
                  <Typography
                    variant="h6"
                    className="text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0"
                  >
                    {t("order_summary")}
                  </Typography>

                  <div className="flex-1 overflow-y-auto">
                    <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                      <div className="flex justify-between items-start">
                        <div className="min-w-0 flex-1 pr-2">
                          <span className="text-sm sm:text-base text-gray-600">
                            {selectedPlan?.name || "Loading..."}
                          </span>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs text-gray-500">
                              Quantité:
                            </span>
                            <div className="flex items-center gap-1">
                              <Button
                                size="sm"
                                variant="outlined"
                                onClick={() =>
                                  setQuantity(Math.max(1, quantity - 1))
                                }
                                disabled={quantity === 1}
                                className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                              >
                                -
                              </Button>
                              <span className="w-8 text-center font-medium text-xs">
                                {quantity}
                              </span>
                              <Button
                                size="sm"
                                variant="outlined"
                                onClick={() =>
                                  setQuantity(Math.min(10, quantity + 1))
                                }
                                disabled={quantity === 10}
                                className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                              >
                                +
                              </Button>
                            </div>
                          </div>
                        </div>
                        <span className="font-medium text-sm sm:text-base flex-shrink-0">
                          {(selectedPlan?.price || 0) * quantity} MAD
                        </span>
                      </div>

                      {additionalIPs > 0 && (
                        <div className="flex justify-between items-start">
                          <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">
                            IPs additionnelles ({additionalIPs} × {quantity})
                          </span>
                          <span className="font-medium text-sm sm:text-base flex-shrink-0">
                            {additionalIPs * 15 * quantity} MAD
                          </span>
                        </div>
                      )}

                  {!isAutoBackup && backupEnabled && (
                    <div className="flex justify-between items-start">
                      <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">Sauvegarde automatique × {quantity}</span>
                      <span className="font-medium text-sm sm:text-base flex-shrink-0">{20 * quantity} MAD</span>
                    </div>
                  )}

                  {(() => {
                    const selectedRegion = locations.find(loc => loc.id === selectedLocation);
                    return selectedRegion && selectedRegion.additionalPrice > 0 ? (
                      <div className="flex justify-between items-start">
                        <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">Région {selectedRegion.name} × {quantity}</span>
                        <span className="font-medium text-sm sm:text-base flex-shrink-0">+{(selectedRegion.additionalPrice * quantity).toFixed(2)} MAD</span>
                      </div>
                    ) : null;
                  })()}

                      {selectedPeriod !== "monthly" && (
                        <div className="flex justify-between items-start text-green-600">
                          <span className="text-sm sm:text-base min-w-0 flex-1 pr-2">
                            Réduction (
                            {selectedPeriod === "6months" ? "3%" : "10%"})
                          </span>
                          <span className="text-sm sm:text-base flex-shrink-0">
                            -
                            {Math.round(
                              (selectedPlan?.price || 0) *
                                quantity *
                                (selectedPeriod === "6months"
                                  ? 6 * 0.03
                                  : 12 * 0.1)
                            )}{" "}
                            MAD
                          </span>
                        </div>
                      )}

                      <hr className="border-gray-200" />

                      <div className="flex justify-between items-start text-base sm:text-lg font-bold">
                        <span className="min-w-0 flex-1 pr-2">
                          {t("total")}
                        </span>
                        <span className="text-blue-600 flex-shrink-0 text-right">
                          {Math.round(calculateTotal())} MAD/
                          {selectedPeriod === "monthly"
                            ? "mois"
                            : selectedPeriod === "6months"
                            ? "6 mois"
                            : "an"}
                        </span>
                      </div>

                      {/* Configuration Summary - Mobile Optimized */}
                      <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200">
                        <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                          <div className="flex flex-wrap">
                            <strong>{t("os_label")}:</strong>&nbsp;
                            <span className="break-all">
                              {
                                operatingSystems.find(
                                  (os) => os.id === selectedOS
                                )?.name
                              }
                            </span>
                          </div>
                          <div className="flex flex-wrap">
                            <strong>{t("location_label")}:</strong>&nbsp;
                            <span className="break-all">
                              {
                                locations.find(
                                  (loc) => loc.id === selectedLocation
                                )?.name
                              }
                            </span>
                          </div>
                          <div className="flex flex-wrap">
                            <strong>Période:</strong>&nbsp;
                            <span className="break-all">
                              {selectedPeriod === "monthly"
                                ? "1 mois"
                                : selectedPeriod === "6months"
                                ? "6 mois"
                                : "12 mois"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Button
                      size="lg"
                      className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold"
                      onClick={handleAddToCart}
                      disabled={orderLoading || loading || !selectedPlan}
                    >
                      {orderLoading ? "Ajout en cours..." : "Ajouter au panier"}
                    </Button>

                    <div className="text-center">
                      <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2">
                        <ShieldIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                        <span>Paiement sécurisé</span>
                      </div>
                      <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500">
                        <ClockIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                        <span>Déploiement en 5 minutes</span>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
