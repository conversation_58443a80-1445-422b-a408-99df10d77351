/**
 * Test script to verify the site settings fixes
 * Run this with: node test-settings-fix.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the model
const SiteSettings = require('./models/SiteSettings');

async function testSettingsFix() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://0.0.0.0:27017/zn_ztech');
    console.log('✅ Connected to database');

    // Clean up any existing settings for fresh test
    await SiteSettings.deleteMany({});
    console.log('🧹 Cleaned up existing settings');

    // Test 1: Create settings with only general section
    console.log('\n📝 Test 1: Create with only general section');
    const generalOnlyData = {
      general: {
        siteName: 'Test Site',
        contactEmail: '<EMAIL>',
        phone: '+1234567890'
      }
    };

    const settings1 = await SiteSettings.create(generalOnlyData);
    console.log('✅ Created settings with only general section:', {
      id: settings1._id,
      siteName: settings1.general.siteName,
      hasGeneral: !!settings1.general,
      hasSeo: !!settings1.seo
    });

    // Test 2: Update with only SEO section
    console.log('\n📝 Test 2: Update with only SEO section');
    settings1.seo = {
      defaultTitle: 'Updated SEO Title',
      defaultDescription: 'Updated SEO Description',
      googleAnalyticsId: 'G-TEST123456'
    };
    settings1.markModified('seo');
    await settings1.save();
    console.log('✅ Updated with SEO section:', {
      defaultTitle: settings1.seo.defaultTitle,
      googleAnalyticsId: settings1.seo.googleAnalyticsId
    });

    // Test 3: Partial update of general section
    console.log('\n📝 Test 3: Partial update of general section');
    settings1.general = { ...settings1.general, siteName: 'Partially Updated Site' };
    settings1.markModified('general');
    await settings1.save();
    console.log('✅ Partial update successful:', {
      siteName: settings1.general.siteName,
      contactEmail: settings1.general.contactEmail // Should still exist
    });

    // Test 4: Test with invalid email (should fail)
    console.log('\n📝 Test 4: Test validation with invalid email');
    try {
      const invalidSettings = new SiteSettings({
        general: {
          siteName: 'Invalid Email Test',
          contactEmail: 'invalid-email-format'
        }
      });
      await invalidSettings.save();
      console.log('❌ Validation should have failed for invalid email');
    } catch (validationError) {
      console.log('✅ Validation correctly caught invalid email:', validationError.message);
    }

    // Test 5: Test with invalid Google Analytics ID (should fail)
    console.log('\n📝 Test 5: Test validation with invalid Google Analytics ID');
    try {
      const invalidGASettings = new SiteSettings({
        seo: {
          defaultTitle: 'Test',
          defaultDescription: 'Test',
          googleAnalyticsId: 'INVALID-GA-ID'
        }
      });
      await invalidGASettings.save();
      console.log('❌ Validation should have failed for invalid GA ID');
    } catch (validationError) {
      console.log('✅ Validation correctly caught invalid GA ID:', validationError.message);
    }

    // Test 6: Test with valid data (should succeed)
    console.log('\n📝 Test 6: Test with completely valid data');
    const validSettings = await SiteSettings.create({
      general: {
        siteName: 'Valid Test Site',
        siteDescription: 'A valid test site',
        contactEmail: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Test Street',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/test',
          twitter: 'https://twitter.com/test',
          facebook: 'https://facebook.com/test'
        }
      },
      seo: {
        defaultTitle: 'Valid Test Site - Home',
        defaultDescription: 'This is a valid test site for testing purposes',
        defaultKeywords: 'test, valid, site',
        favicon: 'https://example.com/favicon.ico',
        googleAnalyticsId: 'G-VALID123456',
        googleSiteVerification: 'valid-verification-code',
        bingVerification: 'valid-bing-code',
        robotsTxt: 'User-agent: *\nAllow: /',
        sitemapEnabled: true
      }
    });
    console.log('✅ Created completely valid settings:', {
      id: validSettings._id,
      siteName: validSettings.general.siteName,
      defaultTitle: validSettings.seo.defaultTitle
    });

    console.log('\n🎉 All tests passed! The settings model is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    if (error.name === 'ValidationError') {
      console.error('Validation errors:');
      Object.values(error.errors).forEach(err => {
        console.error(`- ${err.path}: ${err.message}`);
      });
    }
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');
  }
}

// Run the test
console.log('🚀 Testing site settings fixes...\n');
testSettingsFix();
