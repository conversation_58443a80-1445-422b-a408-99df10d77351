/**
 * Simple test script to verify site settings functionality
 * Run this with: node test-site-settings.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the model
const SiteSettings = require('./models/SiteSettings');

async function testSiteSettings() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://0.0.0.0:27017/zn_ztech');
    console.log('✅ Connected to database');

    // Test 1: Get or create default settings
    console.log('\n📝 Test 1: Get or create default settings');
    const defaultSettings = await SiteSettings.getSettings();
    console.log('Default settings created/retrieved:', {
      siteName: defaultSettings.general.siteName,
      defaultTitle: defaultSettings.seo.defaultTitle,
      id: defaultSettings._id
    });

    // Test 2: Update settings
    console.log('\n📝 Test 2: Update settings');
    defaultSettings.general.siteName = 'Updated Site Name';
    defaultSettings.seo.defaultTitle = 'Updated SEO Title';
    defaultSettings.seo.googleTagManagerId = 'GTM-TEST123456';
    
    await defaultSettings.save();
    console.log('Settings updated successfully');

    // Test 3: Retrieve updated settings
    console.log('\n📝 Test 3: Retrieve updated settings');
    const updatedSettings = await SiteSettings.findOne();
    console.log('Retrieved updated settings:', {
      siteName: updatedSettings.general.siteName,
      defaultTitle: updatedSettings.seo.defaultTitle,
      googleAnalyticsId: updatedSettings.seo.googleAnalyticsId
    });

    // Test 4: Validation test (should fail)
    console.log('\n📝 Test 4: Validation test');
    try {
      const invalidSettings = new SiteSettings({
        general: {
          siteName: '', // This should fail validation
          contactEmail: 'invalid-email' // This should fail validation
        }
      });
      await invalidSettings.save();
      console.log('❌ Validation test failed - invalid data was saved');
    } catch (validationError) {
      console.log('✅ Validation working correctly:', validationError.message);
    }

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');
  }
}

// Run the test
testSiteSettings();
