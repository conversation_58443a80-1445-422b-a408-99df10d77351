const express = require('express');
const router = express.Router();
const {
  getAllRegions,
  getRegion,
  createRegion,
  updateRegion,
  deleteRegion,
  getRegionsForPackage,
  getActiveRegions
} = require('../../controllers/admin/regionController');
const vpsPricingController = require('../../controllers/admin/vpsPricingController');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

// Admin routes (protected)
router.use(authenticateToken);
router.use(requireAdmin);

// Routes pour la gestion des régions VPS
console.log('VPS Regions routes loaded');
router.get('/', getAllRegions);
router.get('/active', getActiveRegions);
router.get('/:id', getRegion);
router.post('/', (req, res, next) => {
  console.log('POST /api/admin/vps-regions called');
  createRegion(req, res, next);
});
router.put('/:id', updateRegion);
router.delete('/:id', deleteRegion);
router.get('/package/:packageId', getRegionsForPackage);

// NOUVELLES ROUTES - Système de pricing séparé
router.post('/pricing', (req, res, next) => {
  console.log('🚀 POST /api/admin/vps-regions/pricing called');
  console.log('📦 Request body:', req.body);
  console.log('👤 User:', req.user ? req.user._id : 'No user');
  vpsPricingController.saveVPSPrice(req, res, next);
});
router.get('/pricing', vpsPricingController.getAllVPSPrices);
router.get('/pricing/:regionId/:packageId', vpsPricingController.getVPSPrice);

module.exports = router;
