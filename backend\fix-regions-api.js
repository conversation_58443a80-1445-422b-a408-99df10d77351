// Script pour corriger les régions via l'API
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Simuler une authentification admin (vous devrez adapter selon votre système)
const adminHeaders = {
  'Content-Type': 'application/json',
  // Ajoutez ici vos headers d'authentification si nécessaire
  // 'Authorization': 'Bearer YOUR_TOKEN'
};

async function fixRegionsViaAPI() {
  try {
    console.log('🔧 Fixing VPS regions via API...');

    // 1. Récupérer tous les packages VPS
    console.log('\n📦 Getting VPS packages...');
    const packagesResponse = await axios.get(`${API_BASE}/packages`, { headers: adminHeaders });
    const vpsPackages = packagesResponse.data.filter(pkg => 
      pkg.name === 'CLOUD VPS 10' || pkg.name === 'CLOUD VPS 20'
    );

    console.log('VPS Packages found:');
    vpsPackages.forEach(pkg => {
      console.log(`  ${pkg.name}: ${pkg._id}`);
    });

    if (vpsPackages.length !== 2) {
      console.error('❌ Expected 2 VPS packages, found:', vpsPackages.length);
      return;
    }

    // 2. Récupérer toutes les régions VPS
    console.log('\n🌍 Getting VPS regions...');
    const regionsResponse = await axios.get(`${API_BASE}/admin/vps-regions`, { headers: adminHeaders });
    const regions = regionsResponse.data;

    console.log(`Found ${regions.length} regions to check`);

    // 3. Pour chaque région, s'assurer qu'elle a une entrée pour chaque package
    for (const region of regions) {
      console.log(`\n🔍 Checking region: ${region.name} (${region.regionId})`);
      console.log(`   Current pricing entries: ${region.pricing?.length || 0}`);

      const existingPackageIds = (region.pricing || []).map(p => p.packageId);
      
      for (const pkg of vpsPackages) {
        const hasEntry = existingPackageIds.includes(pkg._id);
        
        if (!hasEntry) {
          console.log(`   ➕ Adding missing entry for ${pkg.name}...`);
          
          try {
            await axios.post(`${API_BASE}/admin/vps-regions`, {
              regionId: region.regionId,
              packageId: pkg._id,
              additionalPrice: 0
            }, { headers: adminHeaders });
            
            console.log(`   ✅ Added ${pkg.name} with price 0 MAD`);
          } catch (error) {
            console.error(`   ❌ Failed to add ${pkg.name}:`, error.response?.data || error.message);
          }
        } else {
          const existingPrice = region.pricing.find(p => p.packageId === pkg._id)?.additionalPrice || 0;
          console.log(`   ✅ ${pkg.name}: ${existingPrice} MAD (already exists)`);
        }
      }
    }

    console.log('\n🎉 Region fixing completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

fixRegionsViaAPI();
