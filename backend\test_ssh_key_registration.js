const ContaboProvider = require('./services/providers/ContaboProvider');

async function testSSHKeyRegistration() {
  console.log('🧪 Testing SSH Key Registration with Contabo API...\n');

  try {
    const contaboProvider = new ContaboProvider();

    // Test SSH key (example ED25519 key format)
    const testSSHKey = 'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGQw7+X9J5K8H2L3M4N5O6P7Q8R9S0T1U2V3W4X5Y6Z7 <EMAIL>';

    console.log('📝 Test SSH Key:', testSSHKey.substring(0, 50) + '...');

    // Test 1: Register SSH key
    console.log('\n🔑 Test 1: Registering SSH key...');
    const uniqueName = `Test-SSH-Key-${Date.now()}`;
    const sshKeyId = await contaboProvider.registerSSHKey(testSSHKey, uniqueName);
    console.log(`✅ SSH key registered successfully with ID: ${sshKeyId}`);

    // Test 2: Test advanced reinstallation with SSH key
    console.log('\n🔄 Test 2: Testing advanced reinstallation with SSH key...');

    // Get available images first
    const images = await contaboProvider.getImages();
    if (images.length === 0) {
      throw new Error('No images available for testing');
    }

    const selectedImage = images[0];
    console.log(`📀 Using image: ${selectedImage.name} (ID: ${selectedImage.imageId})`);

    // Prepare advanced reinstall data with SSH key
    const advancedReinstallData = {
      imageId: selectedImage.imageId,
      password: 'TestSSHKey123!',
      enableRootUser: true,
      sshKeys: [testSSHKey], // Raw SSH key - should be converted to ID
      userData: `#cloud-config
# Configuration Cloud-Init pour test SSH
packages:
  - htop
  - curl
  - git
ssh_authorized_keys:
  - ${testSSHKey}`,
      customScript: `#!/bin/bash
echo "SSH key test script executed" >> /root/ssh_test.log
echo "SSH key ID used: ${sshKeyId}" >> /root/ssh_test.log`
    };

    console.log('\n📤 Advanced reinstall data prepared:');
    console.log('- Image ID:', advancedReinstallData.imageId);
    console.log('- Password:', advancedReinstallData.password ? '***' : 'Not set');
    console.log('- Enable Root User:', advancedReinstallData.enableRootUser);
    console.log('- SSH Keys:', advancedReinstallData.sshKeys.length, 'key(s)');
    console.log('- User Data:', advancedReinstallData.userData ? 'Set' : 'Not set');
    console.log('- Custom Script:', advancedReinstallData.customScript ? 'Set' : 'Not set');

    // Note: We won't actually reinstall a real VPS in this test
    console.log('\n⚠️ Note: Actual VPS reinstallation skipped in test mode');
    console.log('✅ SSH key registration and payload preparation successful!');

    // Test 3: Verify SSH key can be retrieved
    console.log('\n🔍 Test 3: Verifying SSH key registration...');
    console.log(`✅ SSH key ID ${sshKeyId} can be used for reinstallation`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testSSHKeyRegistration();