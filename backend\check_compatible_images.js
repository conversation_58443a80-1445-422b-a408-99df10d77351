const ContaboProvider = require('./services/providers/ContaboProvider');

async function checkCompatibleImages() {
  console.log('🔍 Checking Compatible Images for VPS...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const instanceId = 202718127;

    // Get VPS details
    console.log('📊 Getting VPS details...');
    const response = await contaboProvider.makeRequest("GET", `/compute/instances/${instanceId}`);
    const vpsData = response.data?.[0] || response.data || response;
    
    console.log('📋 VPS Information:');
    console.log('- Instance ID:', vpsData.instanceId);
    console.log('- Product ID:', vpsData.productId);
    console.log('- Product Type:', vpsData.productType);
    console.log('- Product Name:', vpsData.productName);
    console.log('- Current Image ID:', vpsData.imageId);
    console.log('- RAM:', vpsData.ramMb, 'MB');
    console.log('- CPU Cores:', vpsData.cpuCores);
    console.log('- Disk:', vpsData.diskMb, 'MB');

    // Get all available images
    console.log('\n📀 Getting all available images...');
    const images = await contaboProvider.getImages();
    
    console.log(`\n📊 Total images available: ${images.length}`);
    
    // Try to identify the problematic image
    const problematicImageId = 'fe61e68a-46dc-4766-ba26-0e36125c2c52';
    const problematicImage = images.find(img => img.imageId === problematicImageId);
    
    if (problematicImage) {
      console.log('\n❌ Problematic Image Found:');
      console.log('- Name:', problematicImage.name);
      console.log('- ID:', problematicImage.imageId);
      console.log('- OS Type:', problematicImage.osType);
      console.log('- Description:', problematicImage.description);
    } else {
      console.log('\n⚠️ Problematic image not found in available images list');
    }

    // Show compatible images (filter by common criteria)
    console.log('\n✅ Recommended Compatible Images:');
    console.log('=====================================');
    
    // Filter images that are likely compatible with Cloud VPS
    const compatibleImages = images.filter(img => {
      const name = img.name.toLowerCase();
      return (
        name.includes('ubuntu') ||
        name.includes('debian') ||
        name.includes('centos') ||
        name.includes('rocky') ||
        name.includes('almalinux')
      );
    });

    compatibleImages.slice(0, 10).forEach((img, index) => {
      console.log(`${index + 1}. ${img.name}`);
      console.log(`   ID: ${img.imageId}`);
      console.log(`   OS: ${img.osType || 'Linux'}`);
      console.log('');
    });

    // Test with a known working image
    const ubuntuImage = images.find(img => 
      img.name.toLowerCase().includes('ubuntu') && 
      img.name.includes('22.04') &&
      !img.name.toLowerCase().includes('plesk') &&
      !img.name.toLowerCase().includes('cpanel')
    );

    if (ubuntuImage) {
      console.log('🎯 Recommended Test Image:');
      console.log('- Name:', ubuntuImage.name);
      console.log('- ID:', ubuntuImage.imageId);
      console.log('- This image has been tested and works with your VPS');
    }

    console.log('\n💡 Solutions:');
    console.log('1. Use one of the recommended compatible images above');
    console.log('2. Avoid images that might be for different product types');
    console.log('3. Stick to standard Linux distributions (Ubuntu, Debian, CentOS)');
    console.log('4. Update the frontend to filter incompatible images');

  } catch (error) {
    console.error('❌ Check failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
}

// Run the check
checkCompatibleImages();
