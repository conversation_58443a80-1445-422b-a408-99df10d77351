/**
 * VPS IDs REFERENCE - CONTABO
 * Script organisé contenant tous les IDs nécessaires pour créer un VPS
 * Basé sur les endpoints de Reda : http://localhost:5002/vps/
 */

// ========================================
// 🖼️ IMAGE IDs (OS SYSTEMS)
// ========================================

const VPS_IMAGES = {
  // UBUNTU
  ubuntu: {
    "24.04": "d64d5c6c-9dda-4e38-8174-0ee282474d8a",
    "22.04": "afecbb85-e2fc-46f0-9684-b46b1faf00bb", 
    "20.04": "db1409d2-ed92-4f2f-978e-7b2fa4a1ec90"
  },

  // WINDOWS SERVER
  windows: {
    "2025-datacenter": "ef27e2fa-188f-4767-964b-7543fea74968",
    "2025-standard": "5af826e8-0e9d-4cec-9728-0966f98b4565",
    "2022-datacenter": "3b4102d0-f259-4496-bb4f-66173a8a61a5",
    "2022-standard": "b5549695-970e-491a-827d-b314170154db",
    "2019-datacenter": "511200d3-7924-4443-8ad9-a041870d513e",
    "2019-standard": "c60df48f-c37a-4694-bc1a-f6165eedb587"
  },

  // LINUX DISTRIBUTIONS
  almalinux: {
    "9": "81d9280e-8753-40ae-8aef-7f6e20751b85",
    "8": "e63957b1-9b18-4e57-b557-5d352f33ec23"
  },

  rockylinux: {
    "9": "fe6c2c36-031e-4474-aa5c-c5297196c80e",
    "8": "984507e9-5085-4f9b-91b2-13fad578d6fb"
  },

  debian: {
    "12": "4efbc0ba-2313-4fe1-842a-516f8652e729",
    "11": "66abf39a-ba8b-425e-a385-8eb347ceac10"
  },

  // AUTRES DISTRIBUTIONS
  centos: {
    "9-stream": "8b7c9b2a-ca59-48a2-92ea-5180779183cc"
  },

  archlinux: {
    "rolling": "69b52ee3-2fda-4f44-b8de-69e480d87c7d"
  },

  opensuse: {
    "leap-15.6": "909a1e95-7204-45e0-8844-9a2bdaa16381"
  },

  freebsd: {
    "14.0": "67a63682-6817-41a1-9b87-c8adb4072f27",
    "13.2": "08400f44-f4db-4241-8930-2d3022cb2752"
  },

  fedora: {
    "42": "f4752284-c311-4ee9-8e76-f818eac08f44",
    "41": "9b509bd0-7a81-4142-9fba-66c0cc451cb8"
  }
};

// ========================================
// 🎛️ APPLICATIONS & PANELS
// ========================================

const VPS_APPLICATIONS = {
  // cPANEL
  cpanel: {
    "ubuntu-22.04": "7a8bffde-6721-44c0-ac03-c10796f455f8",
    "ubuntu-20.04": "1c1609d3-e366-4085-a08d-23018ce55fcc",
    "rockylinux-9": "768a7a22-a9a0-4bb8-911f-6d4a876919b9",
    "rockylinux-8": "c663d72c-61be-4ee4-80a0-983ecb6f6646",
    "almalinux-9": "d2f13640-5315-44c5-a23b-1f9a90618caf",
    "almalinux-8": "44ce19cc-67e7-4abf-8334-df58840c28df"
  },

  // PLESK
  plesk: {
    "ubuntu-22.04": "cfa7fcc8-30ae-4612-a831-3c652292b197",
    "ubuntu-20.04": "6e401d7a-1b06-4455-9ffb-b8e1dd873664",
    "debian-11": "c4a41e33-a711-4aa7-88e9-24b631a066e7",
    "rockylinux-8": "75d9e3bf-439b-4dc5-a10e-0450e9f853ad",
    "almalinux-9": "80a9d897-7389-43c3-a9d5-7725851832a9",
    "almalinux-8": "b335f930-cda5-4bbb-a940-4ce75b0c9da5",
    "windows-2025-datacenter": "47c791bf-5fcb-4dbb-a957-8ef7a56766b7",
    "windows-2025-standard": "fe61e68a-46dc-4766-ba26-0e36125c2c52",
    "windows-2022-datacenter": "5703b683-140b-46bb-9014-3ca3dfe82b08",
    "windows-2022-standard": "73c69c28-45dd-426a-8ac9-b10a554c51a7",
    "windows-2019-datacenter": "3cc517ff-bb24-464e-8c33-92c69b427380",
    "windows-2019-standard": "c5370a26-0026-4b1f-8736-57a6cd9223cd",
    "windows-2016-datacenter": "21580f1e-0744-4a48-a191-341757c0bfce",
    "windows-2016-standard": "f761b5c6-0584-45e6-b69f-ba384845f52f"
  }
};

// ========================================
// 📦 PLAN IDs (VPS PACKAGES)
// ========================================

const VPS_PLANS = {
  "V91": {
    id: "V91",
    name: "VPS 10 NVMe",
    cpu: 1,
    ram: 4,
    storage: 75,
    diskType: "NVMe",
    bandwidth: 32000,
    price: { monthly: 4.99, hourly: 0.007 }
  },
  "V92": {
    id: "V92", 
    name: "VPS 10 SSD",
    cpu: 1,
    ram: 4,
    storage: 150,
    diskType: "SSD",
    bandwidth: 32000,
    price: { monthly: 4.99, hourly: 0.007 }
  },
  "V94": {
    id: "V94",
    name: "VPS 20 NVMe", 
    cpu: 2,
    ram: 8,
    storage: 100,
    diskType: "NVMe",
    bandwidth: 32000,
    price: { monthly: 8.99, hourly: 0.013 }
  },
  "V95": {
    id: "V95",
    name: "VPS 20 SSD",
    cpu: 2,
    ram: 8, 
    storage: 200,
    diskType: "SSD",
    bandwidth: 32000,
    price: { monthly: 8.99, hourly: 0.013 }
  },
  "V97": {
    id: "V97",
    name: "VPS 30 NVMe",
    cpu: 4,
    ram: 16,
    storage: 200, 
    diskType: "NVMe",
    bandwidth: 32000,
    price: { monthly: 16.99, hourly: 0.025 }
  },
  "V8": {
    id: "V8",
    name: "VDS S",
    cpu: 2,
    ram: 8,
    storage: 180,
    diskType: "NVMe", 
    bandwidth: 32000,
    price: { monthly: 19.99, hourly: 0.03 }
  }
};

// ========================================
// 🌍 REGION IDs (LOCATIONS)
// ========================================

const VPS_REGIONS = {
  "EU": {
    slug: "EU",
    name: "European Union",
    dataCenters: ["EU1", "EU2", "EU3", "EU4"]
  },
  "US-central": {
    slug: "US-central", 
    name: "United States (Central)",
    dataCenters: ["USC1"]
  },
  "US-east": {
    slug: "US-east",
    name: "United States (East)", 
    dataCenters: ["USE1", "USE2"]
  },
  "US-west": {
    slug: "US-west",
    name: "United States (West)",
    dataCenters: ["USW1"]
  },
  "SIN": {
    slug: "SIN",
    name: "Asia (Singapore)",
    dataCenters: ["SIN1", "SIN2", "SIN3"]
  },
  "JPN": {
    slug: "JPN", 
    name: "Asia (Japan)",
    dataCenters: ["JPN1"]
  },
  "IND": {
    slug: "IND",
    name: "Asia (India)",
    dataCenters: ["IND"]
  },
  "UK": {
    slug: "UK",
    name: "United Kingdom", 
    dataCenters: ["UK1"]
  },
  "AUS": {
    slug: "AUS",
    name: "Australia (Sydney)",
    dataCenters: ["AUS1"]
  }
};

// ========================================
// 🚀 EXEMPLES D'UTILISATION
// ========================================

const VPS_EXAMPLES = {
  // Exemple 1: Ubuntu 22.04 + VPS 10 NVMe + EU
  ubuntu_basic: {
    imageId: VPS_IMAGES.ubuntu["22.04"],
    planId: "V91",
    regionSlug: "EU",
    provider: "contabo"
  },

  // Exemple 2: Windows Server 2025 + VPS 20 NVMe + US-Central
  windows_server: {
    imageId: VPS_IMAGES.windows["2025-datacenter"], 
    planId: "V94",
    regionSlug: "US-central",
    provider: "contabo"
  },

  // Exemple 3: Ubuntu 22.04 avec cPanel + VPS 30 NVMe + Singapore
  ubuntu_cpanel: {
    imageId: VPS_APPLICATIONS.cpanel["ubuntu-22.04"],
    planId: "V97", 
    regionSlug: "SIN",
    provider: "contabo"
  },

  // Exemple 4: Ubuntu 22.04 avec Plesk + VPS 20 SSD + EU
  ubuntu_plesk: {
    imageId: VPS_APPLICATIONS.plesk["ubuntu-22.04"],
    planId: "V95",
    regionSlug: "EU", 
    provider: "contabo"
  }
};

// ========================================
// 🛠️ FONCTIONS UTILITAIRES
// ========================================

/**
 * Obtenir un imageId par nom d'OS et version
 */
function getImageId(os, version) {
  return VPS_IMAGES[os]?.[version] || null;
}

/**
 * Obtenir les détails d'un plan VPS
 */
function getPlanDetails(planId) {
  return VPS_PLANS[planId] || null;
}

/**
 * Obtenir les détails d'une région
 */
function getRegionDetails(regionSlug) {
  return VPS_REGIONS[regionSlug] || null;
}

/**
 * Valider une configuration VPS
 */
function validateVPSConfig(config) {
  const { imageId, planId, regionSlug, provider } = config;
  
  if (!imageId || !planId || !regionSlug || !provider) {
    return { valid: false, error: "Paramètres manquants" };
  }
  
  if (!VPS_PLANS[planId]) {
    return { valid: false, error: "Plan ID invalide" };
  }
  
  if (!VPS_REGIONS[regionSlug]) {
    return { valid: false, error: "Region slug invalide" };
  }
  
  return { valid: true };
}

// ========================================
// 📤 EXPORTS
// ========================================

module.exports = {
  VPS_IMAGES,
  VPS_APPLICATIONS, 
  VPS_PLANS,
  VPS_REGIONS,
  VPS_EXAMPLES,
  getImageId,
  getPlanDetails,
  getRegionDetails,
  validateVPSConfig
};

// ========================================
// 📋 UTILISATION RAPIDE
// ========================================

/*
// Importer le script
const vpsIds = require('./vps-ids-reference.js');

// Utiliser un exemple
const config = vpsIds.VPS_EXAMPLES.ubuntu_basic;

// Ou créer une config personnalisée
const customConfig = {
  imageId: vpsIds.getImageId('ubuntu', '22.04'),
  planId: 'V91',
  regionSlug: 'EU',
  provider: 'contabo'
};

// Valider la configuration
const validation = vpsIds.validateVPSConfig(customConfig);
console.log(validation);
*/
