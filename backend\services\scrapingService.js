/**
 * Main Scraping Orchestrator Service
 * Coordinates Gemini scraping → Database updates with cron job automation
 * Integrates with existing notification system for price change alerts
 */

const cron = require('node-cron');
const GeminiParsingService = require('./geminiParsingService');
const PricingUpdateService = require('./pricingUpdateService');
const Notification = require('../models/Notification');
const NotificationSetting = require('../models/NotificationSetting');
const VpsScheduledTask = require('../models/VpsScheduledTask');

class ScrapingService {
  constructor() {
    this.geminiService = new GeminiParsingService();
    this.pricingService = new PricingUpdateService();
    this.cronJob = null;
    this.isRunning = false;
    this.lastRunTime = null;
    this.lastRunResult = null;
    
    // Default cron schedule: Every day at 2 AM UTC
    this.defaultCronSchedule = '0 2 * * *';
  }

  /**
   * Start the automated scraping cron job
   * @param {string} cronSchedule - Cron schedule expression (optional)
   * @returns {Promise<boolean>} Success status
   */
  async startCronJob(cronSchedule = null) {
    try {
      const schedule = cronSchedule || this.defaultCronSchedule;
      
      // Validate cron expression
      if (!cron.validate(schedule)) {
        throw new Error(`Invalid cron expression: ${schedule}`);
      }

      // Stop existing job if running
      if (this.cronJob) {
        this.cronJob.stop();
        console.log('[SCRAPING] Stopped existing cron job');
      }

      // Start new cron job
      this.cronJob = cron.schedule(schedule, async () => {
        console.log('[SCRAPING] Automated scraping job started');
        await this.runScrapingPipeline(true); // true = automated run
      }, {
        scheduled: true,
        timezone: "UTC"
      });

      console.log(`[SCRAPING] Cron job started with schedule: ${schedule}`);
      return true;

    } catch (error) {
      console.error('[SCRAPING] Error starting cron job:', error.message);
      throw error;
    }
  }

  /**
   * Stop the automated scraping cron job
   * @returns {boolean} Success status
   */
  stopCronJob() {
    try {
      if (this.cronJob) {
        this.cronJob.stop();
        this.cronJob = null;
        console.log('[SCRAPING] Cron job stopped');
        return true;
      }
      console.log('[SCRAPING] No cron job running');
      return false;
    } catch (error) {
      console.error('[SCRAPING] Error stopping cron job:', error.message);
      return false;
    }
  }

  /**
   * Get cron job status
   * @returns {Object} Status information
   */
  getCronJobStatus() {
    return {
      isRunning: this.cronJob !== null,
      schedule: this.defaultCronSchedule,
      lastRunTime: this.lastRunTime,
      lastRunResult: this.lastRunResult,
      currentlyProcessing: this.isRunning
    };
  }

  /**
   * Run the complete scraping pipeline
   * @param {boolean} isAutomated - Whether this is an automated run
   * @returns {Promise<Object>} Pipeline result
   */
  async runScrapingPipeline(isAutomated = false) {
    if (this.isRunning) {
      const message = 'Scraping pipeline is already running';
      console.log(`[SCRAPING] ${message}`);
      return { success: false, message };
    }

    this.isRunning = true;
    this.lastRunTime = new Date();
    
    const runType = isAutomated ? 'AUTOMATED' : 'MANUAL';
    console.log(`[SCRAPING] Starting ${runType} scraping pipeline...`);

    try {
      // Step 1: Scrape Contabo pricing with Gemini (includes retry mechanism)
      console.log('[SCRAPING] Step 1: Scraping Contabo VPS pricing...');
      const scrapingResult = await this.geminiService.scrapeContaboPricing();

      if (!scrapingResult.success) {
        // Enhanced error message with retry information
        const errorMsg = scrapingResult.attempts
          ? `Failed to scrape pricing data from Contabo after ${scrapingResult.attempts} attempts: ${scrapingResult.error}`
          : `Failed to scrape pricing data from Contabo: ${scrapingResult.error}`;
        throw new Error(errorMsg);
      }

      // Parse JSON response from Gemini with robust error recovery
      let scrapedPackages;
      try {
        scrapedPackages = await this.parseGeminiResponse(scrapingResult.response);
      } catch (parseError) {
        console.error('[SCRAPING] JSON parsing failed, attempting recovery...', parseError.message);

        // Attempt to recover and continue with partial data
        const recoveryResult = await this.attemptJsonRecovery(scrapingResult.response, parseError);

        if (recoveryResult.success) {
          console.log(`[SCRAPING] Recovery successful: ${recoveryResult.packages.length} packages recovered`);
          scrapedPackages = recoveryResult.packages;

          // Send warning notification about recovery
          await this.sendRecoveryNotification(parseError, recoveryResult, isAutomated);
        } else {
          // Complete failure - send error notification
          throw new Error(`Failed to parse JSON response: ${parseError.message}`);
        }
      }

      console.log(`[SCRAPING] Successfully scraped ${scrapedPackages.length} packages`);

      // Step 2: Update database with scraped data
      console.log('[SCRAPING] Step 2: Updating database...');
      const updateResult = await this.pricingService.updateContaboPackages(scrapedPackages);

      // Step 3: Send notifications for significant changes
      console.log('[SCRAPING] Step 3: Processing notifications...');
      await this.processNotifications(updateResult, isAutomated);

      // Prepare final result
      const result = {
        success: true,
        timestamp: this.lastRunTime,
        runType,
        scraped: scrapedPackages.length,
        created: updateResult.created,
        updated: updateResult.updated,
        errors: updateResult.errors.length,
        packages: updateResult.packages,
        notifications: {
          sent: updateResult.created + updateResult.updated > 0 ? 1 : 0
        }
      };

      this.lastRunResult = result;
      console.log(`[SCRAPING] ${runType} pipeline completed successfully`);
      
      return result;

    } catch (error) {
      const errorResult = {
        success: false,
        timestamp: this.lastRunTime,
        runType,
        error: error.message,
        notifications: { sent: 1 } // Error notification
      };

      this.lastRunResult = errorResult;
      console.error(`[SCRAPING] ${runType} pipeline failed:`, error.message);

      // Send error notification to admins
      await this.sendErrorNotification(error, isAutomated);

      return errorResult;

    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Process and send notifications for pricing changes
   * @param {Object} updateResult - Result from pricing update service
   * @param {boolean} isAutomated - Whether this is an automated run
   */
  async processNotifications(updateResult, isAutomated) {
    try {
      const { created, updated, packages } = updateResult;

      // Check for actual price changes (not just package updates)
      const priceChanges = packages
        .filter(pkg => pkg.action === 'updated' && pkg.priceChange.difference !== 0);

      // Only send notifications if there are significant changes
      if (created === 0 && updated === 0) {
        console.log('[SCRAPING] No changes detected, skipping notifications');
        return;
      }

      // If packages were "updated" but no actual price changes, skip notification
      if (created === 0 && priceChanges.length === 0) {
        console.log('[SCRAPING] Packages updated but no price changes detected, skipping notifications');
        return;
      }

      // Prepare notification message
      let title = '🔄 VPS Pricing Update';
      let message = `Contabo VPS pricing has been updated:\n`;

      if (created > 0) {
        message += `• ${created} new packages added\n`;
      }

      if (updated > 0) {
        message += `• ${updated} packages updated\n`;
      }

      // Add price change details for updated packages
      const displayChanges = priceChanges.slice(0, 3); // Limit to first 3 changes

      if (displayChanges.length > 0) {
        message += `\nPrice changes:\n`;
        displayChanges.forEach(pkg => {
          const change = pkg.priceChange;
          const symbol = change.difference > 0 ? '↗️' : '↘️';
          message += `• ${pkg.package.name}: $${change.old} → $${change.new} ${symbol}\n`;
        });

        // Show total count if there are more changes
        if (priceChanges.length > 3) {
          message += `• ... and ${priceChanges.length - 3} more price changes\n`;
        }
      }

      message += `\nRun type: ${isAutomated ? 'Automated' : 'Manual'}`;

      // Send notification to all admins
      await this.sendAdminNotification({
        type: 'general',
        title,
        message,
        link: '/admin/packages?brand=Contabo'
      });

      console.log(`[SCRAPING] Admin notification sent for pricing changes (${priceChanges.length} price changes, ${created} new packages)`);

    } catch (error) {
      console.error('[SCRAPING] Error processing notifications:', error.message);
    }
  }

  /**
   * Parse Gemini response with multiple strategies
   * @param {string} response - Raw response from Gemini
   * @returns {Promise<Array>} Array of parsed packages
   */
  async parseGeminiResponse(response) {
    // Validate response (basic validation - detailed validation done in Gemini service)
    if (!response || response.trim().length === 0) {
      throw new Error('Empty response from Gemini API');
    }

    let jsonString = null;

    // Strategy 1: Extract from markdown code block
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      jsonString = jsonMatch[1];
    } else {
      // Strategy 2: Look for JSON object pattern
      const objectMatch = response.match(/\{[\s\S]*\}/);
      if (objectMatch) {
        jsonString = objectMatch[0];
      } else {
        throw new Error('No JSON structure found in response');
      }
    }

    // Validate JSON string before parsing
    if (!jsonString || jsonString.trim().length === 0) {
      throw new Error('No valid JSON found in response');
    }

    // Parse JSON
    const parsedData = JSON.parse(jsonString);

    // Validate structure
    if (!parsedData.packages || !Array.isArray(parsedData.packages)) {
      throw new Error('Invalid JSON structure: missing packages array');
    }

    if (parsedData.packages.length === 0) {
      throw new Error('No packages found in response');
    }

    return parsedData.packages;
  }

  /**
   * Attempt to recover from JSON parsing errors
   * @param {string} response - Raw response from Gemini
   * @param {Error} originalError - The original parsing error
   * @returns {Promise<Object>} Recovery result
   */
  async attemptJsonRecovery(response, originalError) {
    console.log('[SCRAPING] Attempting JSON recovery...');

    try {
      // Strategy 1: Fix common JSON syntax errors
      let fixedJson = response;

      // Extract JSON block if present
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        fixedJson = jsonMatch[1];
      }

      // Common fixes
      fixedJson = fixedJson
        .replace(/,\s*}/g, '}')           // Remove trailing commas
        .replace(/,\s*]/g, ']')           // Remove trailing commas in arrays
        .replace(/:\.\s*"/g, ': "')       // Fix ":. " pattern to ": "
        .replace(/"\s*\.\s*"/g, '" "')    // Fix ". " in strings
        .replace(/([^\\])"/g, '$1"')      // Fix unescaped quotes
        .replace(/\n\s*\n/g, '\n')        // Remove extra newlines
        .trim();

      console.log('[SCRAPING] Attempting to parse fixed JSON...');
      const parsedData = JSON.parse(fixedJson);

      if (parsedData.packages && Array.isArray(parsedData.packages) && parsedData.packages.length > 0) {
        return {
          success: true,
          packages: parsedData.packages,
          fixesApplied: ['syntax_cleanup', 'trailing_comma_removal', 'quote_fixes']
        };
      }

    } catch (fixError) {
      console.log('[SCRAPING] Syntax fix failed, trying manual extraction...');
    }

    // Strategy 2: Manual package extraction using regex
    try {
      const packages = [];
      const packagePattern = /"name":\s*"([^"]+)"[\s\S]*?"monthly":\s*([0-9.]+)[\s\S]*?"vCPU":\s*"([^"]*)"[\s\S]*?"ram":\s*"([^"]*)"[\s\S]*?"storage":\s*"([^"]*)"[\s\S]*?"snapshots":\s*"([^"]*)"[\s\S]*?"traffic":\s*"([^"]*)"/g;

      let match;
      while ((match = packagePattern.exec(response)) !== null) {
        packages.push({
          name: match[1],
          pricing: { monthly: parseFloat(match[2]) },
          specifications: {
            vCPU: match[3],
            ram: match[4],
            storage: match[5],
            snapshots: match[6],
            traffic: match[7]
          }
        });
      }

      if (packages.length > 0) {
        console.log(`[SCRAPING] Manual extraction recovered ${packages.length} packages`);
        return {
          success: true,
          packages: packages,
          fixesApplied: ['manual_regex_extraction']
        };
      }

    } catch (extractError) {
      console.log('[SCRAPING] Manual extraction failed:', extractError.message);
    }

    // Strategy 3: Try to salvage partial data
    try {
      // Look for individual package objects
      const partialPackages = [];
      const singlePackagePattern = /\{[^{}]*"name"[^{}]*"monthly"[^{}]*\}/g;

      let match;
      while ((match = singlePackagePattern.exec(response)) !== null) {
        try {
          const packageData = JSON.parse(match[0]);
          if (packageData.name && packageData.pricing && packageData.pricing.monthly) {
            partialPackages.push(packageData);
          }
        } catch (e) {
          // Skip invalid packages
        }
      }

      if (partialPackages.length > 0) {
        console.log(`[SCRAPING] Partial recovery found ${partialPackages.length} packages`);
        return {
          success: true,
          packages: partialPackages,
          fixesApplied: ['partial_package_extraction']
        };
      }

    } catch (partialError) {
      console.log('[SCRAPING] Partial extraction failed:', partialError.message);
    }

    return {
      success: false,
      error: 'All recovery strategies failed',
      originalError: originalError.message
    };
  }

  /**
   * Send recovery notification to admins
   * @param {Error} originalError - The original parsing error
   * @param {Object} recoveryResult - The recovery result
   * @param {boolean} isAutomated - Whether this was an automated run
   */
  async sendRecoveryNotification(originalError, recoveryResult, isAutomated) {
    try {
      const title = '⚠️ VPS Scraping Recovery';
      const message = `VPS pricing scraping encountered a JSON parsing error but successfully recovered:\n\n` +
        `Original Error: ${originalError.message}\n` +
        `Recovery Method: ${recoveryResult.fixesApplied.join(', ')}\n` +
        `Packages Recovered: ${recoveryResult.packages.length}\n\n` +
        `The scraping will continue with the recovered data.\n\n` +
        `Run type: ${isAutomated ? 'Automated' : 'Manual'}\n` +
        `Time: ${new Date().toISOString()}`;

      await this.sendAdminNotification({
        type: 'general',
        title,
        message,
        link: '/admin/packages'
      });

      console.log('[SCRAPING] Recovery notification sent to admins');

    } catch (notificationError) {
      console.error('[SCRAPING] Failed to send recovery notification:', notificationError.message);
    }
  }

  /**
   * Send error notification to admins
   * @param {Error} error - The error that occurred
   * @param {boolean} isAutomated - Whether this was an automated run
   */
  async sendErrorNotification(error, isAutomated) {
    try {
      const title = '❌ VPS Scraping Error';
      const message = `VPS pricing scraping failed:\n\nError: ${error.message}\n\nRun type: ${isAutomated ? 'Automated' : 'Manual'}\nTime: ${new Date().toISOString()}`;

      await this.sendAdminNotification({
        type: 'general',
        title,
        message,
        link: '/admin/packages'
      });

      console.log('[SCRAPING] Error notification sent to admins');

    } catch (notificationError) {
      console.error('[SCRAPING] Failed to send error notification:', notificationError.message);
    }
  }

  /**
   * Send notification to all admins
   * @param {Object} notificationData - Notification data
   */
  async sendAdminNotification(notificationData) {
    try {
      const notification = new Notification({
        userType: 'admin',
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        link: notificationData.link,
        isRead: false
      });

      await notification.save();

      console.log('[SCRAPING] Notification saved to database:', {
        id: notification._id,
        title: notification.title,
        type: notification.type
      });

      // Send real-time notification via socket if available
      if (global.io) {
        // Get count of clients in the admin room
        const roomSize = global.io.sockets.adapter.rooms.get('admin-room')?.size || 0;

        console.log(`[SCRAPING] Sending real-time notification to admin room (${roomSize} clients)`);
        console.log(`[SCRAPING] Notification details:`, {
          id: notification._id,
          type: notification.type,
          title: notification.title,
          userType: notification.userType
        });

        // Use the correct event name and room targeting like the socket service
        global.io.to('admin-room').emit('notification', notification);

        console.log('[SCRAPING] Real-time notification sent to admin room');
      } else {
        console.log('[SCRAPING] No socket.io instance available for real-time notifications');
      }

      // Send email notifications to all admin users if enabled
      await this.sendEmailNotificationsToAdmins(notificationData);

    } catch (error) {
      console.error('[SCRAPING] Error sending admin notification:', error.message);
    }
  }

  /**
   * Send email notifications to all admin users
   * @param {Object} notificationData - Notification data
   */
  async sendEmailNotificationsToAdmins(notificationData) {
    try {
      // Check if email notifications are enabled for VPS scraping
      const settings = await this.getScrapingSettings();
      if (!settings.emailEnabled) {
        console.log('[SCRAPING] Email notifications disabled, skipping email sending');
        return;
      }

      // Get all admin users
      const User = require('../models/User');
      const adminUsers = await User.find({
        role: 'ADMIN',
        email: { $exists: true, $ne: '' }
      }).select('firstName lastName email');

      if (adminUsers.length === 0) {
        console.log('[SCRAPING] No admin users found with valid email addresses');
        return;
      }

      console.log(`[SCRAPING] Found ${adminUsers.length} admin users for email notifications`);

      // Import email service
      const { sendSimpleEmail } = require('../routes/sendEmail/sendEmail');
      const { getVPSPriceChangeEmailTemplate } = require('../routes/sendEmail/emailTemplates');

      let emailsSent = 0;
      let emailsFailed = 0;

      // Send email to each admin user
      for (const admin of adminUsers) {
        try {
          const emailTemplate = getVPSPriceChangeEmailTemplate({
            adminName: admin.firstName || 'Admin',
            title: notificationData.title,
            message: notificationData.message,
            link: notificationData.link
          });

          await sendSimpleEmail({
            to: admin.email,
            subject: notificationData.title,
            html: emailTemplate
          });

          emailsSent++;
          console.log(`[SCRAPING] Email sent successfully to ${admin.email}`);
        } catch (emailError) {
          emailsFailed++;
          console.error(`[SCRAPING] Failed to send email to ${admin.email}:`, emailError.message);
        }
      }

      console.log(`[SCRAPING] Email notification summary: ${emailsSent} sent, ${emailsFailed} failed`);

    } catch (error) {
      console.error('[SCRAPING] Error sending email notifications to admins:', error.message);
    }
  }

  /**
   * Initialize the scraping service with database-driven scheduling
   * @param {boolean} startCron - Whether to start the cron job immediately
   * @returns {Promise<boolean>} Success status
   */
  async initialize(startCron = true) {
    try {
      console.log('[SCRAPING] Initializing VPS scraping service...');

      if (startCron) {
        // Start the database-driven scheduler (like custom notifications)
        await this.startDatabaseScheduler();
        console.log('[SCRAPING] Database-driven scheduler started');

        // Sync current settings to create scheduled tasks
        await this.syncScheduleFromSettings();
      }

      console.log('[SCRAPING] Service initialized successfully');
      return true;

    } catch (error) {
      console.error('[SCRAPING] Error initializing service:', error.message);
      throw error;
    }
  }

  /**
   * Start the database-driven scheduler (runs every minute like custom notifications)
   * @returns {Promise<boolean>} Success status
   */
  async startDatabaseScheduler() {
    try {
      // Stop existing job if running
      if (this.cronJob) {
        this.cronJob.stop();
        console.log('[SCRAPING] Stopped existing cron job');
      }

      // Start new cron job - runs every minute to check for due tasks
      this.cronJob = cron.schedule('* * * * *', async () => {
        console.log('[SCRAPING] Checking for due VPS scraping tasks...');
        await this.processDueTasks();
      }, {
        scheduled: true,
        timezone: "UTC"
      });

      console.log('[SCRAPING] Database scheduler started - checking every minute');
      return true;

    } catch (error) {
      console.error('[SCRAPING] Error starting database scheduler:', error.message);
      throw error;
    }
  }

  /**
   * Process due VPS scraping tasks from database
   */
  async processDueTasks() {
    try {
      const dueTasks = await VpsScheduledTask.findDueTasks();

      if (dueTasks.length === 0) {
        return; // No due tasks
      }

      console.log(`[SCRAPING] Found ${dueTasks.length} due VPS scraping tasks`);

      for (const task of dueTasks) {
        try {
          // Mark task as running
          await task.markAsRunning();
          console.log(`[SCRAPING] Executing scheduled task ${task._id}`);

          // Execute the scraping pipeline
          const result = await this.runScrapingPipeline(task.isAutomated);

          // Mark task as completed with results
          await task.markAsCompleted(result);
          console.log(`[SCRAPING] Task ${task._id} completed successfully`);

        } catch (error) {
          console.error(`[SCRAPING] Error executing task ${task._id}:`, error);
          await task.markAsFailed(error);
        }
      }

    } catch (error) {
      console.error('[SCRAPING] Error processing due tasks:', error.message);
    }
  }

  /**
   * Sync current settings to create appropriate scheduled tasks
   */
  async syncScheduleFromSettings() {
    try {
      const settings = await this.getScrapingSettings();

      if (!settings.enabled) {
        console.log('[SCRAPING] VPS scraping is disabled, cancelling all scheduled tasks');
        await VpsScheduledTask.cancelAllScheduledTasks();
        return;
      }

      // Parse the current cron schedule to determine recurrence
      const cronParts = settings.cronSchedule.split(' ');

      if (cronParts.length === 5) {
        const [minutes, hours, dayOfMonth, month, dayOfWeek] = cronParts;

        // Determine schedule type and create appropriate task
        if (dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
          // Daily schedule
          const time = `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
          await this.createRecurringTask('daily', time);
          console.log(`[SCRAPING] Created daily recurring task at ${time}`);

        } else if (dayOfMonth === '*' && month === '*' && dayOfWeek !== '*') {
          // Weekly schedule
          const time = `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
          await this.createRecurringTask('weekly', time);
          console.log(`[SCRAPING] Created weekly recurring task at ${time}`);

        } else if (dayOfMonth !== '*' && month !== '*') {
          // One-time custom schedule
          const currentYear = new Date().getFullYear();
          const scheduledDate = new Date(currentYear, parseInt(month) - 1, parseInt(dayOfMonth), parseInt(hours), parseInt(minutes));

          if (scheduledDate > new Date()) {
            await this.createOneTimeTask(scheduledDate);
            console.log(`[SCRAPING] Created one-time task for ${scheduledDate.toISOString()}`);
          } else {
            console.log(`[SCRAPING] Skipped past one-time schedule: ${scheduledDate.toISOString()}`);
          }
        }
      }

    } catch (error) {
      console.error('[SCRAPING] Error syncing schedule from settings:', error.message);
    }
  }

  /**
   * Get VPS scraping settings from database
   * @returns {Promise<Object>} Scraping settings
   */
  async getScrapingSettings() {
    try {
      let settings = await NotificationSetting.findOne({ type: 'vps_scraping' });

      if (!settings) {
        // Create default settings
        settings = await NotificationSetting.create({
          type: 'vps_scraping',
          enabled: true,
          timeThreshold: 0,
          cronSchedule: '0 2 * * *', // Daily at 2 AM UTC
          emailEnabled: true, // Send emails to all admin users
          inAppEnabled: true,
          notificationTitle: 'VPS Pricing Update',
          notificationMessage: 'Contabo VPS pricing has been updated automatically.'
        });
        console.log('[SCRAPING] Created default VPS scraping settings');
      }

      return settings;
    } catch (error) {
      console.error('[SCRAPING] Error getting settings:', error.message);
      // Return default settings if database error
      return {
        enabled: true,
        cronSchedule: '0 2 * * *',
        emailEnabled: true, // Send emails to all admin users
        inAppEnabled: true,
        notificationTitle: 'VPS Pricing Update',
        notificationMessage: 'Contabo VPS pricing has been updated automatically.'
      };
    }
  }

  /**
   * Create a one-time scheduled task
   * @param {Date} scheduledDateTime - When to execute the task
   * @param {string} createdBy - Who created the task
   * @returns {Promise<Object>} Created task
   */
  async createOneTimeTask(scheduledDateTime, createdBy = 'admin') {
    try {
      // Cancel any existing scheduled tasks first
      await VpsScheduledTask.cancelAllScheduledTasks();

      const task = await VpsScheduledTask.createOneTimeTask(scheduledDateTime, createdBy);
      console.log(`[SCRAPING] Created one-time task: ${task._id} for ${scheduledDateTime.toISOString()}`);
      return task;
    } catch (error) {
      console.error('[SCRAPING] Error creating one-time task:', error.message);
      throw error;
    }
  }

  /**
   * Create a recurring scheduled task
   * @param {string} recurrenceType - 'daily' or 'weekly'
   * @param {string} recurrenceTime - Time in HH:MM format
   * @param {string} createdBy - Who created the task
   * @returns {Promise<Object>} Created task
   */
  async createRecurringTask(recurrenceType, recurrenceTime, createdBy = 'system') {
    try {
      // Cancel any existing scheduled tasks first
      await VpsScheduledTask.cancelAllScheduledTasks();

      const task = await VpsScheduledTask.createRecurringTask(recurrenceType, recurrenceTime, createdBy);
      console.log(`[SCRAPING] Created ${recurrenceType} recurring task: ${task._id} at ${recurrenceTime}`);
      return task;
    } catch (error) {
      console.error('[SCRAPING] Error creating recurring task:', error.message);
      throw error;
    }
  }

  /**
   * Update schedule based on user-friendly settings
   * @param {Object} scheduleConfig - Frontend schedule configuration
   * @returns {Promise<boolean>} Success status
   */
  async updateScheduleFromConfig(scheduleConfig) {
    try {
      console.log('[SCRAPING] Updating schedule from config:', scheduleConfig);

      // Cancel all existing scheduled tasks
      await VpsScheduledTask.cancelAllScheduledTasks();

      if (scheduleConfig.frequency === 'daily') {
        await this.createRecurringTask('daily', scheduleConfig.scheduledTime);
      } else if (scheduleConfig.frequency === 'weekly') {
        await this.createRecurringTask('weekly', scheduleConfig.scheduledTime);
      } else if (scheduleConfig.frequency === 'custom' && scheduleConfig.scheduledDate && scheduleConfig.scheduledTime) {
        const scheduledDateTime = new Date(`${scheduleConfig.scheduledDate}T${scheduleConfig.scheduledTime}`);

        if (scheduledDateTime > new Date()) {
          await this.createOneTimeTask(scheduledDateTime, 'admin');
        } else {
          throw new Error('Scheduled time must be in the future');
        }
      }

      console.log('[SCRAPING] Schedule updated successfully');
      return true;

    } catch (error) {
      console.error('[SCRAPING] Error updating schedule:', error.message);
      throw error;
    }
  }

  /**
   * Get scheduled task statistics
   * @returns {Promise<Object>} Task statistics
   */
  async getScheduledTaskStats() {
    try {
      const stats = await VpsScheduledTask.getTaskStats();
      const nextTask = await VpsScheduledTask.findOne({
        status: 'scheduled'
      }).sort({ scheduledDateTime: 1 });

      return {
        stats: stats,
        nextExecution: nextTask ? nextTask.scheduledDateTime : null,
        nextTaskId: nextTask ? nextTask._id : null
      };
    } catch (error) {
      console.error('[SCRAPING] Error getting task stats:', error.message);
      return { stats: [], nextExecution: null, nextTaskId: null };
    }
  }
}

module.exports = ScrapingService;
