const mongoose = require('mongoose');

// Modèle séparé pour les prix VPS - SOLUTION DÉFINITIVE
const vpsPricingSchema = new mongoose.Schema({
  regionId: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: true
  },
  packageName: {
    type: String,
    required: true
  },
  additionalPrice: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  collection: 'vps_pricing'
});

// Index unique pour éviter les doublons
vpsPricingSchema.index({ regionId: 1, packageId: 1 }, { unique: true });

module.exports = mongoose.model('VPSPricing', vpsPricingSchema);
