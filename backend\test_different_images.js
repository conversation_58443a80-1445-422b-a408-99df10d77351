const ContaboProvider = require('./services/providers/ContaboProvider');

async function testDifferentImages() {
  console.log('🧪 Testing Different Images for Password Authentication...\n');

  try {
    const contaboProvider = new ContaboProvider();

    // Get available images
    console.log('📀 Getting available images...');
    const images = await contaboProvider.getImages();
    
    console.log('\n🔍 Available images that might support password auth:');
    
    // Filter images that are more likely to support password authentication
    const passwordFriendlyImages = images.filter(img => {
      const name = img.name.toLowerCase();
      return (
        name.includes('centos') ||
        name.includes('debian') ||
        name.includes('rocky') ||
        name.includes('almalinux') ||
        (name.includes('ubuntu') && !name.includes('plesk') && !name.includes('cpanel')) ||
        name.includes('fedora')
      );
    });

    passwordFriendlyImages.forEach((img, index) => {
      console.log(`${index + 1}. ${img.name} (ID: ${img.imageId})`);
    });

    // Test with CentOS or Debian if available
    const testImage = passwordFriendlyImages.find(img => 
      img.name.toLowerCase().includes('centos') || 
      img.name.toLowerCase().includes('debian')
    ) || passwordFriendlyImages[0];

    if (!testImage) {
      console.log('\n❌ No suitable images found for password authentication test');
      return;
    }

    console.log(`\n✅ Selected for test: ${testImage.name} (ID: ${testImage.imageId})`);

    // Test installation with this image
    const instanceId = 202718127;
    const testPassword = 'PasswordTest123!';
    
    const reinstallData = {
      imageId: testImage.imageId,
      password: testPassword,
      enableRootUser: true,
      userData: `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${testPassword}
chpasswd:
  expire: false
users:
  - name: root
    lock_passwd: false
    passwd: ${testPassword}
  - name: admin
    groups: sudo
    shell: /bin/bash
    lock_passwd: false
    passwd: ${testPassword}

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd
  - echo "Password authentication enabled" >> /root/password_auth.log`
    };

    console.log('\n📤 Reinstall data prepared:');
    console.log('- Image:', testImage.name);
    console.log('- Password:', testPassword);
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: SSH password auth enabled');

    console.log('\n🔄 Starting reinstallation with password-friendly image...');
    const result = await contaboProvider.reinstallVPS(instanceId, reinstallData);

    console.log('\n✅ Reinstallation completed successfully!');
    console.log('Result:', result.message);

    console.log('\n🔍 Testing instructions:');
    console.log('1. Wait 5-10 minutes for reinstallation to complete');
    console.log('2. Test SSH connection:');
    console.log(`   ssh root@185.217.125.69 (password: ${testPassword})`);
    console.log(`   ssh admin@185.217.125.69 (password: ${testPassword})`);
    console.log('3. Cloud-Init should have enabled password authentication');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testDifferentImages();
