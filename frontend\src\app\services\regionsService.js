import apiService from '../lib/apiService';

const regionsService = {
  // === NOUVEAU SYSTÈME VPS REGIONS ===

  // NOUVEAU - Sauvegarder prix VPS (système séparé)
  saveVPSPrice: (data) =>
    apiService.post('/api/admin/vps-regions/pricing', data),

  // NOUVEAU - Obtenir tous les prix VPS
  getAllVPSPrices: () =>
    apiService.get('/api/admin/vps-regions/pricing'),

  // NOUVEAU - Obtenir prix pour une région/package spécifique
  getVPSPrice: (regionId, packageId) =>
    apiService.get(`/api/admin/vps-regions/pricing/${regionId}/${packageId}`),

  // ANCIEN SYSTÈME (pour compatibilité)
  createVPSRegion: (data) =>
    apiService.post('/api/admin/vps-regions', data),

  // Obtenir toutes les régions VPS (admin)
  getAllVPSRegions: () =>
    apiService.get('/api/admin/vps-regions'),

  // Supprimer une région VPS
  deleteVPSRegion: (id) =>
    apiService.delete(`/api/admin/vps-regions/${id}`),

  // === ENDPOINTS PUBLICS ===

  // Obtenir toutes les régions actives (public)
  getActiveRegions: () =>
    apiService.get('/api/regions'),

  // Obtenir les régions pour un package spécifique (public)
  getRegionsForPackage: (packageId) =>
    apiService.get(`/api/regions/package/${packageId}`)
};

export default regionsService;
