/**
 * Test script for Contabo Provider
 * This script tests the Contabo provider methods directly
 */

const ContaboProvider = require('./services/providers/ContaboProvider');
require('dotenv').config();

async function testContaboProvider() {
  console.log('🚀 Testing Contabo Provider\n');
  
  try {
    // Initialize provider
    const provider = new ContaboProvider();
    
    // Test 1: Authentication
    console.log('🔐 Testing authentication...');
    await provider.authenticate();
    console.log('✅ Authentication successful!\n');
    
    // Test 2: Get available images
    console.log('📋 Testing getAvailableImages...');
    const imagesResult = await provider.getAvailableImages();
    console.log('✅ Images retrieved:', imagesResult.data?.length || 0);
    if (imagesResult.data && imagesResult.data.length > 0) {
      console.log('📄 Sample image:', imagesResult.data[0]);
    }
    console.log('');
    
    // Test 3: Get available applications
    console.log('📱 Testing getAvailableApplications...');
    const appsResult = await provider.getAvailableApplications();
    console.log('✅ Applications retrieved:', appsResult.data?.length || 0);
    if (appsResult.data && appsResult.data.length > 0) {
      console.log('📄 Sample application:', appsResult.data[0]);
    }
    console.log('');
    
    // Test 4: Get VPS instances (to get a real instance ID for testing)
    console.log('🖥️  Testing getCustomerVPS...');
    const vpsInstances = await provider.getCustomerVPS('test-user');
    console.log('✅ VPS instances retrieved:', vpsInstances.length);
    
    if (vpsInstances.length > 0) {
      console.log('📄 Sample VPS:', vpsInstances[0]);
      
      // Test 5: Test reinstall with a real instance (commented out for safety)
      const testInstanceId = vpsInstances[0].id;
      console.log(`\n⚠️  Found instance ${testInstanceId} - reinstall test is commented out for safety`);
      
      // Uncomment the following lines to test actual reinstall (BE CAREFUL!)
      /*
      const reinstallData = {
        imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb', // Ubuntu 22.04 LTS
        password: 'TestPassword123!'
      };
      
      console.log('🔄 Testing reinstallVPS...');
      console.log('📤 Reinstall data:', reinstallData);
      
      const reinstallResult = await provider.reinstallVPS(testInstanceId, reinstallData);
      console.log('✅ Reinstall result:', reinstallResult);
      */
    } else {
      console.log('⚠️  No VPS instances found for testing reinstall');
    }
    
    console.log('\n🎉 All Contabo Provider tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Contabo Provider test failed:');
    console.error('Error message:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Test individual methods
async function testIndividualMethods() {
  console.log('🧪 Testing individual methods\n');
  
  const provider = new ContaboProvider();
  
  try {
    await provider.authenticate();
    
    // Test makeRequest method with different endpoints
    console.log('🔍 Testing makeRequest with /compute/images...');
    const imagesResponse = await provider.makeRequest('GET', '/compute/images');
    console.log('✅ Direct images request successful, count:', imagesResponse.data?.length || 0);
    
    console.log('\n🔍 Testing makeRequest with /compute/applications...');
    const appsResponse = await provider.makeRequest('GET', '/compute/applications');
    console.log('✅ Direct applications request successful, count:', appsResponse.data?.length || 0);
    
    console.log('\n🔍 Testing makeRequest with /compute/instances...');
    const instancesResponse = await provider.makeRequest('GET', '/compute/instances');
    console.log('✅ Direct instances request successful, count:', instancesResponse.data?.length || 0);
    
  } catch (error) {
    console.error('❌ Individual method test failed:', error.message);
    throw error;
  }
}

// Run tests
async function runAllTests() {
  try {
    await testContaboProvider();
    console.log('\n' + '='.repeat(50));
    await testIndividualMethods();
  } catch (error) {
    console.error('Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testContaboProvider,
  testIndividualMethods
};
