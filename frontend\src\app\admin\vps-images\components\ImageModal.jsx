'use client'

import React, { useState } from 'react';
import {
  X,
  Plus,
  Trash2,
  Save,
  Upload,
  Monitor,
  Package,
  Settings,
  Database,
  DollarSign,
  Tag,
  Calendar,
  Hash,
  Type,
  FileText,
  Layers,
  Shield,
  Zap
} from 'lucide-react';
import {
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Button,
  Input,
  Textarea,
  Select,
  Option,
  Switch,
  Typography,
  Card,
  Chip,
  Badge
} from "@material-tailwind/react";
import { motion, AnimatePresence } from 'framer-motion';

export default function ImageModal({
  isOpen,
  onClose,
  onSave,
  formData,
  setFormData,
  activeTab,
  loading,
  selectedImage,
  availableOsImages = []
}) {
  const [activeFormTab, setActiveFormTab] = useState('basic');
  const [iconFile, setIconFile] = useState(null);
  const [iconPreview, setIconPreview] = useState(null);

  // Données de référence
  const osTypes = ['Linux', 'Windows'];
  const categories = {
    os: ['os'],
    apps: ['apps', 'blockchain']
  };
  const appTypes = ['control_panel', 'lamp', 'blockchain'];
  const iconTypes = {
    os: ['ubuntu', 'windows', 'debian', 'centos', 'almalinux', 'rockylinux', 'fedora', 'opensuse', 'freebsd', 'archlinux', 'custom'],
    apps: ['cpanel', 'plesk', 'blockchain', 'custom']
  };

  // Gérer les changements de formulaire
  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Si on change isPopular et que la catégorie était 'popular',
      // la remettre à la catégorie normale
      if (field === 'isPopular' && prev.category === 'popular') {
        newData.category = activeTab === 'os' ? 'os' : 'apps';
      }

      return newData;
    });
  };

  // Ajouter une variante (pour les apps)
  const addVariant = () => {
    const newVariant = {
      variantId: `variant-${Date.now()}`,
      name: '',
      price: 0,
      description: ''
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  // Supprimer une variante
  const removeVariant = (index) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index)
    }));
  };

  // Mettre à jour une variante
  const updateVariant = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map((variant, i) => 
        i === index ? { ...variant, [field]: value } : variant
      )
    }));
  };

  // Gérer la sélection des OS supportés
  const toggleSupportedOs = (osId) => {
    setFormData(prev => ({
      ...prev,
      supportedOs: prev.supportedOs.includes(osId)
        ? prev.supportedOs.filter(id => id !== osId)
        : [...prev.supportedOs, osId]
    }));
  };

  // Gérer l'upload d'icône
  const handleIconUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validation du fichier
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp'];
    const maxSize = 2 * 1024 * 1024; // 2MB

    if (!allowedTypes.includes(file.type)) {
      alert('Type de fichier non supporté. Utilisez JPG, PNG, SVG ou WebP.');
      return;
    }

    if (file.size > maxSize) {
      alert('Le fichier est trop volumineux. Taille maximale : 2MB.');
      return;
    }

    setIconFile(file);

    // Créer une prévisualisation
    const reader = new FileReader();
    reader.onload = (e) => {
      setIconPreview(e.target.result);
    };
    reader.readAsDataURL(file);

    // Mettre à jour le type d'icône
    handleInputChange('iconType', 'custom');
  };

  // Supprimer l'icône uploadée
  const removeIconUpload = () => {
    setIconFile(null);
    setIconPreview(null);
    // Reset le input file
    const fileInput = document.getElementById('icon-upload');
    if (fileInput) fileInput.value = '';
  };

  // Obtenir l'icône pour le type
  const getIconPreview = (iconType) => {
    // Si on a une prévisualisation d'upload, l'utiliser
    if (iconPreview && formData.iconType === 'custom') {
      return <img src={iconPreview} alt="Preview" className="h-6 w-6 object-cover rounded" />;
    }

    // Si l'image existe déjà et a une icône custom
    if (selectedImage && selectedImage.iconUrl && formData.iconType === 'custom') {
      return <img src={selectedImage.iconUrl} alt="Current icon" className="h-6 w-6 object-cover rounded" />;
    }

    // Icônes par défaut
    const iconMap = {
      ubuntu: <Monitor className="h-6 w-6 text-orange-500" />,
      windows: <Monitor className="h-6 w-6 text-blue-500" />,
      debian: <Monitor className="h-6 w-6 text-red-500" />,
      cpanel: <Settings className="h-6 w-6 text-blue-600" />,
      plesk: <Settings className="h-6 w-6 text-purple-600" />,
      blockchain: <Database className="h-6 w-6 text-yellow-600" />
    };
    return iconMap[iconType] || <Package className="h-6 w-6 text-gray-500" />;
  };

  return (
    <Dialog
      open={isOpen}
      handler={onClose}
      size="xl"
      className="max-h-[90vh] overflow-y-auto"
    >
      <DialogHeader className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {activeTab === 'os' ? (
            <Monitor className="h-6 w-6 text-blue-600" />
          ) : (
            <Package className="h-6 w-6 text-purple-600" />
          )}
          <Typography variant="h5">
            {selectedImage ? 'Modifier' : 'Ajouter'} une {activeTab === 'os' ? 'Image OS' : 'Application'}
          </Typography>
        </div>
        <Button variant="text" color="gray" onClick={onClose} className="p-2">
          <X className="h-5 w-5" />
        </Button>
      </DialogHeader>

      <DialogBody className="space-y-6">
        {/* Onglets du formulaire */}
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveFormTab('basic')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all flex-1 ${
              activeFormTab === 'basic'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FileText className="h-4 w-4 inline mr-2" />
            Informations de base
          </button>
          <button
            onClick={() => setActiveFormTab('advanced')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all flex-1 ${
              activeFormTab === 'advanced'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Settings className="h-4 w-4 inline mr-2" />
            Configuration avancée
          </button>
          {activeTab === 'apps' && (
            <button
              onClick={() => setActiveFormTab('variants')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all flex-1 ${
                activeFormTab === 'variants'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Layers className="h-4 w-4 inline mr-2" />
              Variantes & Prix
            </button>
          )}
        </div>

        <AnimatePresence mode="wait">
          {/* Onglet Informations de base */}
          {activeFormTab === 'basic' && (
            <motion.div
              key="basic"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Nom */}
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Nom *
                  </Typography>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Ex: Ubuntu, cPanel, Plesk..."
                    required
                  />
                </div>

                {/* Version */}
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Version *
                  </Typography>
                  <Input
                    value={formData.version}
                    onChange={(e) => handleInputChange('version', e.target.value)}
                    placeholder="Ex: 22.04, 2025-de, v1.0..."
                    required
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Description *
                </Typography>
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Description détaillée de l'image..."
                  rows={3}
                  required
                />
              </div>

              {/* Case à cocher Image populaire */}
              <div className="mb-4">
                <div className="flex items-center gap-3">
                  <Switch
                    checked={formData.isPopular || false}
                    onChange={(e) => {
                      const checked = typeof e === 'boolean' ? e : e.target.checked;
                      handleInputChange('isPopular', checked);
                    }}
                    color="purple"
                  />
                  <div>
                    <Typography variant="small" className="font-medium text-purple-600">
                      Image populaire
                    </Typography>
                    <Typography variant="small" className="text-gray-500">
                      Cette image apparaîtra dans l'onglet "Popular" en plus de sa catégorie normale
                    </Typography>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Catégorie */}
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Catégorie *
                  </Typography>
                  <Select
                    value={formData.category}
                    onChange={(value) => handleInputChange('category', value)}
                  >
                    {categories[activeTab]?.map(cat => (
                      <Option key={cat} value={cat}>
                        {cat.charAt(0).toUpperCase() + cat.slice(1)}
                      </Option>
                    ))}
                  </Select>
                </div>

                {/* Prix */}
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Prix
                  </Typography>
                  <Input
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    placeholder="Ex: Inclus, 205 MAD..."
                  />
                </div>

                {/* Ordre d'affichage */}
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Ordre d'affichage
                  </Typography>
                  <Input
                    type="number"
                    value={formData.displayOrder}
                    onChange={(e) => handleInputChange('displayOrder', parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
              </div>

              {/* Type d'icône */}
              <div>
                <Typography variant="small" className="font-medium mb-2">
                  Type d'icône
                </Typography>
                <div className="grid grid-cols-4 md:grid-cols-6 gap-3">
                  {iconTypes[activeTab]?.map(iconType => (
                    <button
                      key={iconType}
                      type="button"
                      onClick={() => handleInputChange('iconType', iconType)}
                      className={`p-3 rounded-lg border-2 transition-all flex flex-col items-center gap-2 ${
                        formData.iconType === iconType
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {getIconPreview(iconType)}
                      <span className="text-xs capitalize">{iconType}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Upload d'icône personnalisée */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <Typography variant="small" className="font-medium mb-3">
                  Icône personnalisée (optionnel)
                </Typography>

                {!iconPreview && !selectedImage?.iconUrl ? (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <Typography variant="small" className="text-gray-600 mb-2">
                      Glissez une image ici ou cliquez pour sélectionner
                    </Typography>
                    <Typography variant="small" className="text-gray-500 text-xs">
                      JPG, PNG, SVG, WebP - Max 2MB
                    </Typography>
                    <input
                      id="icon-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleIconUpload}
                      className="hidden"
                    />
                    <Button
                      size="sm"
                      variant="outlined"
                      className="mt-3"
                      onClick={() => document.getElementById('icon-upload').click()}
                    >
                      Sélectionner un fichier
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-4 p-4 bg-white rounded-lg border">
                    <div className="flex-shrink-0">
                      {iconPreview ? (
                        <img src={iconPreview} alt="Preview" className="h-12 w-12 object-cover rounded" />
                      ) : selectedImage?.iconUrl ? (
                        <img src={selectedImage.iconUrl} alt="Current" className="h-12 w-12 object-cover rounded" />
                      ) : null}
                    </div>
                    <div className="flex-1">
                      <Typography variant="small" className="font-medium">
                        {iconFile ? iconFile.name : 'Icône actuelle'}
                      </Typography>
                      <Typography variant="small" className="text-gray-500">
                        {iconFile ? `${(iconFile.size / 1024).toFixed(1)} KB` : 'Icône personnalisée'}
                      </Typography>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outlined"
                        onClick={() => document.getElementById('icon-upload').click()}
                      >
                        Changer
                      </Button>
                      <Button
                        size="sm"
                        variant="text"
                        color="red"
                        onClick={removeIconUpload}
                      >
                        Supprimer
                      </Button>
                    </div>
                    <input
                      id="icon-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleIconUpload}
                      className="hidden"
                    />
                  </div>
                )}
              </div>

              {/* Statut */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <Typography variant="small" className="font-medium">
                    Statut de publication
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    {formData.status === 'published' ? 'Image visible publiquement' : 'Image en brouillon'}
                  </Typography>
                </div>
                <Switch
                  checked={formData.status === 'published'}
                  onChange={(e) => {
                    const checked = typeof e === 'boolean' ? e : e.target.checked;
                    handleInputChange('status', checked ? 'published' : 'draft');
                  }}
                  color="green"
                />
              </div>
            </motion.div>
          )}

          {/* Onglet Configuration avancée */}
          {activeFormTab === 'advanced' && (
            <motion.div
              key="advanced"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {activeTab === 'os' ? (
                // Configuration pour OS
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Type d'OS */}
                    <div>
                      <Typography variant="small" className="font-medium mb-2">
                        Type d'OS *
                      </Typography>
                      <Select
                        value={formData.osType}
                        onChange={(value) => handleInputChange('osType', value)}
                      >
                        {osTypes.map(type => (
                          <Option key={type} value={type}>{type}</Option>
                        ))}
                      </Select>
                    </div>

                    {/* Date de sortie */}
                    <div>
                      <Typography variant="small" className="font-medium mb-2">
                        Date de sortie
                      </Typography>
                      <Input
                        type="date"
                        value={formData.releaseDate || ''}
                        onChange={(e) => handleInputChange('releaseDate', e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Support LTS */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <Typography variant="small" className="font-medium">
                        Support LTS (Long Term Support)
                      </Typography>
                      <Typography variant="small" className="text-gray-600">
                        Cette version bénéficie d'un support à long terme
                      </Typography>
                    </div>
                    <Switch
                      checked={formData.isLTS}
                      onChange={(e) => {
                        const checked = typeof e === 'boolean' ? e : e.target.checked;
                        handleInputChange('isLTS', checked);
                      }}
                      color="blue"
                    />
                  </div>
                </div>
              ) : (
                // Configuration pour Apps
                <div className="space-y-4">
                  {/* Type d'application */}
                  <div>
                    <Typography variant="small" className="font-medium mb-2">
                      Type d'application *
                    </Typography>
                    <Select
                      value={formData.type}
                      onChange={(value) => handleInputChange('type', value)}
                    >
                      {appTypes.map(type => (
                        <Option key={type} value={type}>
                          {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Option>
                      ))}
                    </Select>
                  </div>

                  {/* OS supportés */}
                  <div>
                    <Typography variant="small" className="font-medium mb-2">
                      Systèmes d'exploitation supportés
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto">
                      {availableOsImages.map(os => (
                        <div
                          key={os.osId}
                          className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                            formData.supportedOs.includes(os.osId)
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => toggleSupportedOs(os.osId)}
                        >
                          <div className="flex items-center gap-2">
                            <Monitor className="h-4 w-4 text-gray-600" />
                            <div>
                              <div className="text-sm font-medium">{os.name} {os.version}</div>
                              <div className="text-xs text-gray-500">{os.description}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    {formData.supportedOs.length === 0 && (
                      <Typography variant="small" className="text-red-500 mt-1">
                        Sélectionnez au moins un système d'exploitation
                      </Typography>
                    )}
                  </div>

                  {/* Dépendances */}
                  <div>
                    <Typography variant="small" className="font-medium mb-2">
                      Dépendances (optionnel)
                    </Typography>
                    <Textarea
                      value={formData.dependencies.join(', ')}
                      onChange={(e) => handleInputChange('dependencies', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                      placeholder="IDs des applications requises, séparés par des virgules..."
                      rows={2}
                    />
                    <Typography variant="small" className="text-gray-500 mt-1">
                      Laissez vide si aucune dépendance n'est requise
                    </Typography>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Onglet Variantes & Prix (Apps seulement) */}
          {activeFormTab === 'variants' && activeTab === 'apps' && (
            <motion.div
              key="variants"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-between">
                <Typography variant="h6">Variantes de prix</Typography>
                <Button
                  size="sm"
                  onClick={addVariant}
                  className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Ajouter une variante
                </Button>
              </div>

              {formData.variants.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <Typography className="text-gray-500 mb-2">
                    Aucune variante définie
                  </Typography>
                  <Typography variant="small" className="text-gray-400">
                    Ajoutez des variantes pour proposer différentes options de prix
                  </Typography>
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.variants.map((variant, index) => (
                    <Card key={variant.variantId} className="p-4">
                      <div className="flex items-start justify-between mb-4">
                        <Typography variant="small" className="font-medium text-gray-700">
                          Variante #{index + 1}
                        </Typography>
                        <Button
                          size="sm"
                          variant="text"
                          color="red"
                          onClick={() => removeVariant(index)}
                          className="p-1"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Nom de la variante */}
                        <div>
                          <Typography variant="small" className="font-medium mb-2">
                            Nom *
                          </Typography>
                          <Input
                            value={variant.name}
                            onChange={(e) => updateVariant(index, 'name', e.target.value)}
                            placeholder="Ex: 5 Accounts, Web Admin..."
                            required
                          />
                        </div>

                        {/* Prix */}
                        <div>
                          <Typography variant="small" className="font-medium mb-2">
                            Prix (MAD) *
                          </Typography>
                          <Input
                            type="number"
                            value={variant.price}
                            onChange={(e) => updateVariant(index, 'price', parseFloat(e.target.value) || 0)}
                            placeholder="0.00"
                            min="0"
                            step="0.01"
                            required
                          />
                        </div>

                        {/* Description */}
                        <div>
                          <Typography variant="small" className="font-medium mb-2">
                            Description
                          </Typography>
                          <Input
                            value={variant.description}
                            onChange={(e) => updateVariant(index, 'description', e.target.value)}
                            placeholder="Description de la variante..."
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {formData.variants.length > 0 && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <Typography variant="small" className="font-medium text-blue-900 mb-2">
                    Aperçu des prix
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    {formData.variants.map((variant, index) => (
                      <Chip
                        key={index}
                        value={`${variant.name}: ${variant.price} MAD`}
                        color="blue"
                        className="text-xs"
                      />
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </DialogBody>

      <DialogFooter className="flex gap-2">
        <Button variant="text" color="gray" onClick={onClose}>
          Annuler
        </Button>
        <Button
          onClick={() => onSave(iconFile)}
          loading={loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Save className="h-4 w-4 mr-2" />
          {selectedImage ? 'Modifier' : 'Ajouter'}
        </Button>
      </DialogFooter>
    </Dialog>
  );
}
