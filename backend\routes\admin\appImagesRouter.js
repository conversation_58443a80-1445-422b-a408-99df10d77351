const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const VpsApp = require('../../models/VpsApp');
const VpsOS = require('../../models/VpsOS');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

const router = express.Router();

// Configuration multer pour l'upload d'icônes d'applications
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/app-icons');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non supporté. Utilisez JPG, PNG, SVG ou WebP.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB max
  }
});

// Middleware d'authentification pour toutes les routes
// TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
// router.use(authenticateToken);
// router.use(requireAdmin);

/**
 * @route GET /api/admin/app-images
 * @desc Récupérer toutes les applications
 * @access Admin
 */
router.get('/', async (req, res) => {
  try {
    const { status, category, type, search, page = 1, limit = 20 } = req.query;
    
    // Construire le filtre
    let filter = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    if (type && type !== 'all') {
      filter.type = type;
    }
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { type: { $regex: search, $options: 'i' } }
      ];
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [appImages, total] = await Promise.all([
      VpsApp.find(filter)
        .sort({ displayOrder: 1, name: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      VpsApp.countDocuments(filter)
    ]);

    // Ajouter l'URL complète des icônes et enrichir avec les infos des OS supportés
    const appImagesWithDetails = await Promise.all(
      appImages.map(async (app) => {
        const appObj = app.toObject();
        
        // Ajouter l'URL de l'icône
        appObj.iconUrl = app.customIcon ? `/uploads/app-icons/${app.customIcon}` : null;
        
        // Enrichir avec les noms des OS supportés
        if (app.supportedOs && app.supportedOs.length > 0) {
          const supportedOsDetails = await VpsOS.find({ 
            osId: { $in: app.supportedOs } 
          }).select('osId name version');
          
          appObj.supportedOsDetails = supportedOsDetails;
        }
        
        return appObj;
      })
    );

    res.json({
      success: true,
      data: appImagesWithDetails,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des applications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des applications'
    });
  }
});

/**
 * @route GET /api/admin/app-images/:id
 * @desc Récupérer une application par ID
 * @access Admin
 */
router.get('/:id', async (req, res) => {
  try {
    const appImage = await VpsApp.findByAppId(req.params.id);
    
    if (!appImage) {
      return res.status(404).json({
        success: false,
        message: 'Application non trouvée'
      });
    }

    const appObj = appImage.toObject();
    appObj.iconUrl = appImage.customIcon ? `/uploads/app-icons/${appImage.customIcon}` : null;
    
    // Enrichir avec les détails des OS supportés
    if (appImage.supportedOs && appImage.supportedOs.length > 0) {
      const supportedOsDetails = await VpsOS.find({ 
        osId: { $in: appImage.supportedOs } 
      }).select('osId name version description');
      
      appObj.supportedOsDetails = supportedOsDetails;
    }

    res.json({
      success: true,
      data: appObj
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'application:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'application'
    });
  }
});

/**
 * @route POST /api/admin/app-images
 * @desc Créer une nouvelle application
 * @access Admin
 */
router.post('/', upload.single('icon'), async (req, res) => {
  try {
    const {
      name,
      description,
      type = 'control_panel',
      category = 'apps',
      iconType = 'custom',
      displayOrder = 0,
      status = 'draft',
      isPopular = false,
      supportedOs = [],
      variants = [],
      dependencies = []
    } = req.body;

    // Validation des champs requis
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        message: 'Les champs nom et description sont requis'
      });
    }

    // Parser les arrays si ils sont en string (form-data)
    let parsedSupportedOs = [];
    let parsedVariants = [];
    let parsedDependencies = [];

    try {
      parsedSupportedOs = typeof supportedOs === 'string' ? JSON.parse(supportedOs) : supportedOs;
      parsedVariants = typeof variants === 'string' ? JSON.parse(variants) : variants;
      parsedDependencies = typeof dependencies === 'string' ? JSON.parse(dependencies) : dependencies;
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: 'Format invalide pour supportedOs, variants ou dependencies'
      });
    }

    // Validation des OS supportés
    if (parsedSupportedOs.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Au moins un OS supporté est requis'
      });
    }

    // Vérifier que les OS supportés existent
    const existingOs = await VpsOS.find({ osId: { $in: parsedSupportedOs } });
    if (existingOs.length !== parsedSupportedOs.length) {
      return res.status(400).json({
        success: false,
        message: 'Un ou plusieurs OS supportés n\'existent pas'
      });
    }

    // Générer un nouvel appId
    const appId = uuidv4();

    // Générer des IDs pour les variantes si elles n'en ont pas
    const processedVariants = parsedVariants.map(variant => ({
      ...variant,
      variantId: variant.variantId || uuidv4(),
      price: parseFloat(variant.price) || 0
    }));

    // Préparer les données
    const appData = {
      appId,
      name: name.trim(),
      description: description.trim(),
      type,
      category,
      iconType,
      displayOrder: parseInt(displayOrder) || 0,
      status,
      isPopular: isPopular === 'true' || isPopular === true,
      supportedOs: parsedSupportedOs,
      variants: processedVariants,
      dependencies: parsedDependencies
    };

    // Ajouter l'icône personnalisée si uploadée
    if (req.file) {
      appData.customIcon = req.file.filename;
      appData.iconType = 'custom';
    }

    // Créer l'application
    const newAppImage = new VpsApp(appData);
    await newAppImage.save();

    const appObj = newAppImage.toObject();
    appObj.iconUrl = newAppImage.customIcon ? `/uploads/app-icons/${newAppImage.customIcon}` : null;

    res.status(201).json({
      success: true,
      message: 'Application créée avec succès',
      data: appObj
    });
  } catch (error) {
    // Supprimer le fichier uploadé en cas d'erreur
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression du fichier:', unlinkError);
      }
    }

    console.error('Erreur lors de la création de l\'application:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Une application avec cet ID existe déjà'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de l\'application'
    });
  }
});

/**
 * @route PUT /api/admin/app-images/:id
 * @desc Modifier une application
 * @access Admin
 */
router.put('/:id', upload.single('icon'), async (req, res) => {
  try {
    const appImage = await VpsApp.findByAppId(req.params.id);

    if (!appImage) {
      // Supprimer le fichier uploadé si l'app n'existe pas
      if (req.file) {
        try {
          await fs.unlink(req.file.path);
        } catch (unlinkError) {
          console.error('Erreur lors de la suppression du fichier:', unlinkError);
        }
      }

      return res.status(404).json({
        success: false,
        message: 'Application non trouvée'
      });
    }

    const {
      name,
      description,
      type,
      category,
      iconType,
      displayOrder,
      status,
      isPopular,
      supportedOs,
      variants,
      dependencies
    } = req.body;

    // Mettre à jour les champs de base
    if (name) appImage.name = name.trim();
    if (description) appImage.description = description.trim();
    if (type) appImage.type = type;
    if (category) appImage.category = category;
    if (iconType) appImage.iconType = iconType;
    if (displayOrder !== undefined) appImage.displayOrder = parseInt(displayOrder) || 0;
    if (status) appImage.status = status;
    if (isPopular !== undefined) appImage.isPopular = isPopular === 'true' || isPopular === true;

    // Mettre à jour les arrays si fournis
    if (supportedOs) {
      const parsedSupportedOs = typeof supportedOs === 'string' ? JSON.parse(supportedOs) : supportedOs;

      // Vérifier que les OS supportés existent
      if (parsedSupportedOs.length > 0) {
        const existingOs = await VpsOS.find({ osId: { $in: parsedSupportedOs } });
        if (existingOs.length !== parsedSupportedOs.length) {
          return res.status(400).json({
            success: false,
            message: 'Un ou plusieurs OS supportés n\'existent pas'
          });
        }
        appImage.supportedOs = parsedSupportedOs;
      }
    }

    if (variants) {
      const parsedVariants = typeof variants === 'string' ? JSON.parse(variants) : variants;
      const processedVariants = parsedVariants.map(variant => ({
        ...variant,
        variantId: variant.variantId || uuidv4(),
        price: parseFloat(variant.price) || 0
      }));
      appImage.variants = processedVariants;
    }

    if (dependencies) {
      const parsedDependencies = typeof dependencies === 'string' ? JSON.parse(dependencies) : dependencies;
      appImage.dependencies = parsedDependencies;
    }

    // Gérer la nouvelle icône
    if (req.file) {
      // Supprimer l'ancienne icône si elle existe
      if (appImage.customIcon) {
        try {
          await fs.unlink(path.join(__dirname, '../../uploads/app-icons', appImage.customIcon));
        } catch (unlinkError) {
          console.error('Erreur lors de la suppression de l\'ancienne icône:', unlinkError);
        }
      }

      appImage.customIcon = req.file.filename;
      appImage.iconType = 'custom';
    }

    await appImage.save();

    const appObj = appImage.toObject();
    appObj.iconUrl = appImage.customIcon ? `/uploads/app-icons/${appImage.customIcon}` : null;

    res.json({
      success: true,
      message: 'Application modifiée avec succès',
      data: appObj
    });
  } catch (error) {
    // Supprimer le fichier uploadé en cas d'erreur
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression du fichier:', unlinkError);
      }
    }

    console.error('Erreur lors de la modification de l\'application:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la modification de l\'application'
    });
  }
});

/**
 * @route DELETE /api/admin/app-images/:id
 * @desc Supprimer une application
 * @access Admin
 */
router.delete('/:id', async (req, res) => {
  try {
    const appImage = await VpsApp.findByAppId(req.params.id);

    if (!appImage) {
      return res.status(404).json({
        success: false,
        message: 'Application non trouvée'
      });
    }

    // Supprimer l'icône personnalisée si elle existe
    if (appImage.customIcon) {
      try {
        await fs.unlink(path.join(__dirname, '../../uploads/app-icons', appImage.customIcon));
      } catch (unlinkError) {
        console.error('Erreur lors de la suppression de l\'icône:', unlinkError);
      }
    }

    await VpsApp.deleteOne({ appId: req.params.id });

    res.json({
      success: true,
      message: 'Application supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'application:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression de l\'application'
    });
  }
});

/**
 * @route PATCH /api/admin/app-images/:id/status
 * @desc Changer le statut d'une application (published/draft)
 * @access Admin
 */
router.patch('/:id/status', async (req, res) => {
  try {
    const { status } = req.body;

    if (!['published', 'draft'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Statut invalide. Utilisez "published" ou "draft"'
      });
    }

    const appImage = await VpsApp.findByAppId(req.params.id);

    if (!appImage) {
      return res.status(404).json({
        success: false,
        message: 'Application non trouvée'
      });
    }

    appImage.status = status;
    await appImage.save();

    res.json({
      success: true,
      message: `Application ${status === 'published' ? 'publiée' : 'mise en brouillon'} avec succès`,
      data: { status: appImage.status }
    });
  } catch (error) {
    console.error('Erreur lors du changement de statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du changement de statut'
    });
  }
});

/**
 * @route GET /api/admin/app-images/stats/overview
 * @desc Récupérer les statistiques des applications
 * @access Admin
 */
router.get('/stats/overview', async (req, res) => {
  try {
    const [total, published, draft, byType, byCategory] = await Promise.all([
      VpsApp.countDocuments(),
      VpsApp.countDocuments({ status: 'published' }),
      VpsApp.countDocuments({ status: 'draft' }),
      VpsApp.aggregate([
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      VpsApp.aggregate([
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ])
    ]);

    const typeStats = {};
    byType.forEach(item => {
      typeStats[item._id] = item.count;
    });

    const categoryStats = {};
    byCategory.forEach(item => {
      categoryStats[item._id] = item.count;
    });

    res.json({
      success: true,
      data: {
        total,
        published,
        draft,
        byType: typeStats,
        byCategory: categoryStats
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des statistiques'
    });
  }
});

module.exports = router;
