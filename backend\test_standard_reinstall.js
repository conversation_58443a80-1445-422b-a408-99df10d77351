const ContaboProvider = require('./services/providers/ContaboProvider');

async function testStandardReinstall() {
  console.log('🧪 Testing Standard Reinstallation with Password...\n');

  try {
    const contaboProvider = new ContaboProvider();

    // Real VPS instance ID
    const instanceId = 202718127;

    // Get available images
    console.log('📀 Getting available images...');
    const images = await contaboProvider.getImages();
    // Try to find a standard Ubuntu image without cPanel
    const ubuntuImage = images.find(img =>
      img.name.toLowerCase().includes('ubuntu') &&
      img.name.includes('22.04') &&
      !img.name.toLowerCase().includes('cpanel')
    ) || images.find(img => img.name.toLowerCase().includes('ubuntu') && img.name.includes('20.04'));
    
    if (!ubuntuImage) {
      throw new Error('Ubuntu 22.04 image not found');
    }
    
    console.log(`✅ Selected image: ${ubuntuImage.name} (ID: ${ubuntuImage.imageId})`);

    // Standard installation data with root user enabled
    const standardPassword = 'StandardTest123!';
    const standardReinstallData = {
      imageId: ubuntuImage.imageId,
      password: standardPassword,
      enableRootUser: true  // Force enable root user for password authentication
    };

    console.log('\n📤 Standard reinstall data prepared:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', standardPassword ? '***' : 'Not set');
    console.log('- Type: Standard installation (password only)');

    console.log('\n🔄 Starting standard reinstallation...');
    console.log('📝 Password being sent:', standardPassword);
    
    const result = await contaboProvider.reinstallVPS(instanceId, standardReinstallData);

    console.log('\n✅ Standard reinstallation completed successfully!');
    console.log('Result:', result.message);
    console.log('Instance ID:', result.instanceId);

    console.log('\n🔍 Password verification steps:');
    console.log('1. Wait for the VPS to complete reinstallation (5-10 minutes)');
    console.log('2. Try SSH connection with:');
    console.log(`   ssh root@[VPS_IP] (password: ${standardPassword})`);
    console.log('3. If connection fails, check if password was properly set');

    console.log('\n⚠️ Important notes:');
    console.log('- Make sure the VPS has finished reinstalling before testing SSH');
    console.log('- Some images might have different default users (ubuntu, admin, etc.)');
    console.log('- Check VPS status in Contabo panel before testing');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testStandardReinstall();
