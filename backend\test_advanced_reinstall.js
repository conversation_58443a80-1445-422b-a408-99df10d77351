/**
 * Test script pour l'installation avancée VPS
 * Teste tous les paramètres avancés avec l'API Contabo
 */

const ContaboProvider = require('./services/providers/ContaboProvider');

async function testAdvancedReinstall() {
  console.log('🚀 Test de l\'installation avancée VPS');
  console.log('=====================================');

  try {
    const contaboProvider = new ContaboProvider();
    
    // 1. Authentification
    console.log('\n1️⃣ Authentification...');
    await contaboProvider.authenticate();
    console.log('✅ Authentification réussie');

    // 2. Récupérer les VPS disponibles
    console.log('\n2️⃣ Récupération des VPS...');
    const vpsInstances = await contaboProvider.getCustomerVPS('test-user');
    console.log(`✅ ${vpsInstances.length} VPS trouvé(s)`);
    
    if (vpsInstances.length === 0) {
      console.log('❌ Aucun VPS disponible pour le test');
      return;
    }

    // Utiliser le premier VPS disponible
    const testVPS = vpsInstances[0];
    console.log(`🎯 VPS de test: ${testVPS.name} (${testVPS.id})`);

    // 3. Récupérer les images disponibles
    console.log('\n3️⃣ Récupération des images...');
    const imagesResponse = await contaboProvider.getAvailableImages();
    const images = imagesResponse.data || [];
    console.log(`✅ ${images.length} images disponibles`);

    // Afficher quelques images pour debug
    console.log('📋 Premières images disponibles:');
    images.slice(0, 3).forEach(img => {
      console.log(`   - ${img.name} (${img.imageId})`);
    });

    // Trouver une image Ubuntu pour le test
    const ubuntuImage = images.find(img =>
      img.name.toLowerCase().includes('ubuntu') &&
      img.name.toLowerCase().includes('22.04')
    );

    if (!ubuntuImage) {
      console.log('⚠️  Aucune image Ubuntu 22.04 trouvée, utilisation de la première image');
      const firstImage = images[0];
      if (firstImage) {
        console.log(`🐧 Image sélectionnée: ${firstImage.name} (${firstImage.imageId})`);
      } else {
        console.log('❌ Aucune image disponible');
        return;
      }
    } else {
      console.log(`🐧 Image Ubuntu sélectionnée: ${ubuntuImage.name} (${ubuntuImage.imageId})`);
    }

    // 4. Récupérer les applications disponibles
    console.log('\n4️⃣ Récupération des applications...');
    const applicationsResponse = await contaboProvider.getAvailableApplications();
    const applications = applicationsResponse.data || [];
    console.log(`✅ ${applications.length} applications disponibles`);

    // Afficher quelques applications pour debug
    if (applications.length > 0) {
      console.log('📋 Premières applications disponibles:');
      applications.slice(0, 3).forEach(app => {
        console.log(`   - ${app.name} (${app.applicationId})`);
      });
    }

    // Trouver Docker pour le test
    const dockerApp = applications.find(app =>
      app.name.toLowerCase().includes('docker')
    );

    if (dockerApp) {
      console.log(`🐳 Application trouvée: ${dockerApp.name} (${dockerApp.applicationId})`);
    }

    // 5. Test de l'installation avancée
    console.log('\n5️⃣ Test de l\'installation avancée...');
    
    const selectedImage = ubuntuImage || images[0];
    const advancedReinstallData = {
      imageId: selectedImage.imageId,
      password: 'AdvancedTest123!',
      enableRootUser: true,
      sshKeys: [], // Pas de clé SSH pour ce test
      userData: `#cloud-config
# Configuration Cloud-Init pour test avancé
packages:
  - htop
  - curl
  - git
runcmd:
  - echo "Installation avancée terminée" > /root/advanced_install.log
  - systemctl enable ssh
  - systemctl start ssh
`,
      // Ajouter Docker si disponible
      ...(dockerApp && { applicationId: dockerApp.applicationId }),
      customScript: `#!/bin/bash
echo "Script personnalisé exécuté" >> /root/custom_script.log
date >> /root/custom_script.log
`
    };

    console.log('📤 Données d\'installation avancée:');
    console.log(JSON.stringify(advancedReinstallData, null, 2));

    // ATTENTION: Décommentez la ligne suivante pour effectuer la réinstallation réelle
    // const result = await contaboProvider.reinstallVPS(testVPS.id, advancedReinstallData);
    
    console.log('\n⚠️  SIMULATION SEULEMENT - Décommentez la ligne pour une vraie réinstallation');
    console.log('✅ Test de l\'installation avancée terminé avec succès');
    
    // Simulation du résultat
    const simulatedResult = {
      success: true,
      action: 'reinstall',
      instanceId: testVPS.id,
      message: "VPS reinstallation started successfully",
      data: {
        instanceId: testVPS.id,
        status: 'reinstalling',
        parameters: advancedReinstallData
      }
    };
    
    console.log('\n📋 Résultat simulé:');
    console.log(JSON.stringify(simulatedResult, null, 2));

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Exécuter le test
if (require.main === module) {
  testAdvancedReinstall();
}

module.exports = { testAdvancedReinstall };
