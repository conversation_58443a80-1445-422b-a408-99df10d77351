/**
 * Test script for VPS Reinstall API
 * This script tests the connection between frontend and backend for reinstall functionality
 * NO AUTOMATIC REINSTALLATION - Only tests API endpoints
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5002'; // Backend URL

// Test data
const testData = {
  // Standard installation
  standardInstall: {
    imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb', // Ubuntu 22.04 LTS
    password: 'TestPassword123!'
  },
  
  // Advanced installation
  advancedInstall: {
    imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb',
    password: 'TestPassword123!',
    enableRootUser: true,
    sshKeys: ['ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC...'],
    userData: '#cloud-config\npackages:\n  - nginx\n  - git',
    applicationId: 'docker',
    customScript: '#!/bin/bash\necho "Custom installation script"\napt update'
  }
};

async function testGetAvailableImages() {
  try {
    console.log('🧪 Testing: Get Available Images');
    const response = await axios.get(`${BASE_URL}/api/vps/images`);
    
    console.log('✅ Status:', response.status);
    console.log('📋 Images found:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('📄 First image:', response.data.data[0]);
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Error getting images:', error.response?.data || error.message);
    throw error;
  }
}

async function testGetAvailableApplications() {
  try {
    console.log('\n🧪 Testing: Get Available Applications');
    const response = await axios.get(`${BASE_URL}/api/vps/applications`);
    
    console.log('✅ Status:', response.status);
    console.log('📱 Applications found:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('📄 First application:', response.data.data[0]);
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Error getting applications:', error.response?.data || error.message);
    throw error;
  }
}

async function testReinstallVPS(instanceId, reinstallData, testName) {
  try {
    console.log(`\n🧪 Testing: ${testName}`);
    console.log('📤 Payload:', JSON.stringify(reinstallData, null, 2));
    
    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/${instanceId}/reinstall`,
      reinstallData,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-request-id': generateRequestId()
        }
      }
    );
    
    console.log('✅ Status:', response.status);
    console.log('📋 Response:', response.data);
    
    return response.data;
  } catch (error) {
    console.error(`❌ Error in ${testName}:`, error.response?.data || error.message);
    throw error;
  }
}

function generateRequestId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function runTests() {
  console.log('🚀 Starting VPS Reinstall API Tests\n');
  
  try {
    // Test 1: Get available images
    await testGetAvailableImages();
    
    // Test 2: Get available applications
    await testGetAvailableApplications();
    
    // Test 3: Test reinstall endpoint validation (without actual reinstall)
    console.log('\n🧪 Testing: Reinstall endpoint validation');
    console.log('⚠️  Testing validation only - NO ACTUAL REINSTALLATION will occur');

    try {
      // Test with invalid data to check validation
      const response = await axios.post(
        `${BASE_URL}/api/vps/instances/test-instance/reinstall`,
        { /* empty data to test validation */ },
        {
          headers: { 'Content-Type': 'application/json' },
          validateStatus: () => true // Accept all status codes
        }
      );

      console.log('✅ Validation test status:', response.status);
      console.log('📋 Validation response:', response.data);
    } catch (error) {
      console.log('✅ Expected validation error:', error.response?.data || error.message);
    }
    
    console.log('\n🎉 All available tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testGetAvailableImages,
  testGetAvailableApplications,
  testReinstallVPS,
  testData
};
