const mongoose = require('mongoose');

// Modèle pour les régions VPS configurées par l'admin
const vpsRegionSchema = new mongoose.Schema({
  regionId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true
  },
  countryCode: {
    type: String,
    required: true,
    trim: true,
    uppercase: true,
    maxlength: 2
  },
  continent: {
    type: String,
    required: true,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true
  },
  flag: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance'],
    default: 'active'
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  // Prix supplémentaires par package
  pricing: [{
    packageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Package',
      required: true
    },
    packageName: {
      type: String,
      required: true
    },
    additionalPrice: {
      type: Number,
      required: true,
      min: 0,
      default: 0 // Prix supplémentaire en MAD (Dirham Marocain)
    }
  }],
  // Informations techniques
  datacenterProvider: {
    type: String,
    default: 'contabo'
  },
  networkSpeed: {
    type: String,
    default: '1 Gbps'
  },
  uptime: {
    type: String,
    default: '99.9%'
  },
  coordinates: {
    latitude: Number,
    longitude: Number
  },
  features: [{
    type: String
  }],
  availableServices: [{
    type: String,
    enum: ['vps', 'dedicated', 'storage'],
    default: ['vps']
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Index pour les recherches
vpsRegionSchema.index({ status: 1, displayOrder: 1 });
vpsRegionSchema.index({ regionId: 1 });
vpsRegionSchema.index({ country: 1 });
vpsRegionSchema.index({ continent: 1 });



module.exports = mongoose.model('VPSRegion', vpsRegionSchema);
