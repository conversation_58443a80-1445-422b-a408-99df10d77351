/**
 * Script pour réinitialiser le mot de passe d'un VPS via l'API Contabo
 */

const ContaboProvider = require('./services/providers/ContaboProvider');
require('dotenv').config();

async function resetVPSPassword(instanceId, newPassword) {
  console.log('🔐 RÉINITIALISATION DU MOT DE PASSE VPS');
  console.log('=' .repeat(50));
  console.log(`📋 Instance ID: ${instanceId}`);
  console.log(`📋 Nouveau mot de passe: ${newPassword}`);
  console.log('');

  try {
    // 1. Authentification
    console.log('1️⃣ Authentification Contabo...');
    const provider = new ContaboProvider();
    await provider.authenticate();
    console.log('✅ Authentification réussie');
    console.log('');

    // 2. Vérifier que l'instance existe
    console.log('2️⃣ Vérification de l\'instance...');
    const instances = await provider.getCustomerVPS('test-user');
    const instance = instances.find(vps => vps.id.toString() === instanceId.toString());

    if (!instance) {
      console.log('❌ Instance non trouvée');
      return false;
    }

    console.log(`✅ Instance trouvée: ${instance.name}`);
    console.log(`📋 Statut actuel: ${instance.status}`);
    console.log('');

    // 3. Réinitialiser le mot de passe
    console.log('3️⃣ Réinitialisation du mot de passe...');
    
    // Utiliser l'API Contabo pour réinitialiser le mot de passe
    const resetData = {
      rootPassword: newPassword
    };

    console.log('📤 Envoi de la requête de réinitialisation...');
    
    // Note: L'endpoint exact peut varier selon l'API Contabo
    // Nous utilisons l'endpoint de contrôle d'instance
    const response = await provider.makeRequest(
      'POST',
      `/compute/instances/${instanceId}/actions/reset-password`,
      resetData
    );

    if (response && response.status === 200) {
      console.log('✅ Mot de passe réinitialisé avec succès !');
      console.log('');
      console.log('📝 PROCHAINES ÉTAPES:');
      console.log('   1. Attendez 2-3 minutes pour la propagation');
      console.log(`   2. Testez SSH: ssh root@${instance.ip}`);
      console.log(`   3. Utilisez le nouveau mot de passe: ${newPassword}`);
      console.log('');
      
      return true;
    } else {
      console.log('⚠️ Réponse inattendue de l\'API');
      console.log('📋 Réponse:', response);
      return false;
    }

  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation:', error.message);
    
    // Si l'endpoint reset-password n'existe pas, essayons une autre méthode
    if (error.message.includes('404') || error.message.includes('Not Found')) {
      console.log('');
      console.log('ℹ️ L\'endpoint reset-password n\'est pas disponible');
      console.log('📝 SOLUTIONS ALTERNATIVES:');
      console.log('   1. Utilisez la console web Contabo');
      console.log('   2. Réinstallez le VPS avec un nouveau mot de passe');
      console.log('   3. Contactez le support Contabo');
    }
    
    return false;
  }
}

// Fonction alternative : Réinstaller avec un nouveau mot de passe
async function reinstallWithNewPassword(instanceId, imageId, newPassword) {
  console.log('🔄 RÉINSTALLATION AVEC NOUVEAU MOT DE PASSE');
  console.log('=' .repeat(50));
  
  try {
    const provider = new ContaboProvider();
    await provider.authenticate();
    
    const reinstallData = {
      imageId: imageId,
      password: newPassword,
      enableRootUser: true
    };
    
    console.log('📤 Réinstallation en cours...');
    const result = await provider.reinstallVPS(instanceId, reinstallData);
    
    if (result.success) {
      console.log('✅ Réinstallation démarrée avec succès !');
      console.log('⏳ Attendez 10-15 minutes puis testez SSH');
      return true;
    } else {
      console.log('❌ Échec de la réinstallation');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    return false;
  }
}

// Test avec votre instance
if (require.main === module) {
  const INSTANCE_ID = '202718127';
  const NEW_PASSWORD = 'NewSecurePass123!'; // Nouveau mot de passe plus simple
  const IMAGE_ID = 'afecbb85-e2fc-46f0-9684-b46b1faf00bb'; // Même image que précédemment
  
  console.log('🚀 SCRIPT DE RÉINITIALISATION DE MOT DE PASSE');
  console.log('');
  console.log('Choisissez une option:');
  console.log('1. Réinitialiser le mot de passe (si supporté)');
  console.log('2. Réinstaller avec un nouveau mot de passe');
  console.log('');
  
  // Option 1: Essayer de réinitialiser le mot de passe
  resetVPSPassword(INSTANCE_ID, NEW_PASSWORD)
    .then(success => {
      if (!success) {
        console.log('');
        console.log('🔄 Tentative de réinstallation avec nouveau mot de passe...');
        return reinstallWithNewPassword(INSTANCE_ID, IMAGE_ID, NEW_PASSWORD);
      }
      return success;
    })
    .then(finalResult => {
      if (finalResult) {
        console.log('');
        console.log('🎉 SUCCÈS ! Nouveau mot de passe configuré');
        console.log(`📋 Mot de passe: ${NEW_PASSWORD}`);
      } else {
        console.log('');
        console.log('❌ Échec de toutes les tentatives');
        console.log('📝 Utilisez la console web Contabo ou contactez le support');
      }
    })
    .catch(console.error);
}

module.exports = { resetVPSPassword, reinstallWithNewPassword };
