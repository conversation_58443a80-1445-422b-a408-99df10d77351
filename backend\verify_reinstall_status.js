/**
 * Script pour vérifier le statut de réinstallation d'un VPS
 */

const ContaboProvider = require('./services/providers/ContaboProvider');
require('dotenv').config();

async function verifyReinstallStatus(instanceId) {
  console.log('🔍 Vérification du statut de réinstallation\n');
  console.log(`📋 Instance ID: ${instanceId}`);
  console.log('=' .repeat(50));

  try {
    // 1. Initialiser le provider Contabo
    console.log('1️⃣ Connexion à l\'API Contabo...');
    const provider = new ContaboProvider();
    await provider.authenticate();
    console.log('✅ Authentification réussie\n');

    // 2. Récupérer les détails de l'instance
    console.log('2️⃣ Récupération des détails de l\'instance...');
    const instances = await provider.getCustomerVPS('test-user');
    const instance = instances.find(vps => vps.id.toString() === instanceId.toString());

    if (!instance) {
      console.log('❌ Instance non trouvée');
      return;
    }

    console.log('✅ Instance trouvée\n');

    // 3. Afficher les informations actuelles
    console.log('📋 INFORMATIONS ACTUELLES:');
    console.log(`   Nom: ${instance.name}`);
    console.log(`   ID: ${instance.id}`);
    console.log(`   Statut: ${instance.status}`);
    console.log(`   IP: ${instance.ip}`);
    console.log(`   Région: ${instance.region || 'N/A'}`);
    console.log(`   OS: ${instance.osType || 'N/A'}`);
    console.log(`   Créé: ${instance.createdAt || 'N/A'}`);
    console.log('');

    // 4. Vérifier le statut de réinstallation
    console.log('3️⃣ Analyse du statut...');
    
    const status = instance.status.toLowerCase();
    let statusMessage = '';
    let statusIcon = '';

    switch (status) {
      case 'running':
        statusIcon = '✅';
        statusMessage = 'Instance en cours d\'exécution - Réinstallation probablement terminée';
        break;
      case 'stopped':
        statusIcon = '⏹️';
        statusMessage = 'Instance arrêtée - Peut être en cours de réinstallation';
        break;
      case 'installing':
      case 'provisioning':
        statusIcon = '🔄';
        statusMessage = 'Réinstallation en cours...';
        break;
      case 'error':
        statusIcon = '❌';
        statusMessage = 'Erreur détectée - Vérifiez le panel Contabo';
        break;
      default:
        statusIcon = '❓';
        statusMessage = `Statut inconnu: ${status}`;
    }

    console.log(`${statusIcon} ${statusMessage}\n`);

    // 5. Recommandations basées sur le statut
    console.log('4️⃣ Recommandations:');
    
    if (status === 'running') {
      console.log('✅ Votre VPS semble prêt !');
      console.log('📝 Prochaines étapes:');
      console.log('   1. Testez la connexion SSH avec le nouveau mot de passe');
      console.log('   2. Vérifiez que le bon OS est installé');
      console.log('   3. Configurez vos applications si nécessaire');
    } else if (status === 'installing' || status === 'provisioning') {
      console.log('⏳ Réinstallation en cours...');
      console.log('📝 Attendez quelques minutes et relancez cette vérification');
    } else {
      console.log('⚠️ Vérifiez le panel Contabo pour plus de détails');
    }

    console.log('');

    // 6. Test de connectivité (ping)
    console.log('5️⃣ Test de connectivité...');
    if (instance.ip) {
      console.log(`📡 Test de ping vers ${instance.ip}...`);
      // Note: Le ping depuis Node.js nécessite des permissions spéciales
      console.log('ℹ️ Testez manuellement: ping ' + instance.ip);
    }

    return {
      success: true,
      instance: instance,
      status: status,
      message: statusMessage,
      recommendations: getRecommendations(status)
    };

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

function getRecommendations(status) {
  const recommendations = [];
  
  switch (status.toLowerCase()) {
    case 'running':
      recommendations.push('Testez la connexion SSH');
      recommendations.push('Vérifiez la version de l\'OS');
      recommendations.push('Configurez vos services');
      break;
    case 'installing':
    case 'provisioning':
      recommendations.push('Attendez la fin de l\'installation');
      recommendations.push('Vérifiez dans 5-10 minutes');
      break;
    case 'stopped':
      recommendations.push('Démarrez l\'instance si nécessaire');
      recommendations.push('Vérifiez les logs dans le panel Contabo');
      break;
    default:
      recommendations.push('Consultez le panel Contabo');
      recommendations.push('Contactez le support si nécessaire');
  }
  
  return recommendations;
}

// Test avec votre instance réelle
if (require.main === module) {
  const INSTANCE_ID = '202718127'; // Votre instance réelle
  
  console.log('🚀 Vérification du statut de réinstallation');
  console.log(`📋 Instance: ${INSTANCE_ID}\n`);
  
  verifyReinstallStatus(INSTANCE_ID)
    .then(result => {
      if (result.success) {
        console.log('\n🎉 Vérification terminée avec succès !');
      } else {
        console.log('\n❌ Erreur lors de la vérification');
      }
    })
    .catch(console.error);
}

module.exports = { verifyReinstallStatus };
