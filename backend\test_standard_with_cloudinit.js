const ContaboProvider = require('./services/providers/ContaboProvider');

async function testStandardWithCloudInit() {
  console.log('🧪 Testing Standard Installation with Cloud-Init Password Auth...\n');

  try {
    const contaboProvider = new ContaboProvider();

    // Real VPS instance ID
    const instanceId = 202718127;

    // Get available images - prefer Ubuntu standard
    console.log('📀 Getting available images...');
    const images = await contaboProvider.getImages();
    const ubuntuImage = images.find(img => 
      img.name.toLowerCase().includes('ubuntu') && 
      img.name.includes('22.04') && 
      !img.name.toLowerCase().includes('plesk') && 
      !img.name.toLowerCase().includes('cpanel')
    ) || images.find(img => img.name.toLowerCase().includes('ubuntu'));
    
    if (!ubuntuImage) {
      throw new Error('Ubuntu image not found');
    }
    
    console.log(`✅ Selected image: ${ubuntuImage.name} (ID: ${ubuntuImage.imageId})`);

    // Standard installation with Cloud-Init (like the frontend now does)
    const testPassword = 'CloudInitTest123!';
    const standardReinstallData = {
      imageId: ubuntuImage.imageId,
      password: testPassword,
      enableRootUser: true,
      userData: `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${testPassword}
chpasswd:
  expire: false

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Password authentication enabled for standard installation" >> /root/install.log`
    };

    console.log('\n📤 Standard reinstall data with Cloud-Init:');
    console.log('- Image:', ubuntuImage.name);
    console.log('- Password:', testPassword);
    console.log('- Enable Root User:', true);
    console.log('- Cloud-Init: SSH password auth configuration');

    console.log('\n🔄 Starting standard reinstallation with Cloud-Init...');
    const result = await contaboProvider.reinstallVPS(instanceId, standardReinstallData);

    console.log('\n✅ Standard reinstallation with Cloud-Init completed successfully!');
    console.log('Result:', result.message);
    console.log('Instance ID:', result.instanceId);

    console.log('\n🔍 Testing instructions:');
    console.log('1. Wait 5-10 minutes for reinstallation and Cloud-Init to complete');
    console.log('2. Test SSH connection:');
    console.log(`   ssh root@185.217.125.69 (password: ${testPassword})`);
    console.log('3. Cloud-Init should have:');
    console.log('   - Enabled SSH password authentication');
    console.log('   - Set root password');
    console.log('   - Configured SSH to allow root login');
    console.log('   - Restarted SSH service');

    console.log('\n📝 What Cloud-Init does:');
    console.log('- Creates /etc/ssh/sshd_config.d/99-enable-password-auth.conf');
    console.log('- Sets PasswordAuthentication yes');
    console.log('- Sets PermitRootLogin yes');
    console.log('- Restarts SSH service');
    console.log('- Logs completion to /root/install.log');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testStandardWithCloudInit();
