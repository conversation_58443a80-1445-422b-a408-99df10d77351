/**
 * VPS Service
 * Main service for VPS operations following Single Responsibility Principle
 * Handles business logic and coordinates between providers and data layer
 */

const VPSProviderFactory = require("./providers/VPSProviderFactory");
const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const VPSInstance = require("../models/VPSInstance");
const Package = require("../models/Package");
const OrderStatus = require("../constants/enums/order-status");
const PaymentMethod = require("../constants/enums/payment-method");
const { v4: uuidv4 } = require("uuid");

// Define VPS status enum directly to avoid import issues
const VPSStatus = {
  PENDING: "PENDING",
  PROVISIONING: "PROVISIONING",
  ACTIVE: "ACTIVE",
  SUSPENDED: "SUSPENDED",
  TERMINATED: "TERMINATED",
  FAILED: "FAILED",
};

class VPSService {
  constructor(providerName = null) {
    this.defaultProvider =
      providerName || process.env.DEFAULT_VPS_PROVIDER || "contabo";
  }

  /**
   * Get VPS provider instance
   * @param {string} providerName - Provider name
   * @returns {VPSProviderInterface} Provider instance
   */
  getProvider(providerName = null) {
    const provider = providerName || this.defaultProvider;
    return VPSProviderFactory.createProvider(provider);
  }

  /**
   * Get available VPS plans from all or specific provider
   * @param {string} providerName - Optional provider name
   * @returns {Promise<Array>} Array of VPS plans
   */
  async getAvailablePlans(providerName = null) {
    try {
      if (providerName) {
        const provider = this.getProvider(providerName);
        return await provider.getPlans();
      }

      // Get plans from all providers
      const supportedProviders = VPSProviderFactory.getSupportedProviders();
      const allPlans = [];

      for (const provider of supportedProviders) {
        try {
          const providerInstance = this.getProvider(provider);
          const plans = await providerInstance.getPlans();
          allPlans.push(...plans);
        } catch (error) {
          console.warn(
            `Failed to fetch plans from ${provider}:`,
            error.message
          );
        }
      }

      return allPlans;
    } catch (error) {
      console.error("Failed to fetch VPS plans:", error.message);
      throw new Error("Unable to fetch VPS plans at this time");
    }
  }

  /**
   * Get VPS plan details
   * @param {string} planId - Plan ID
   * @param {string} providerName - Provider name
   * @returns {Promise<Object>} Plan details
   */
  async getPlanDetails(planId, providerName = null) {
    try {
      const provider = this.getProvider(providerName);
      return await provider.getPlanDetails(planId);
    } catch (error) {
      console.error(
        `Failed to fetch plan details for ${planId}:`,
        error.message
      );
      throw new Error("Unable to fetch plan details");
    }
  }

  /**
   * Create a VPS order using existing Order/SubOrder system
   * @param {Object} orderData - Order data with packageId from ZTech database
   * @param {Object} user - User object
   * @returns {Promise<Object>} Created order
   */
  async createVPSOrder(orderData, user) {
    try {
      // Get the VPS package from ZTech database using the packageId
      const vpsPackage = await Package.findById(orderData.packageId)
        .populate("brand")
        .populate("specifications");

      if (!vpsPackage) {
        throw new Error("VPS package not found");
      }

      // Verify this is a VPS package with provider configuration
      if (
        !vpsPackage.vpsConfig ||
        !vpsPackage.vpsConfig.provider ||
        !vpsPackage.vpsConfig.providerProductId
      ) {
        throw new Error(
          "Invalid VPS package configuration - missing provider details"
        );
      }

      // Get plan details from provider using the configured product ID
      const planDetails = await this.getPlanDetails(
        vpsPackage.vpsConfig.providerProductId,
        vpsPackage.vpsConfig.provider
      );

      if (!planDetails) {
        throw new Error(
          `Provider plan not found for product ID: ${vpsPackage.vpsConfig.providerProductId}`
        );
      }

      // Calculate pricing using ZTech package price (not provider price)
      const period = orderData.billingCycle === "yearly" ? 12 : 1;
      const basePrice = vpsPackage.price * period;
      const totalAmount = basePrice * (1 + 0.2); // Including 20% tax
      const taxAmount = basePrice * 0.2;

      // Generate unique identifiers
      const orderIdentifiant = uuidv4().split("-")[0].toUpperCase();
      const subOrderIdentifiant = uuidv4().split("-")[0];

      // Create SubOrder with VPS configuration
      const subOrder = new SubOrder({
        identifiant: subOrderIdentifiant,
        package: vpsPackage._id,
        quantity: 1,
        period: period,
        price: basePrice,
        basedPrice: basePrice,
        discount: 0,
        status: OrderStatus.PENDING,
        vps: {
          provider: vpsPackage.vpsConfig.provider,
          planId: vpsPackage.vpsConfig.providerProductId, // Use Contabo product ID (V91, V92, etc.)
          region: orderData.region,
          operatingSystem: orderData.operatingSystem,
          displayName: orderData.displayName,
          status: VPSStatus.PENDING,
          sshKeys: orderData.sshKeys || [],
          specifications: {
            cpu: planDetails.cpu,
            ram: planDetails.ram,
            storage: planDetails.storage,
            bandwidth: planDetails.bandwidth,
          },
        },
      });

      await subOrder.save();

      // Create main Order
      const order = new Order({
        user: user._id,
        identifiant: orderIdentifiant,
        subOrders: [subOrder._id],
        totalPrice: totalAmount,
        subTotal: basePrice,
        totalDiscount: 0,
        shippingFee: 0,
        taxRate: 0.2,
        taxAmount: taxAmount,
        status: OrderStatus.PENDING,
        paymentMethod: PaymentMethod.PAYZONE,
        isPaid: false,
        billingInfo: {
          BillToName: orderData.billingInfo.name,
          email: orderData.billingInfo.email,
          phone: orderData.billingInfo.phone,
          address: orderData.billingInfo.address,
          country: orderData.billingInfo.country,
          isCompany: orderData.billingInfo.isCompany || false,
          companyICE: orderData.billingInfo.companyICE,
          companyAddress: orderData.billingInfo.companyAddress,
          companyPhone: orderData.billingInfo.companyPhone,
          companyEmail: orderData.billingInfo.companyEmail,
        },
        customerIpAddress: orderData.ipAddress,
        customerUserAgent: orderData.userAgent,
      });

      const savedOrder = await order.save();

      // Return populated order
      return await Order.findById(savedOrder._id)
        .populate("user", "firstName lastName email")
        .populate({
          path: "subOrders",
          populate: {
            path: "package",
            model: "Package",
          },
        });
    } catch (error) {
      console.error("Failed to create VPS order:", error.message);
      throw error;
    }
  }

  /**
   * Create a VPS package in the system
   * @param {Object} planDetails - Plan details from provider
   * @param {string} provider - Provider name
   * @returns {Promise<Object>} Created package
   */
  async createVPSPackage(planDetails, provider) {
    try {
      // Find VPS category and brand (create if needed)
      const Category = require("../models/Category");
      const Brand = require("../models/Brand");

      let vpsCategory = await Category.findOne({ name: "VPS" });
      if (!vpsCategory) {
        vpsCategory = new Category({
          name: "VPS",
          name_fr: "VPS",
          description: "Virtual Private Servers",
          description_fr: "Serveurs Privés Virtuels",
        });
        await vpsCategory.save();
      }

      let vpsBrand = await Brand.findOne({ name: provider });
      if (!vpsBrand) {
        vpsBrand = new Brand({
          name: provider,
          name_fr: provider,
          category: vpsCategory._id,
          packages: [],
        });
        await vpsBrand.save();
      }

      // Create VPS package
      const vpsPackage = new Package({
        reference: planDetails.id,
        name: planDetails.name,
        name_fr: planDetails.name,
        description:
          planDetails.description ||
          `${planDetails.cpu} CPU, ${planDetails.ram}GB RAM, ${planDetails.storage}GB Storage`,
        description_fr:
          planDetails.description ||
          `${planDetails.cpu} CPU, ${planDetails.ram}GB RAM, ${planDetails.storage}GB Stockage`,
        price: planDetails.price.monthly,
        regularPrice: planDetails.price.monthly,
        category: vpsCategory._id,
        brand: vpsBrand._id,
        specifications: [],
        status: "PUBLISHED",
      });

      const savedPackage = await vpsPackage.save();

      // Update brand with new package
      vpsBrand.packages.push(savedPackage._id);
      await vpsBrand.save();

      return savedPackage;
    } catch (error) {
      console.error("Failed to create VPS package:", error.message);
      throw error;
    }
  }

  /**
   * Process VPS order payment confirmation
   * @param {string} orderId - Order identifier
   * @param {Object} paymentData - Payment confirmation data
   * @returns {Promise<Object>} Updated order
   */
  async processPaymentConfirmation(orderId, paymentData) {
    try {
      const order = await Order.findOne({ identifiant: orderId }).populate(
        "subOrders"
      );

      if (!order) {
        throw new Error("Order not found");
      }

      // Update payment information
      order.isPaid = true;
      order.datePaid = new Date();
      order.transactionId = paymentData.transactionId;
      order.isPaymentProcessed = true;
      order.callBackConfirmed = {
        value: true,
        at: new Date(),
      };
      order.status = OrderStatus.PROCESSING;

      await order.save();

      // Start VPS provisioning for each VPS suborder
      for (const subOrder of order.subOrders) {
        if (subOrder.vps) {
          await this.provisionVPS(order, subOrder);
        }
      }

      return order;
    } catch (error) {
      console.error("Failed to process payment confirmation:", error.message);
      throw error;
    }
  }

  /**
   * Provision VPS instance
   * @param {Object} order - Main order
   * @param {Object} subOrder - SubOrder with VPS configuration
   * @returns {Promise<Object>} VPS instance
   */
  async provisionVPS(order, subOrder) {
    try {
      // Update suborder status to provisioning
      subOrder.vps.status = VPSStatus.PROVISIONING;
      subOrder.status = OrderStatus.PROCESSING;
      await subOrder.save();

      const provider = this.getProvider(subOrder.vps.provider);

      // Prepare VPS creation data
      const vpsData = {
        planId: subOrder.vps.planId,
        region: subOrder.vps.region,
        imageId: this.getOSImageId(subOrder.vps.operatingSystem),
        displayName: subOrder.vps.displayName,
        // Don't send rootPassword to Contabo - they don't accept it during creation
        sshKeys: subOrder.vps.sshKeys || [],
      };

      // Store client's chosen password for later use
      const clientChosenPassword = subOrder.vps.rootPassword;

      // Create VPS with provider
      const providerResponse = await provider.createVPS(vpsData);

      // Update suborder with provider data
      subOrder.vps.providerInstanceId = providerResponse.id;
      subOrder.vps.ipAddress = providerResponse.ip;
      subOrder.vps.ipv6Address = providerResponse.ipv6;
      subOrder.vps.provisionedAt = new Date();

      // Handle password: Use client's choice, Contabo's generated, or generate new one
      let finalPassword;
      if (
        clientChosenPassword &&
        this.validateContaboPassword(clientChosenPassword)
      ) {
        // Client provided a valid password
        finalPassword = clientChosenPassword;
        console.log(
          `🔑 Using client's chosen password for VPS ${subOrder.identifiant}`
        );
      } else if (
        clientChosenPassword &&
        !this.validateContaboPassword(clientChosenPassword)
      ) {
        // Client provided invalid password - generate a valid one
        finalPassword = this.generateSecurePassword();
        console.log(
          `⚠️  Client's password invalid (must be 8-30 alphanumeric chars). Generated new password for VPS ${subOrder.identifiant}`
        );
      } else if (providerResponse.rootPassword) {
        // Use Contabo's generated password
        finalPassword = providerResponse.rootPassword;
        console.log(
          `🔑 Using Contabo's generated password for VPS ${subOrder.identifiant}`
        );
      } else {
        // Generate our own password as fallback
        finalPassword = this.generateSecurePassword();
        console.log(
          `🔑 Generated fallback password for VPS ${subOrder.identifiant}`
        );
      }

      subOrder.vps.rootPassword = finalPassword;
      subOrder.vps.status = VPSStatus.ACTIVE;
      subOrder.vps.activatedAt = new Date();
      subOrder.status = OrderStatus.ACTIVE;

      await subOrder.save();

      // Create VPS instance record for management
      const vpsInstance = await this.createVPSInstance(
        order,
        subOrder,
        providerResponse
      );

      return vpsInstance;
    } catch (error) {
      console.error("Failed to provision VPS:", error.message);

      // Update suborder with error status
      subOrder.vps.status = VPSStatus.FAILED;
      subOrder.status = OrderStatus.FAILED;
      await subOrder.save();

      throw error;
    }
  }

  /**
   * Get user's VPS orders
   * @param {string} userId - User ID
   * @param {string} status - Optional status filter
   * @returns {Promise<Array>} User's VPS orders
   */
  async getUserVPSOrders(userId, status = null) {
    try {
      const query = { user: userId };
      if (status) {
        query.status = status;
      }

      const orders = await Order.find(query)
        .populate({
          path: "subOrders",
          match: { vps: { $ne: null } }, // Only suborders with VPS configuration
          populate: {
            path: "package",
            model: "Package",
          },
        })
        .populate("user", "firstName lastName email")
        .sort({ createdAt: -1 });

      // Filter out orders that don't have VPS suborders
      return orders.filter(
        (order) => order.subOrders && order.subOrders.length > 0
      );
    } catch (error) {
      console.error("Failed to fetch user VPS orders:", error.message);
      throw error;
    }
  }

  /**
   * Get user's VPS instances from local database
   * @param {string} userId - User ID
   * @param {string} status - Optional status filter
   * @returns {Promise<Array>} User's VPS instances
   */
  async getUserVPSInstances(userId, status = null) {
    try {
      return await VPSInstance.findByUser(userId, status);
    } catch (error) {
      console.error("Failed to fetch user VPS instances:", error.message);
      throw error;
    }
  }

  /**
   * Get user's VPS instances directly from Contabo API
   * @param {string} userId - User ID (not used by Contabo, but kept for consistency)
   * @param {string} status - Optional status filter
   * @returns {Promise<Array>} User's VPS instances from Contabo
   */
  async getContaboVPSInstances(userId, status = null) {
    try {
      console.log(`🔍 RÉCUPÉRATION VPS CONTABO (timeout 60s)`);

      // Cache simple pour éviter les appels répétés
      const cacheKey = `contabo_vps_${userId}_${status || 'all'}`;
      if (this.vpsCache && this.vpsCache[cacheKey]) {
        const cached = this.vpsCache[cacheKey];
        const now = Date.now();
        // Cache valide pendant 30 secondes seulement (pour les tests)
        if (now - cached.timestamp < 30 * 1000) {
          console.log(`📦 Utilisation du cache (${cached.data.length} VPS) - Age: ${Math.round((now - cached.timestamp)/1000)}s`);
          return cached.data;
        } else {
          console.log(`🗑️ Cache expiré (${Math.round((now - cached.timestamp)/1000)}s), nouvelle requête API`);
        }
      }

      // EXACTEMENT comme dans le script de test
      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      console.log('🔐 Authentification Contabo...');
      await contaboProvider.authenticate();
      console.log('✅ Authentification réussie !');

      console.log('📡 Récupération des VPS instances...');
      const vpsInstances = await contaboProvider.getCustomerVPS('test-user');

      console.log(`🎉 ${vpsInstances.length} VPS trouvé(s)`);

      // Afficher les VPS trouvés
      vpsInstances.forEach((vps, index) => {
        console.log(`   ${index + 1}. ${vps.name} (${vps.id}) - ${vps.status} - ${vps.ip}`);
      });

      // Sauvegarder en cache
      if (!this.vpsCache) this.vpsCache = {};
      this.vpsCache[cacheKey] = {
        data: vpsInstances,
        timestamp: Date.now()
      };

      return vpsInstances;

    } catch (error) {
      console.error("❌ Erreur Contabo API:", error.message);
      throw error;
    }
  }

  /**
   * Control VPS instance (start, stop, restart, reset-password)
   * @param {string} instanceId - Instance ID
   * @param {string} action - Action to perform
   * @param {string} userId - User ID for authorization
   * @returns {Promise<Object>} Operation result
   */
  async controlVPSInstance(instanceId, action, userId) {
    try {
      const instance = await VPSInstance.findOne({ instanceId, user: userId });
      if (!instance) {
        throw new Error("VPS instance not found or access denied");
      }

      const provider = this.getProvider(instance.config.provider);
      let result;

      switch (action) {
        case "start":
          result = await provider.startVPS(instanceId);
          await instance.updateStatus("running");
          break;
        case "stop":
          result = await provider.stopVPS(instanceId);
          await instance.updateStatus("stopped");
          break;
        case "restart":
          result = await provider.restartVPS(instanceId);
          await instance.updateStatus("restarting");
          // Status will be updated to 'running' after restart completes
          setTimeout(async () => {
            try {
              await instance.updateStatus("running");
            } catch (error) {
              console.error(
                "Failed to update status after restart:",
                error.message
              );
            }
          }, 30000); // Wait 30 seconds before updating to running
          break;
        case "reset-password":
          result = await provider.resetPassword(instanceId);
          // Update the stored password if provided
          if (result.newPassword) {
            instance.access.rootPassword = result.newPassword;
            await instance.save();
          }
          break;
        default:
          throw new Error("Invalid action");
      }

      return result;
    } catch (error) {
      console.error(`Failed to ${action} VPS instance:`, error.message);
      throw error;
    }
  }

  /**
   * Create VPS instance record
   * @param {Object} order - Main order
   * @param {Object} subOrder - SubOrder with VPS configuration
   * @param {Object} providerResponse - Provider response
   * @returns {Promise<Object>} VPS instance
   */
  async createVPSInstance(order, subOrder, providerResponse) {
    try {
      const vpsInstance = new VPSInstance({
        instanceId: providerResponse.id,
        order: order._id,
        user: order.user,
        config: {
          name: subOrder.vps.displayName,
          provider: subOrder.vps.provider,
          planId: subOrder.vps.planId,
          region: subOrder.vps.region,
          operatingSystem: subOrder.vps.operatingSystem,
          specifications: subOrder.vps.specifications,
        },
        network: {
          ipv4: providerResponse.ip,
          ipv6: providerResponse.ipv6,
          hostname: `${subOrder.vps.displayName.toLowerCase()}.${
            subOrder.vps.provider
          }.com`,
        },
        access: {
          rootPassword: subOrder.vps.rootPassword,
          sshKeys: subOrder.vps.sshKeys || [],
        },
        status: "running",
        providerData: {
          providerInstanceId: providerResponse.id,
          providerStatus: providerResponse.status,
          providerRegion: subOrder.vps.region,
          additionalData: providerResponse.raw,
        },
        billing: {
          monthlyPrice: subOrder.price / subOrder.period,
          currency: order.currency || "MAD",
          billingCycle: subOrder.period === 12 ? "yearly" : "monthly",
          nextBillingDate: this.calculateNextBillingDate(
            subOrder.period === 12 ? "yearly" : "monthly"
          ),
        },
      });

      return await vpsInstance.save();
    } catch (error) {
      console.error("Failed to create VPS instance record:", error.message);
      throw error;
    }
  }

  /**
   * Helper methods
   */
  calculateOrderTotal(planDetails, billingCycle) {
    switch (billingCycle) {
      case "hourly":
        return planDetails.price.hourly * 24 * 30;
      case "yearly":
        return planDetails.price.monthly * 12;
      default:
        return planDetails.price.monthly;
    }
  }

  getOSImageId(operatingSystem) {
    const osMap = {
      "ubuntu-22.04": "ubuntu-22.04",
      "ubuntu-20.04": "ubuntu-20.04",
      "debian-11": "debian-11",
      "centos-8": "centos-8",
    };
    return osMap[operatingSystem] || "ubuntu-22.04";
  }

  /**
   * Validate password meets Contabo requirements
   * @param {string} password - Password to validate
   * @returns {boolean} - True if valid
   */
  validateContaboPassword(password) {
    if (!password || typeof password !== "string") return false;

    // Check length (8-30 characters)
    if (password.length < 8 || password.length > 30) return false;

    // Check alphanumeric only (no special characters)
    const alphanumericRegex = /^[a-zA-Z0-9]+$/;
    return alphanumericRegex.test(password);
  }

  generateSecurePassword() {
    // Contabo requirements: alphanumeric only, 8-30 characters
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";

    // Generate 16 character password (within 8-30 range)
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return password;
  }

  calculateNextBillingDate(billingCycle) {
    const now = new Date();
    switch (billingCycle) {
      case "hourly":
        return new Date(now.getTime() + 60 * 60 * 1000);
      case "yearly":
        return new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
      default:
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
    }
  }

  /**
   * Execute VPS action (start, stop, restart, console)
   * @param {string} instanceId - VPS instance ID
   * @param {string} action - Action to perform
   * @returns {Promise<Object>} Action result
   */
  async executeVPSAction(instanceId, action) {
    try {
      console.log(`🎯 Executing VPS action: ${action} on instance ${instanceId}`);

      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      let result;

      switch (action.toLowerCase()) {
        case 'start':
          result = await contaboProvider.startVPS(instanceId);
          break;

        case 'stop':
          result = await contaboProvider.stopVPS(instanceId);
          break;

        case 'restart':
          result = await contaboProvider.restartVPS(instanceId);
          break;





        case 'cloud-init':
          // Pour cloud-init, on toggle l'état (on assume qu'il était désactivé)
          result = await contaboProvider.toggleCloudInit(instanceId, true);
          break;
        case 'rescue':
          result = await contaboProvider.startRescueSystem(instanceId);
          break;

        default:
          throw new Error(`Unsupported action: ${action}`);
      }

      console.log(`✅ VPS action ${action} completed successfully for instance ${instanceId}`);

      // Vider le cache après une action pour forcer le rafraîchissement
      console.log(`🗑️ Clearing VPS cache after ${action} action`);
      this.vpsCache = {};

      return result;

    } catch (error) {
      console.error(`❌ VPS action ${action} failed for instance ${instanceId}:`, error.message);
      throw error;
    }
  }

  /**
   * Get VPS snapshots with quota information
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Snapshots data with quota info
   */
  async getVPSSnapshots(instanceId) {
    try {
      console.log(`📸 Getting snapshots for VPS instance: ${instanceId}`);

      const contaboProvider = this.getProvider('contabo');
      const snapshotInfo = await contaboProvider.getVPSSnapshots(instanceId);

      console.log(`✅ Found ${snapshotInfo.snapshots.length}/${snapshotInfo.quota.max} snapshots for instance ${instanceId}`);
      return snapshotInfo;
    } catch (error) {
      console.error(`❌ Failed to get VPS snapshots for instance ${instanceId}:`, error.message);
      throw error;
    }
  }

  /**
   * Create VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} name - Snapshot name
   * @param {string} description - Snapshot description
   * @returns {Promise<Object>} Created snapshot
   */
  async createVPSSnapshot(instanceId, name, description) {
    try {
      console.log(`📸 Creating snapshot for VPS instance: ${instanceId}`);

      const contaboProvider = this.getProvider('contabo');
      const snapshot = await contaboProvider.createSnapshot(instanceId, name, description);

      // Vider le cache après création
      console.log(`🗑️ Clearing VPS cache after snapshot creation`);
      this.vpsCache = {};

      console.log(`✅ Snapshot created successfully for instance ${instanceId}`);
      return snapshot;
    } catch (error) {
      console.error(`❌ Failed to create VPS snapshot for instance ${instanceId}:`, error.message);
      throw error;
    }
  }

  /**
   * Rename VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @param {string} name - New snapshot name
   * @param {string} description - New snapshot description
   * @returns {Promise<Object>} Updated snapshot
   */
  async renameVPSSnapshot(instanceId, snapshotId, name, description) {
    try {
      console.log(`✏️ Renaming snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const contaboProvider = this.getProvider('contabo');
      const snapshot = await contaboProvider.renameSnapshot(instanceId, snapshotId, name, description);

      console.log(`✅ Snapshot renamed successfully`);
      return snapshot;
    } catch (error) {
      console.error(`❌ Failed to rename VPS snapshot:`, error.message);
      throw error;
    }
  }

  /**
   * Rollback VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise<Object>} Rollback result
   */
  async rollbackVPSSnapshot(instanceId, snapshotId) {
    try {
      console.log(`🔄 Rolling back snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const contaboProvider = this.getProvider('contabo');
      const result = await contaboProvider.rollbackSnapshot(instanceId, snapshotId);

      // Vider le cache après rollback
      console.log(`🗑️ Clearing VPS cache after snapshot rollback`);
      this.vpsCache = {};

      console.log(`✅ Snapshot rollback initiated successfully`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to rollback VPS snapshot:`, error.message);
      throw error;
    }
  }

  /**
   * Delete VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteVPSSnapshot(instanceId, snapshotId) {
    try {
      console.log(`🗑️ Deleting snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const contaboProvider = this.getProvider('contabo');
      const result = await contaboProvider.deleteSnapshot(instanceId, snapshotId);

      console.log(`✅ Snapshot deleted successfully`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to delete VPS snapshot:`, error.message);
      throw error;
    }
  }

  /**
   * Get Cloud-Init status for a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Cloud-Init status
   */
  async getCloudInitStatus(instanceId) {
    try {
      console.log(`☁️ Getting Cloud-Init status for VPS: ${instanceId}`);

      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      const result = await contaboProvider.getCloudInitStatus(instanceId);

      console.log(`✅ Cloud-Init status retrieved for VPS ${instanceId}`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to get Cloud-Init status:`, error.message);
      throw error;
    }
  }

  /**
   * Reinstall VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} reinstallData - Reinstallation parameters
   * @returns {Promise<Object>} Reinstallation result
   */
  async reinstallVPS(instanceId, reinstallData) {
    try {
      console.log(`🔄 Reinstalling VPS: ${instanceId}`);

      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      const result = await contaboProvider.reinstallVPS(instanceId, reinstallData);

      console.log(`✅ VPS reinstallation started for ${instanceId}`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to reinstall VPS:`, error.message);
      throw error;
    }
  }

  /**
   * Get available images for VPS installation
   * @returns {Promise<Object>} Available images
   */
  async getAvailableImages() {
    try {
      console.log(`📋 Getting available images`);

      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      const result = await contaboProvider.getAvailableImages();

      console.log(`✅ Available images retrieved`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to get available images:`, error.message);
      throw error;
    }
  }

  /**
   * Get available applications for VPS installation
   * @returns {Promise<Object>} Available applications
   */
  async getAvailableApplications() {
    try {
      console.log(`📱 Getting available applications`);

      const ContaboProvider = require('./providers/ContaboProvider');
      const contaboProvider = new ContaboProvider();

      const result = await contaboProvider.getAvailableApplications();

      console.log(`✅ Available applications retrieved`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to get available applications:`, error.message);
      throw error;
    }
  }


}

module.exports = VPSService;
