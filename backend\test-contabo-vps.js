/**
 * Script de test pour lister les VPS Contabo
 * Utilise directement l'API Contabo pour récupérer les instances
 */

require('dotenv').config();
const ContaboProvider = require('./services/providers/ContaboProvider');

async function testContaboVPS() {
  console.log('🔍 Test de récupération des VPS Contabo...\n');
  
  // Afficher les credentials (masqués pour sécurité)
  console.log('📋 Configuration Contabo:');
  console.log(`   API URL: ${process.env.CONTABO_API_URL}`);
  console.log(`   Client ID: ${process.env.CONTABO_CLIENT_ID}`);
  console.log(`   Username: ${process.env.CONTABO_USERNAME}`);
  console.log(`   Client Secret: ${process.env.CONTABO_CLIENT_SECRET ? '***masqué***' : 'NON DÉFINI'}`);
  console.log(`   Password: ${process.env.CONTABO_PASSWORD ? '***masqué***' : 'NON DÉFINI'}\n`);

  try {
    // Créer une instance du provider Contabo
    const contaboProvider = new ContaboProvider();
    
    console.log('🔐 Tentative d\'authentification...');
    
    // Tester l'authentification d'abord
    await contaboProvider.authenticate();
    console.log('✅ Authentification réussie !\n');
    
    console.log('📡 Récupération des VPS instances...');
    
    // Récupérer les VPS
    const vpsInstances = await contaboProvider.getCustomerVPS('test-user');
    
    console.log(`\n🎉 RÉSULTATS : ${vpsInstances.length} VPS trouvé(s)\n`);
    
    if (vpsInstances.length === 0) {
      console.log('❌ Aucun VPS trouvé sur ce compte Contabo');
      console.log('💡 Vérifiez que vous avez des VPS actifs sur votre compte Contabo\n');
    } else {
      console.log('📋 LISTE DES VPS :');
      console.log('=' .repeat(80));
      
      vpsInstances.forEach((vps, index) => {
        console.log(`\n${index + 1}. VPS Instance:`);
        console.log(`   🆔 ID: ${vps.id || vps.instanceId || 'N/A'}`);
        console.log(`   📛 Nom: ${vps.name || vps.displayName || 'N/A'}`);
        console.log(`   🔄 Statut: ${vps.status || 'N/A'}`);
        console.log(`   🌍 Région: ${vps.region || 'N/A'}`);
        console.log(`   💻 Plan: ${vps.plan || vps.productId || 'N/A'}`);
        console.log(`   🌐 IP: ${vps.ipv4 || vps.ip || 'N/A'}`);
        console.log(`   📅 Créé: ${vps.createdAt || vps.created || 'N/A'}`);
        
        // Afficher toutes les propriétés disponibles
        console.log(`   📊 Données complètes:`);
        console.log('   ', JSON.stringify(vps, null, 4));
        console.log('   ' + '-'.repeat(60));
      });
    }
    
  } catch (error) {
    console.error('\n❌ ERREUR lors de la récupération des VPS:');
    console.error(`   Message: ${error.message}`);
    
    if (error.response) {
      console.error(`   Status HTTP: ${error.response.status}`);
      console.error(`   Réponse API: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    if (error.code === 'ENOTFOUND') {
      console.error('   🌐 Problème de réseau - Vérifiez votre connexion internet');
    } else if (error.response?.status === 401) {
      console.error('   🔐 Problème d\'authentification - Vérifiez vos credentials Contabo');
    } else if (error.response?.status === 403) {
      console.error('   🚫 Accès refusé - Vérifiez les permissions de votre compte Contabo');
    }
    
    console.error('\n💡 SOLUTIONS POSSIBLES:');
    console.error('   1. Vérifiez vos credentials Contabo dans le fichier .env');
    console.error('   2. Vérifiez que votre compte Contabo a accès à l\'API');
    console.error('   3. Vérifiez votre connexion internet');
    console.error('   4. Contactez le support Contabo si le problème persiste');
  }
}

// Exécuter le test
console.log('🚀 DÉMARRAGE DU TEST CONTABO VPS');
console.log('=' .repeat(50));
testContaboVPS()
  .then(() => {
    console.log('\n✅ Test terminé');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Erreur fatale:', error.message);
    process.exit(1);
  });
