const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Package = require('../models/Package');
const Category = require('../models/Category');
const Brand = require('../models/Brand');
const Specification = require('../models/Specification');

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech';
    console.log('Connecting to MongoDB:', mongoURI);
    
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Check VPS packages
const checkVPSPackages = async () => {
  try {
    console.log('🔍 Checking VPS packages...\n');

    // 1. Check VPS category
    const vpsCategory = await Category.findOne({ name: 'VPS' });
    if (vpsCategory) {
      console.log('✅ VPS Category found:', {
        id: vpsCategory._id,
        name: vpsCategory.name,
        description: vpsCategory.description
      });
    } else {
      console.log('❌ VPS Category not found');
    }

    // 2. Check VPS brand
    const vpsBrand = await Brand.findOne({ name: 'VPS Hosting' });
    if (vpsBrand) {
      console.log('✅ VPS Brand found:', {
        id: vpsBrand._id,
        name: vpsBrand.name,
        packagesCount: vpsBrand.packages?.length || 0
      });
    } else {
      console.log('❌ VPS Brand not found');
    }

    // 3. Check VPS packages
    const vpsPackages = await Package.find({ 
      brand: vpsBrand?._id 
    }).populate('category').populate('brand').populate('specifications');

    console.log(`\n📦 Found ${vpsPackages.length} VPS packages:`);
    
    if (vpsPackages.length === 0) {
      console.log('❌ No VPS packages found');
    } else {
      vpsPackages.forEach((pkg, index) => {
        console.log(`\n${index + 1}. ${pkg.name}`);
        console.log(`   ID: ${pkg._id}`);
        console.log(`   Reference: ${pkg.reference}`);
        console.log(`   Price: ${pkg.price} MAD`);
        console.log(`   Status: ${pkg.status}`);
        console.log(`   Category: ${pkg.category?.name}`);
        console.log(`   Brand: ${pkg.brand?.name}`);
        console.log(`   Specifications: ${pkg.specifications?.length || 0}`);
        console.log(`   VPS Config:`, pkg.vpsConfig);
      });
    }

    // 4. Check all packages (to see if any exist)
    const allPackages = await Package.find({}).populate('brand');
    console.log(`\n📊 Total packages in database: ${allPackages.length}`);
    
    if (allPackages.length > 0) {
      console.log('\nAll packages:');
      allPackages.forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.name} (${pkg.brand?.name || 'No Brand'}) - ${pkg.price} MAD`);
      });
    }

    // 5. Check specifications
    const allSpecs = await Specification.find({});
    console.log(`\n📋 Total specifications in database: ${allSpecs.length}`);

    return {
      category: vpsCategory,
      brand: vpsBrand,
      packages: vpsPackages,
      totalPackages: allPackages.length,
      totalSpecs: allSpecs.length
    };

  } catch (error) {
    console.error('❌ Error checking VPS packages:', error);
    throw error;
  }
};

// Run the script
const run = async () => {
  try {
    await connectDB();
    const result = await checkVPSPackages();
    
    console.log('\n📈 Summary:');
    console.log(`- VPS Category: ${result.category ? '✅ Found' : '❌ Missing'}`);
    console.log(`- VPS Brand: ${result.brand ? '✅ Found' : '❌ Missing'}`);
    console.log(`- VPS Packages: ${result.packages.length}`);
    console.log(`- Total Packages: ${result.totalPackages}`);
    console.log(`- Total Specifications: ${result.totalSpecs}`);
    
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed.');
  }
};

// Execute if run directly
if (require.main === module) {
  run();
}

module.exports = { checkVPSPackages };
