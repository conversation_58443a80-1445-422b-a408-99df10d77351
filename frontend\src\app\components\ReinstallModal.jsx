'use client';

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, HardDrive, Key, Settings, Info, Trash2, Eye, EyeOff, RefreshCw, Upload } from 'lucide-react';
import vpsService from '../services/vpsService';
import customImageService from '../services/customImageService';

const ReinstallModal = ({ 
  isOpen, 
  onClose, 
  server, 
  onReinstallSuccess 
}) => {
  const [formData, setFormData] = useState({
    imageId: '',
    password: '',
    userData: '',
    sshKeys: [],
    installationType: 'standard', // 'standard' ou 'advanced'
    advancedImageType: 'standard', // 'standard' ou 'custom' (pour l'installation avancée)
    selectedApplication: '', // Application sélectionnée
    adminPassword: '',
    enableRootUser: false,
    publicSshKey: '',
    cloudInitTemplate: '',
    customScript: '',
    // Custom Image fields
    customImageId: '', // ID of selected custom image
    customImageUrl: '', // URL for creating new custom image
    customImageName: '',
    customImageOsType: 'Linux',
    customImageVersion: '',
    customImageDescription: ''
  });

  const [availableImages, setAvailableImages] = useState([]);
  const [availableApplications, setAvailableApplications] = useState([]);
  const [customImages, setCustomImages] = useState([]);
  const [loadingImages, setLoadingImages] = useState(false);
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAdminPassword, setShowAdminPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isGeneratingPassword, setIsGeneratingPassword] = useState(false);
  const [showCustomImageModal, setShowCustomImageModal] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchAvailableImages();
      fetchAvailableApplications();
      fetchCustomImages();
      setErrors({});
      setShowPassword(false);
      setShowCustomImageModal(false); // Reset modal state
    }
  }, [isOpen]);

  // Debug log for modal state
  useEffect(() => {
    console.log('🔍 Custom Image Modal State:', showCustomImageModal);
  }, [showCustomImageModal]);

  // Fonction pour générer un mot de passe sécurisé (alphanumeric seulement)
  const generateSecurePassword = () => {
    setIsGeneratingPassword(true);

    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';

    // Pas de caractères spéciaux pour éviter les problèmes SSH
    const allChars = lowercase + uppercase + numbers;
    let password = '';

    // Assurer au moins un caractère de chaque type
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];

    // Compléter avec des caractères aléatoires (12 caractères total)
    for (let i = 3; i < 12; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Mélanger le mot de passe
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    setTimeout(() => {
      setFormData(prev => ({ ...prev, password }));
      setIsGeneratingPassword(false);
      setShowPassword(true);
    }, 500);
  };

  // Fonction pour générer une clé SSH publique d'exemple (simulation)
  const generateExampleSSHKey = () => {
    const keyTypes = ['ssh-rsa', 'ssh-ed25519'];
    const keyType = keyTypes[Math.floor(Math.random() * keyTypes.length)];
    const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const username = 'user';
    const hostname = 'localhost';

    if (keyType === 'ssh-ed25519') {
      return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} ${username}@${hostname}`;
    } else {
      return `ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ${randomString} ${username}@${hostname}`;
    }
  };

  // Fonction pour évaluer la force du mot de passe (alphanumeric)
  const getPasswordStrength = (password) => {
    if (!password) return { percentage: 0, label: '', color: 'bg-gray-300' };

    let score = 0;

    // Longueur (plus important pour les mots de passe alphanumériques)
    if (password.length >= 8) score += 2;
    if (password.length >= 10) score += 1;
    if (password.length >= 12) score += 1;

    // Complexité (alphanumeric seulement)
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;

    // Évaluation finale adaptée pour alphanumeric
    if (score <= 3) {
      return { percentage: 25, label: 'Faible', color: 'bg-red-500' };
    } else if (score <= 5) {
      return { percentage: 60, label: 'Bon', color: 'bg-yellow-500' };
    } else if (score <= 6) {
      return { percentage: 85, label: 'Fort', color: 'bg-blue-500' };
    } else {
      return { percentage: 100, label: 'Excellent', color: 'bg-green-500' };
    }
  };

  // Fonction pour récupérer les images disponibles
  const fetchAvailableImages = async () => {
    try {
      setLoadingImages(true);
      console.log('🖼️ Fetching available images...');

      const response = await vpsService.getAvailableImages();

      if (response.data.success && response.data.data) {
        const allImages = response.data.data;
        console.log(`✅ Retrieved ${allImages.length} images:`, allImages);

        // Filter out Windows images that are incompatible with Linux VPS
        const compatibleImages = allImages.filter(img => {
          const name = img.name.toLowerCase();
          const osType = (img.osType || '').toLowerCase();
          
          // Exclude Windows images
          if (osType === 'windows' || name.includes('windows')) {
            console.log(`🚫 Filtering out Windows image: ${img.name}`);
            return false;
          }
          
          // Include Linux images
          return true;
        });

        console.log(`✅ Compatible images after filtering: ${compatibleImages.length}`);
        setAvailableImages(compatibleImages);

        // Sélectionner la première image compatible par défaut
        if (compatibleImages.length > 0) {
          setFormData(prev => ({
            ...prev,
            imageId: compatibleImages[0].imageId || compatibleImages[0].id || ''
          }));
        }
      } else {
        console.error('❌ Failed to fetch images:', response.data.message);
        // Fallback vers des images par défaut en cas d'erreur
        const fallbackImages = [
          {
            imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb',
            name: 'Ubuntu 22.04 LTS',
            osType: 'Linux',
            description: 'Ubuntu 22.04 LTS Jammy Jellyfish'
          }
        ];
        setAvailableImages(fallbackImages);
        setFormData(prev => ({
          ...prev,
          imageId: fallbackImages[0].imageId
        }));
      }
    } catch (error) {
      console.error('❌ Error fetching images:', error);
      // Fallback vers des images par défaut en cas d'erreur
      const fallbackImages = [
        {
          imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb',
          name: 'Ubuntu 22.04 LTS',
          osType: 'Linux',
          description: 'Ubuntu 22.04 LTS Jammy Jellyfish'
        }
      ];
      setAvailableImages(fallbackImages);
      setFormData(prev => ({
        ...prev,
        imageId: fallbackImages[0].imageId
      }));
    } finally {
      setLoadingImages(false);
    }
  };

  // Fonction pour récupérer les applications disponibles
  const fetchAvailableApplications = async () => {
    try {
      setLoadingApplications(true);
      console.log('📦 Fetching available applications...');

      const response = await vpsService.getAvailableApplications();

      if (response.data.success && response.data.data) {
        const applications = response.data.data;
        console.log(`✅ Retrieved ${applications.length} applications:`, applications);
        setAvailableApplications(applications);
      } else {
        console.error('❌ Failed to fetch applications:', response.data.message);
        setAvailableApplications([]);
      }
    } catch (error) {
      console.error('❌ Error fetching applications:', error);
      setAvailableApplications([]);
    } finally {
      setLoadingApplications(false);
    }
  };

  // Fonction pour récupérer les custom images
  const fetchCustomImages = async () => {
    try {
      console.log('🖼️ Fetching custom images...');
      console.log('🔍 customImageService:', customImageService);

      const response = await customImageService.getCustomImages();
      console.log('🔍 Custom images response:', response);

      if (response.success) {
        console.log(`✅ Retrieved ${response.data.length} custom images`);
        setCustomImages(response.data);
      } else {
        console.error('❌ Failed to fetch custom images:', response.message);
        setCustomImages([]);
      }
    } catch (error) {
      console.error('❌ Error fetching custom images:', error);
      console.error('❌ Error details:', error.message, error.stack);
      setCustomImages([]);
    }
  };

  // Fonction de validation
  const validateForm = () => {
    const newErrors = {};

    // Validation de l'image
    if (!formData.imageId && formData.installationType === 'standard') {
      newErrors.imageId = 'Veuillez sélectionner un système d\'exploitation';
    }

    // Validation selon le type d'installation
    if (formData.installationType === 'standard') {
      // Installation standard : mot de passe requis
      if (!formData.password) {
        newErrors.password = 'Le mot de passe est requis';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
      }
    } else {
      // Installation avancée : mot de passe OU clé SSH requis
      if (!formData.adminPassword && !formData.publicSshKey) {
        newErrors.access = 'Password or public SSH-Keys must be set in order to access the VPS.';
      }

      if (formData.adminPassword && formData.adminPassword.length < 8) {
        newErrors.adminPassword = 'Le mot de passe doit contenir au moins 8 caractères';
      }

      // Validation Custom Image
      if (formData.advancedImageType === 'custom' && !formData.customImageId) {
        newErrors.customImage = 'Custom image is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Fonction de soumission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔄 Starting VPS reinstallation...');

      // Préparer les données de réinstallation
      const reinstallData = {
        imageId: formData.imageId
      };

      // Données selon le type d'installation
      if (formData.installationType === 'standard') {
        reinstallData.password = formData.password;
        reinstallData.enableRootUser = true; // Force enable root user for password authentication

        // Add selected application if any
        if (formData.selectedApplication) {
          reinstallData.selectedApplication = formData.selectedApplication;
        }

        // Add Cloud-Init configuration to enable SSH password authentication
        reinstallData.userData = `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${formData.password}
chpasswd:
  expire: false

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Password authentication enabled for standard installation" >> /root/install.log`;
      } else {
        // Installation avancée
        if (formData.adminPassword) {
          reinstallData.password = formData.adminPassword;
        }

        if (formData.publicSshKey) {
          reinstallData.sshKeys = [formData.publicSshKey];
        }

        if (formData.enableRootUser) {
          reinstallData.enableRootUser = true;
        }

        if (formData.userData) {
          reinstallData.userData = formData.userData;
        }

        if (formData.customScript) {
          reinstallData.customScript = formData.customScript;
        }

        if (formData.cloudInitTemplate) {
          reinstallData.cloudInitTemplate = formData.cloudInitTemplate;
        }

        // Custom Image handling
        if (formData.advancedImageType === 'custom') {
          reinstallData.customImageId = formData.customImageId;
        }
      }

      console.log('📤 Reinstall data:', reinstallData);

      // Appeler l'API de réinstallation
      const response = await vpsService.reinstallVPS(server.id, reinstallData);

      if (response.data.success) {
        console.log('✅ VPS reinstallation started successfully');

        // Appeler le callback de succès si fourni
        if (onReinstallSuccess) {
          onReinstallSuccess(response.data);
        }

        // Fermer le modal
        onClose();
      } else {
        throw new Error(response.data.message || 'Failed to start VPS reinstallation');
      }

    } catch (error) {
      console.error('❌ VPS reinstallation failed:', error);
      setErrors({
        submit: error.message || 'Une erreur est survenue lors de la réinstallation'
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center">
              <AlertTriangle className="w-6 h-6 text-orange-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">
                Réinstaller le VPS {server?.name || server?.id}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6">
            <div className="bg-orange-50 border border-orange-200 rounded-md p-4 mb-6">
              <div className="flex">
                <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-orange-800">
                    Attention : Réinstallation du VPS
                  </h3>
                  <div className="mt-2 text-sm text-orange-700">
                    <p>
                      Cette action va <strong>effacer complètement</strong> toutes les données présentes sur le VPS et installer un nouveau système d'exploitation.
                    </p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Tous les fichiers et configurations seront perdus</li>
                      <li>Le VPS sera temporairement indisponible pendant l'installation</li>
                      <li>Cette action est <strong>irréversible</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Type d'installation */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Type d'installation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Installation standard */}
                  <div className="relative">
                    <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                      formData.installationType === 'standard' 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        name="installationType"
                        value="standard"
                        checked={formData.installationType === 'standard'}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          installationType: e.target.value 
                        }))}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <HardDrive className="w-5 h-5 text-blue-600 mr-2" />
                        <span className="font-medium">Installation standard</span>
                      </div>
                    </label>
                  </div>

                  {/* Installation avancée */}
                  <div className="relative">
                    <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                      formData.installationType === 'advanced' 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        name="installationType"
                        value="advanced"
                        checked={formData.installationType === 'advanced'}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          installationType: e.target.value 
                        }))}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <Settings className="w-5 h-5 text-purple-600 mr-2" />
                        <span className="font-medium">Installation avancée/Image personnalisée</span>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Installation avancée */}
              {formData.installationType === 'advanced' && (
                <>
                  {/* Choix entre Standard Image et Custom Image */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Type d'image
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      {/* Standard Image Option */}
                      <div className="relative">
                        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                          formData.advancedImageType === 'standard'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="advancedImageType"
                            value="standard"
                            checked={formData.advancedImageType === 'standard'}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              advancedImageType: e.target.value
                            }))}
                            className="mr-3"
                          />
                          <div className="flex items-center">
                            <HardDrive className="w-5 h-5 text-blue-600 mr-2" />
                            <span className="font-medium">Standard Image</span>
                          </div>
                        </label>
                      </div>

                      {/* Custom Image Option */}
                      <div className="relative">
                        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                          formData.advancedImageType === 'custom'
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="advancedImageType"
                            value="custom"
                            checked={formData.advancedImageType === 'custom'}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              advancedImageType: e.target.value
                            }))}
                            className="mr-3"
                          />
                          <div className="flex items-center">
                            <Upload className="w-5 h-5 text-purple-600 mr-2" />
                            <span className="font-medium">Custom Image</span>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Standard Image Section */}
                  {formData.advancedImageType === 'standard' && (
                    <div className="space-y-6">
                      {/* Standard Image Selector */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Standard Image
                        </label>
                        {loadingImages ? (
                          <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                            <span className="text-gray-600">Chargement des images...</span>
                          </div>
                        ) : (
                          <select
                            value={formData.imageId}
                            onChange={(e) => setFormData(prev => ({ ...prev, imageId: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={isLoading}
                          >
                            <option value="">Sélectionner un système d'exploitation</option>
                            {availableImages.map((image) => (
                              <option key={image.imageId || image.id} value={image.imageId || image.id}>
                                {image.name} {image.version ? `(${image.version})` : ''}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>

                      {/* Admin Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Root Password' : 'Admin Password'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title={formData.enableRootUser ? "Mot de passe pour l'utilisateur root" : "Mot de passe pour l'utilisateur admin"} />
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? "text" : "password"}
                            value={formData.adminPassword}
                            onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24"
                            placeholder="Select or create new password"
                            disabled={isLoading}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center">
                            <button
                              type="button"
                              onClick={() => {
                                setIsGeneratingPassword(true);
                                const lowercase = 'abcdefghijklmnopqrstuvwxyz';
                                const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                                const numbers = '0123456789';
                                const allChars = lowercase + uppercase + numbers;
                                let password = '';

                                password += lowercase[Math.floor(Math.random() * lowercase.length)];
                                password += uppercase[Math.floor(Math.random() * uppercase.length)];
                                password += numbers[Math.floor(Math.random() * numbers.length)];

                                for (let i = 3; i < 12; i++) {
                                  password += allChars[Math.floor(Math.random() * allChars.length)];
                                }

                                password = password.split('').sort(() => Math.random() - 0.5).join('');

                                setTimeout(() => {
                                  setFormData(prev => ({ ...prev, adminPassword: password }));
                                  setIsGeneratingPassword(false);
                                  setShowPassword(true);
                                }, 500);
                              }}
                              className="px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors"
                              title="Générer un mot de passe sécurisé"
                              disabled={isLoading || isGeneratingPassword}
                            >
                              <RefreshCw className={`w-3 h-3 ${isGeneratingPassword ? 'animate-spin' : ''}`} />
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="px-2 py-1 text-gray-500 hover:text-gray-700"
                              title={showPassword ? "Masquer le mot de passe" : "Afficher le mot de passe"}
                              disabled={isLoading}
                            >
                              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                            {formData.adminPassword && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, adminPassword: '' }))}
                                className="px-2 py-1 text-gray-400 hover:text-red-500 rounded-r"
                                title="Supprimer le mot de passe"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Enable Root User */}
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.enableRootUser}
                            onChange={(e) => setFormData(prev => ({ ...prev, enableRootUser: e.target.checked }))}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            disabled={isLoading}
                          />
                          <span className="text-sm font-medium text-gray-700">
                            Enable Root User
                            <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Activer l'utilisateur root" />
                          </span>
                        </label>
                      </div>

                      {/* Public SSH-Key */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Public SSH-Key for User Root' : 'Public SSH-Key for User Admin'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Clé SSH publique pour l'accès" />
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.publicSshKey}
                            onChange={(e) => setFormData(prev => ({ ...prev, publicSshKey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20"
                            rows={3}
                            placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname"
                            disabled={isLoading}
                          />
                          <div className="absolute top-2 right-2 flex flex-col space-y-1">
                            <button
                              type="button"
                              onClick={() => {
                                const key = generateExampleSSHKey();
                                setFormData(prev => ({ ...prev, publicSshKey: key }));
                              }}
                              className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                              title="Générer une clé SSH d'exemple"
                              disabled={isLoading}
                            >
                              <Key className="w-3 h-3" />
                            </button>
                            {formData.publicSshKey && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, publicSshKey: '' }))}
                                className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                title="Supprimer la clé SSH"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Cloud-Init */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Cloud-Init
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Template Cloud-Init pour la configuration automatique" />
                        </label>
                        <div className="relative">
                          <select
                            value={formData.cloudInitTemplate}
                            onChange={(e) => setFormData(prev => ({ ...prev, cloudInitTemplate: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                            disabled={isLoading}
                          >
                            <option value="">Select cloud init template</option>
                            {availableApplications.map((app) => (
                              <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                                {app.name} {app.version ? `(${app.version})` : ''}
                              </option>
                            ))}
                          </select>
                          {formData.cloudInitTemplate && (
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, cloudInitTemplate: '' }))}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500"
                              title="Supprimer la sélection"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        <textarea
                          value={formData.userData}
                          onChange={(e) => setFormData(prev => ({ ...prev, userData: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                          rows={8}
                          placeholder="#cloud-config&#10;# Configuration Cloud-Init personnalisée&#10;packages:&#10;  - nginx&#10;  - docker.io"
                          disabled={isLoading}
                        />
                      </div>

                      {/* Custom Script */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Custom Script
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Script personnalisé à exécuter lors de l'installation" />
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.customScript}
                            onChange={(e) => setFormData(prev => ({ ...prev, customScript: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                            rows={6}
                            placeholder="#!/bin/bash&#10;# Votre script personnalisé ici&#10;echo 'Installation personnalisée en cours...'"
                            disabled={isLoading}
                          />
                          {formData.customScript && (
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, customScript: '' }))}
                              className="absolute right-3 top-3 text-gray-400 hover:text-red-500"
                              title="Supprimer le script"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Script bash qui sera exécuté après l'installation du système d'exploitation.
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Custom Image Section */}
                  {formData.advancedImageType === 'custom' && (
                    <div className="space-y-6">
                      {/* Custom Image Selector */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Custom Image
                          <Info className="w-4 h-4 inline ml-1 text-purple-500" title="Sélectionner ou créer une image personnalisée" />
                        </label>
                        <div className="space-y-2">
                          <select
                            value={formData.customImageId || ''}
                            onChange={(e) => setFormData(prev => ({ ...prev, customImageId: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                            disabled={isLoading}
                          >
                            <option value="">Select existing Custom Image</option>
                            {customImages.map((image) => (
                              <option key={image.imageId || image.id} value={image.imageId || image.id}>
                                {image.name} {image.version ? `(${image.version})` : ''} - {image.osType}
                              </option>
                            ))}
                          </select>

                          <div className="text-center">
                            <button
                              type="button"
                              onClick={() => {
                                console.log('🔄 Opening Custom Image Modal...');
                                setShowCustomImageModal(true);
                              }}
                              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                              disabled={isLoading}
                            >
                              <Upload className="w-4 h-4 mr-2" />
                              Add Custom Image
                            </button>
                          </div>
                        </div>
                        {errors.customImage && (
                          <p className="text-xs text-red-600 mt-1">{errors.customImage}</p>
                        )}
                        {formData.customImageId && (
                          <div className="mt-2 p-2 bg-purple-50 rounded-md">
                            <p className="text-sm text-purple-800">
                              Custom Image sélectionnée: <strong>
                                {customImages.find(img => (img.imageId || img.id) === formData.customImageId)?.name || 'Custom Image'}
                              </strong>
                            </p>
                            {customImages.find(img => (img.imageId || img.id) === formData.customImageId)?.description && (
                              <p className="text-xs text-purple-600 mt-1">
                                {customImages.find(img => (img.imageId || img.id) === formData.customImageId)?.description}
                              </p>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Admin Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Root Password' : 'Admin Password'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title={formData.enableRootUser ? "Mot de passe pour l'utilisateur root" : "Mot de passe pour l'utilisateur admin"} />
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? "text" : "password"}
                            value={formData.adminPassword}
                            onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24"
                            placeholder="Select or create new password"
                            disabled={isLoading}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center">
                            <button
                              type="button"
                              onClick={() => {
                                setIsGeneratingPassword(true);
                                const lowercase = 'abcdefghijklmnopqrstuvwxyz';
                                const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                                const numbers = '0123456789';
                                const allChars = lowercase + uppercase + numbers;
                                let password = '';

                                password += lowercase[Math.floor(Math.random() * lowercase.length)];
                                password += uppercase[Math.floor(Math.random() * uppercase.length)];
                                password += numbers[Math.floor(Math.random() * numbers.length)];

                                for (let i = 3; i < 12; i++) {
                                  password += allChars[Math.floor(Math.random() * allChars.length)];
                                }

                                password = password.split('').sort(() => Math.random() - 0.5).join('');

                                setTimeout(() => {
                                  setFormData(prev => ({ ...prev, adminPassword: password }));
                                  setIsGeneratingPassword(false);
                                  setShowPassword(true);
                                }, 500);
                              }}
                              className="px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors"
                              title="Générer un mot de passe sécurisé"
                              disabled={isLoading || isGeneratingPassword}
                            >
                              <RefreshCw className={`w-3 h-3 ${isGeneratingPassword ? 'animate-spin' : ''}`} />
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="px-2 py-1 text-gray-500 hover:text-gray-700"
                              title={showPassword ? "Masquer le mot de passe" : "Afficher le mot de passe"}
                              disabled={isLoading}
                            >
                              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                            {formData.adminPassword && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, adminPassword: '' }))}
                                className="px-2 py-1 text-gray-400 hover:text-red-500 rounded-r"
                                title="Supprimer le mot de passe"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Enable Root User */}
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.enableRootUser}
                            onChange={(e) => setFormData(prev => ({ ...prev, enableRootUser: e.target.checked }))}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            disabled={isLoading}
                          />
                          <span className="text-sm font-medium text-gray-700">
                            Enable Root User
                            <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Activer l'utilisateur root" />
                          </span>
                        </label>
                      </div>

                      {/* Public SSH-Key */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Public SSH-Key for User Root' : 'Public SSH-Key for User Admin'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Clé SSH publique pour l'accès" />
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.publicSshKey}
                            onChange={(e) => setFormData(prev => ({ ...prev, publicSshKey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20"
                            rows={3}
                            placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname"
                            disabled={isLoading}
                          />
                          <div className="absolute top-2 right-2 flex flex-col space-y-1">
                            <button
                              type="button"
                              onClick={() => {
                                const key = generateExampleSSHKey();
                                setFormData(prev => ({ ...prev, publicSshKey: key }));
                              }}
                              className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                              title="Générer une clé SSH d'exemple"
                              disabled={isLoading}
                            >
                              <Key className="w-3 h-3" />
                            </button>
                            {formData.publicSshKey && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, publicSshKey: '' }))}
                                className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                title="Supprimer la clé SSH"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Cloud-Init */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Cloud-Init
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Template Cloud-Init pour la configuration automatique" />
                        </label>
                        <div className="relative">
                          <select
                            value={formData.cloudInitTemplate}
                            onChange={(e) => setFormData(prev => ({ ...prev, cloudInitTemplate: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                            disabled={isLoading}
                          >
                            <option value="">Select cloud init template</option>
                            {availableApplications.map((app) => (
                              <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                                {app.name} {app.version ? `(${app.version})` : ''}
                              </option>
                            ))}
                          </select>
                          {formData.cloudInitTemplate && (
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, cloudInitTemplate: '' }))}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500"
                              title="Supprimer la sélection"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        <textarea
                          value={formData.userData}
                          onChange={(e) => setFormData(prev => ({ ...prev, userData: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                          rows={8}
                          placeholder="#cloud-config&#10;# Configuration Cloud-Init personnalisée&#10;packages:&#10;  - nginx&#10;  - docker.io"
                          disabled={isLoading}
                        />
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Installation standard */}
              {formData.installationType === 'standard' && (
                <div className="space-y-6">
                  {/* Image Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Système d'exploitation
                    </label>
                    {loadingImages ? (
                      <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                        <span className="text-gray-600">Chargement des images...</span>
                      </div>
                    ) : (
                      <select
                        value={formData.imageId}
                        onChange={(e) => setFormData(prev => ({ ...prev, imageId: e.target.value }))}
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.imageId ? 'border-red-300' : 'border-gray-300'
                        }`}
                        disabled={isLoading}
                      >
                        <option value="">Sélectionner un système d'exploitation</option>
                        {availableImages.map((image) => (
                          <option key={image.imageId || image.id} value={image.imageId || image.id}>
                            {image.name} {image.version ? `(${image.version})` : ''}
                          </option>
                        ))}
                      </select>
                    )}
                    {errors.imageId && (
                      <p className="text-red-600 text-sm mt-1">{errors.imageId}</p>
                    )}
                  </div>

                  {/* Application Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Application (optionnel)
                    </label>

                    {loadingApplications ? (
                      <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                        <span className="text-gray-600">Chargement des applications...</span>
                      </div>
                    ) : (
                      <select
                        value={formData.selectedApplication}
                        onChange={(e) => setFormData(prev => ({ ...prev, selectedApplication: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={isLoading}
                      >
                        <option value="">Aucune application (OS seulement)</option>
                        {availableApplications.map((app) => (
                          <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                            {app.name} {app.version ? `(${app.version})` : ''}
                          </option>
                        ))}
                      </select>
                    )}

                    {formData.selectedApplication && (
                      <div className="mt-2 p-2 bg-green-50 rounded-md">
                        <p className="text-sm text-green-800">
                          Application sélectionnée: <strong>
                            {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.name || formData.selectedApplication}
                          </strong>
                        </p>
                        {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.description && (
                          <p className="text-xs text-green-600 mt-1">
                            {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.description}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Key className="w-4 h-4 inline mr-1" />
                      Mot de passe root
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        className={`w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Mot de passe pour l'accès root"
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        title={showPassword ? "Cacher le mot de passe" : "Voir le mot de passe"}
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      <button
                        type="button"
                        onClick={generateSecurePassword}
                        disabled={isGeneratingPassword || isLoading}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 disabled:text-gray-400"
                        title="Générer un mot de passe sécurisé"
                      >
                        <RefreshCw className={`w-4 h-4 ${isGeneratingPassword ? 'animate-spin' : ''}`} />
                      </button>
                    </div>
                    {formData.password && (
                      <div className="mt-2">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                getPasswordStrength(formData.password).color
                              }`}
                              style={{ width: `${getPasswordStrength(formData.password).percentage}%` }}
                            />
                          </div>
                          <span className={`text-xs font-medium ${getPasswordStrength(formData.password).color.replace('bg-', 'text-')}`}>
                            {getPasswordStrength(formData.password).label}
                          </span>
                        </div>
                      </div>
                    )}
                    {errors.password && (
                      <p className="text-red-600 text-sm mt-1">{errors.password}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Message d'erreur global */}
              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{errors.submit}</p>
                </div>
              )}

              {errors.access && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{errors.access}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  disabled={isLoading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                      Réinstallation...
                    </>
                  ) : (
                    'Réinstaller le VPS'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Custom Image Modal */}
      {showCustomImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Add Custom Image</h2>
              <button
                onClick={() => setShowCustomImageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              {/* Image URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="URL de l'image personnalisée" />
                </label>
                <input
                  type="url"
                  value={formData.customImageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageUrl: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/my-custom-image.iso"
                />
              </div>

              {/* Image Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image Name
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Nom de l'image personnalisée" />
                </label>
                <input
                  type="text"
                  value={formData.customImageName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Image"
                />
              </div>

              {/* OS Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OS Type
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Type de système d'exploitation" />
                </label>
                <select
                  value={formData.customImageOsType}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageOsType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Linux">Linux</option>
                  <option value="Windows">Windows</option>
                </select>
              </div>

              {/* Version */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Version
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Version du système d'exploitation" />
                </label>
                <input
                  type="text"
                  value={formData.customImageVersion}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageVersion: e.target.value }))}
                  className="w-full px-3 py-2 border border-red-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="22.04, 2022, etc."
                />
                <p className="text-xs text-red-600 mt-1">Version is required.</p>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Description de l'image personnalisée" />
                </label>
                <textarea
                  value={formData.customImageDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageDescription: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Description de votre image personnalisée..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t">
              <button
                type="button"
                onClick={() => setShowCustomImageModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={async () => {
                  try {
                    // Validate required fields
                    if (!formData.customImageUrl) {
                      alert('Image URL is required');
                      return;
                    }
                    if (!formData.customImageName) {
                      alert('Image Name is required');
                      return;
                    }
                    if (!formData.customImageVersion) {
                      alert('Version is required');
                      return;
                    }

                    console.log('Saving custom image:', {
                      url: formData.customImageUrl,
                      name: formData.customImageName,
                      osType: formData.customImageOsType,
                      version: formData.customImageVersion,
                      description: formData.customImageDescription
                    });

                    // Save custom image via API
                    const result = await customImageService.createCustomImage({
                      url: formData.customImageUrl,
                      name: formData.customImageName,
                      osType: formData.customImageOsType,
                      version: formData.customImageVersion,
                      description: formData.customImageDescription
                    });

                    if (result.success) {
                      console.log('✅ Custom image created successfully:', result.data);

                      // Refresh the custom images list
                      await fetchCustomImages();

                      // Update the custom image selector with the new image
                      const newImageId = result.data.imageId || result.data.id;
                      console.log('🔄 Setting selected custom image ID:', newImageId);

                      setFormData(prev => ({
                        ...prev,
                        customImageId: newImageId, // Use the ID for selection
                        // Reset custom image form fields
                        customImageUrl: '',
                        customImageName: '',
                        customImageVersion: '',
                        customImageDescription: ''
                      }));
                      setShowCustomImageModal(false);

                      alert('Custom image created successfully!');
                    }
                  } catch (error) {
                    console.error('❌ Failed to create custom image:', error);
                    alert('Failed to create custom image: ' + error.message);
                  }
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ReinstallModal;
