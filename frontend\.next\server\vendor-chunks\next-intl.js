"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function(n) {\n        for(var e = 1; e < arguments.length; e++){\n            var t = arguments[e];\n            for(var r in t)({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n        }\n        return n;\n    }, _extends.apply(null, arguments);\n}\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCxTQUFTQztJQUNQLE9BQU9BLFdBQVdKLE9BQU9LLE1BQU0sR0FBR0wsT0FBT0ssTUFBTSxDQUFDQyxJQUFJLEtBQUssU0FBVUMsQ0FBQztRQUNsRSxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUMsVUFBVUMsTUFBTSxFQUFFRixJQUFLO1lBQ3pDLElBQUlHLElBQUlGLFNBQVMsQ0FBQ0QsRUFBRTtZQUNwQixJQUFLLElBQUlJLEtBQUtELEVBQUcsQ0FBQyxDQUFDLEdBQUdFLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDSCxHQUFHQyxNQUFPTCxDQUFBQSxDQUFDLENBQUNLLEVBQUUsR0FBR0QsQ0FBQyxDQUFDQyxFQUFFO1FBQ2pFO1FBQ0EsT0FBT0w7SUFDVCxHQUFHSCxTQUFTVyxLQUFLLENBQUMsTUFBTU47QUFDMUI7QUFFQVAsa0JBQWUsR0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/ZWQxNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICByZXR1cm4gX2V4dGVuZHMgPSBPYmplY3QuYXNzaWduID8gT2JqZWN0LmFzc2lnbi5iaW5kKCkgOiBmdW5jdGlvbiAobikge1xuICAgIGZvciAodmFyIGUgPSAxOyBlIDwgYXJndW1lbnRzLmxlbmd0aDsgZSsrKSB7XG4gICAgICB2YXIgdCA9IGFyZ3VtZW50c1tlXTtcbiAgICAgIGZvciAodmFyIHIgaW4gdCkgKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsIHIpICYmIChuW3JdID0gdFtyXSk7XG4gICAgfVxuICAgIHJldHVybiBuO1xuICB9LCBfZXh0ZW5kcy5hcHBseShudWxsLCBhcmd1bWVudHMpO1xufVxuXG5leHBvcnRzLmV4dGVuZHMgPSBfZXh0ZW5kcztcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIl9leHRlbmRzIiwiYXNzaWduIiwiYmluZCIsIm4iLCJlIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidCIsInIiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJhcHBseSIsImV4dGVuZHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function(k) {\n    if (k !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n        enumerable: true,\n        get: function() {\n            return useIntl[k];\n        }\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */ // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n    return function() {\n        try {\n            return hook(...arguments);\n        } catch (_unused) {\n            throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl-docs.vercel.app/docs/environments/server-client-components#missing-context\"));\n        }\n    };\n}\nconst useTranslations = callHook(\"useTranslations\", useIntl.useTranslations);\nconst useFormatter = callHook(\"useFormatter\", useIntl.useFormatter);\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function(k) {\n    if (k !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n        enumerable: true,\n        get: function() {\n            return useIntl[k];\n        }\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\nfunction useLocale() {\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const params = navigation.useParams();\n    let locale;\n    try {\n        // eslint-disable-next-line react-compiler/react-compiler\n        // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n        locale = _useLocale.useLocale();\n    } catch (error) {\n        if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === \"string\") {\n            locale = params[constants.LOCALE_SEGMENT_NAME];\n        } else {\n            throw error;\n        }\n    }\n    return locale;\n}\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl-docs.vercel.app/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = \"X-NEXT-INTL-LOCALE\";\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = \"locale\";\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUU3RCx5Q0FBeUM7QUFDekMsTUFBTUMscUJBQXFCO0FBRTNCLDhEQUE4RDtBQUM5RCxNQUFNQyxzQkFBc0I7QUFFNUJILDBCQUEwQixHQUFHRTtBQUM3QkYsMkJBQTJCLEdBQUdHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9zaGFyZWQvY29uc3RhbnRzLmpzPzg3ZWMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG4vLyBTaG91bGQgdGFrZSBwcmVjZWRlbmNlIG92ZXIgdGhlIGNvb2tpZVxuY29uc3QgSEVBREVSX0xPQ0FMRV9OQU1FID0gJ1gtTkVYVC1JTlRMLUxPQ0FMRSc7XG5cbi8vIEluIGEgVVJMIGxpa2UgXCIvZW4tVVMvYWJvdXRcIiwgdGhlIGxvY2FsZSBzZWdtZW50IGlzIFwiZW4tVVNcIlxuY29uc3QgTE9DQUxFX1NFR01FTlRfTkFNRSA9ICdsb2NhbGUnO1xuXG5leHBvcnRzLkhFQURFUl9MT0NBTEVfTkFNRSA9IEhFQURFUl9MT0NBTEVfTkFNRTtcbmV4cG9ydHMuTE9DQUxFX1NFR01FTlRfTkFNRSA9IExPQ0FMRV9TRUdNRU5UX05BTUU7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJIRUFERVJfTE9DQUxFX05BTUUiLCJMT0NBTEVfU0VHTUVOVF9OQU1FIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n() {\n    return n = Object.assign ? Object.assign.bind() : function(n) {\n        for(var r = 1; r < arguments.length; r++){\n            var t = arguments[r];\n            for(var a in t)({}).hasOwnProperty.call(t, a) && (n[a] = t[a]);\n        }\n        return n;\n    }, n.apply(null, arguments);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBO0lBQUksT0FBT0EsSUFBRUMsT0FBT0MsTUFBTSxHQUFDRCxPQUFPQyxNQUFNLENBQUNDLElBQUksS0FBRyxTQUFTSCxDQUFDO1FBQUUsSUFBSSxJQUFJSSxJQUFFLEdBQUVBLElBQUVDLFVBQVVDLE1BQU0sRUFBQ0YsSUFBSTtZQUFDLElBQUlHLElBQUVGLFNBQVMsQ0FBQ0QsRUFBRTtZQUFDLElBQUksSUFBSUksS0FBS0QsRUFBRSxDQUFDLENBQUMsR0FBR0UsY0FBYyxDQUFDQyxJQUFJLENBQUNILEdBQUVDLE1BQUtSLENBQUFBLENBQUMsQ0FBQ1EsRUFBRSxHQUFDRCxDQUFDLENBQUNDLEVBQUU7UUFBQztRQUFDLE9BQU9SO0lBQUMsR0FBRUEsRUFBRVcsS0FBSyxDQUFDLE1BQUtOO0FBQVU7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzPzQyMTAiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbigpe3JldHVybiBuPU9iamVjdC5hc3NpZ24/T2JqZWN0LmFzc2lnbi5iaW5kKCk6ZnVuY3Rpb24obil7Zm9yKHZhciByPTE7cjxhcmd1bWVudHMubGVuZ3RoO3IrKyl7dmFyIHQ9YXJndW1lbnRzW3JdO2Zvcih2YXIgYSBpbiB0KSh7fSkuaGFzT3duUHJvcGVydHkuY2FsbCh0LGEpJiYoblthXT10W2FdKX1yZXR1cm4gbn0sbi5hcHBseShudWxsLGFyZ3VtZW50cyl9ZXhwb3J0e24gYXMgZXh0ZW5kc307XG4iXSwibmFtZXMiOlsibiIsIk9iamVjdCIsImFzc2lnbiIsImJpbmQiLCJyIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidCIsImEiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJhcHBseSIsImV4dGVuZHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl-docs.vercel.app/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUEySyxxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzP2RjZTQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnR7ZXh0ZW5kcyBhcyBlfWZyb21cIi4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanNcIjtpbXBvcnQgbCBmcm9tXCJyZWFjdFwiO2ltcG9ydHtJbnRsUHJvdmlkZXIgYXMgdH1mcm9tXCJ1c2UtaW50bC9fSW50bFByb3ZpZGVyXCI7ZnVuY3Rpb24gcihyKXtsZXR7bG9jYWxlOm8sLi4uaX09cjtpZighbyl0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZGV0ZXJtaW5lIGxvY2FsZSBpbiBgTmV4dEludGxDbGllbnRQcm92aWRlcmAsIHBsZWFzZSBwcm92aWRlIHRoZSBgbG9jYWxlYCBwcm9wIGV4cGxpY2l0bHkuXFxuXFxuU2VlIGh0dHBzOi8vbmV4dC1pbnRsLWRvY3MudmVyY2VsLmFwcC9kb2NzL2NvbmZpZ3VyYXRpb24jbG9jYWxlXCIpO3JldHVybiBsLmNyZWF0ZUVsZW1lbnQodCxlKHtsb2NhbGU6b30saSkpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImV4dGVuZHMiLCJlIiwibCIsIkludGxQcm92aWRlciIsInQiLCJyIiwibG9jYWxlIiwibyIsImkiLCJFcnJvciIsImNyZWF0ZUVsZW1lbnQiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n() {\n    return n = Object.assign ? Object.assign.bind() : function(n) {\n        for(var r = 1; r < arguments.length; r++){\n            var t = arguments[r];\n            for(var a in t)({}).hasOwnProperty.call(t, a) && (n[a] = t[a]);\n        }\n        return n;\n    }, n.apply(null, arguments);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBO0lBQUksT0FBT0EsSUFBRUMsT0FBT0MsTUFBTSxHQUFDRCxPQUFPQyxNQUFNLENBQUNDLElBQUksS0FBRyxTQUFTSCxDQUFDO1FBQUUsSUFBSSxJQUFJSSxJQUFFLEdBQUVBLElBQUVDLFVBQVVDLE1BQU0sRUFBQ0YsSUFBSTtZQUFDLElBQUlHLElBQUVGLFNBQVMsQ0FBQ0QsRUFBRTtZQUFDLElBQUksSUFBSUksS0FBS0QsRUFBRSxDQUFDLENBQUMsR0FBR0UsY0FBYyxDQUFDQyxJQUFJLENBQUNILEdBQUVDLE1BQUtSLENBQUFBLENBQUMsQ0FBQ1EsRUFBRSxHQUFDRCxDQUFDLENBQUNDLEVBQUU7UUFBQztRQUFDLE9BQU9SO0lBQUMsR0FBRUEsRUFBRVcsS0FBSyxDQUFDLE1BQUtOO0FBQVU7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzPzQyMTAiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbigpe3JldHVybiBuPU9iamVjdC5hc3NpZ24/T2JqZWN0LmFzc2lnbi5iaW5kKCk6ZnVuY3Rpb24obil7Zm9yKHZhciByPTE7cjxhcmd1bWVudHMubGVuZ3RoO3IrKyl7dmFyIHQ9YXJndW1lbnRzW3JdO2Zvcih2YXIgYSBpbiB0KSh7fSkuaGFzT3duUHJvcGVydHkuY2FsbCh0LGEpJiYoblthXT10W2FdKX1yZXR1cm4gbn0sbi5hcHBseShudWxsLGFyZ3VtZW50cyl9ZXhwb3J0e24gYXMgZXh0ZW5kc307XG4iXSwibmFtZXMiOlsibiIsIk9iamVjdCIsImFzc2lnbiIsImJpbmQiLCJyIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidCIsImEiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJhcHBseSIsImV4dGVuZHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\n\n\n\n\n\n\nasync function i(i) {\n    let { locale: n, now: s, timeZone: m, ...c } = i;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: null != n ? n : await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),\n        now: null != s ? s : await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n        timeZone: null != m ? m : await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    }, c));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1FO0FBQXFCO0FBQW1EO0FBQW1EO0FBQWdEO0FBQXFEO0FBQUEsZUFBZU8sRUFBRUEsQ0FBQztJQUFFLElBQUcsRUFBQ0MsUUFBT0MsQ0FBQyxFQUFDQyxLQUFJQyxDQUFDLEVBQUNDLFVBQVNDLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNQO0lBQUUscUJBQU9MLDBEQUFlLENBQUNDLHlFQUFDQSxFQUFDRixnRkFBQ0EsQ0FBQztRQUFDTyxRQUFPLFFBQU1DLElBQUVBLElBQUUsTUFBTUwsNkVBQUNBO1FBQUdNLEtBQUksUUFBTUMsSUFBRUEsSUFBRSxNQUFNTiwwRUFBQ0E7UUFBR08sVUFBUyxRQUFNQyxJQUFFQSxJQUFFLE1BQU1QLCtFQUFDQTtJQUFFLEdBQUVRO0FBQUc7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlci5qcz9lNDYwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCByIGZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi4vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIjtpbXBvcnQgbyBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qc1wiO2ltcG9ydCBsIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzXCI7aW1wb3J0IGEgZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qc1wiO2FzeW5jIGZ1bmN0aW9uIGkoaSl7bGV0e2xvY2FsZTpuLG5vdzpzLHRpbWVab25lOm0sLi4uY309aTtyZXR1cm4gci5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm51bGwhPW4/bjphd2FpdCBvKCksbm93Om51bGwhPXM/czphd2FpdCBsKCksdGltZVpvbmU6bnVsbCE9bT9tOmF3YWl0IGEoKX0sYykpfWV4cG9ydHtpIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImV4dGVuZHMiLCJlIiwiciIsInQiLCJvIiwibCIsImEiLCJpIiwibG9jYWxlIiwibiIsIm5vdyIsInMiLCJ0aW1lWm9uZSIsIm0iLCJjIiwiY3JlYXRlRWxlbWVudCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n\n\n\n\nconst o = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function() {\n    const e = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n    return e instanceof Promise ? await e : e;\n});\nconst i = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function() {\n    let t;\n    try {\n        t = (await o()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.HEADER_LOCALE_NAME) || void 0;\n    } catch (t) {\n        if (t instanceof Error && \"DYNAMIC_SERVER_USAGE\" === t.digest) {\n            const e = new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl-docs.vercel.app/docs/getting-started/app-router/with-i18n-routing#static-rendering\", {\n                cause: t\n            });\n            throw e.digest = t.digest, e;\n        }\n        throw t;\n    }\n    return t;\n});\nasync function s() {\n    return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_3__.getCachedRequestLocale)() || await i();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst n = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(function() {\n    return {\n        locale: void 0\n    };\n});\nfunction t() {\n    return n().locale;\n}\nfunction c(o) {\n    n().locale = o;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBQSxNQUFNRSxJQUFFRCw0Q0FBQ0EsQ0FBRTtJQUFXLE9BQU07UUFBQ0UsUUFBTyxLQUFLO0lBQUM7QUFBQztBQUFJLFNBQVNDO0lBQUksT0FBT0YsSUFBSUMsTUFBTTtBQUFBO0FBQUMsU0FBU0UsRUFBRUosQ0FBQztJQUFFQyxJQUFJQyxNQUFNLEdBQUNGO0FBQUM7QUFBaUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL1JlcXVlc3RMb2NhbGVDYWNoZS5qcz8xZjliIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBvfWZyb21cInJlYWN0XCI7Y29uc3Qgbj1vKChmdW5jdGlvbigpe3JldHVybntsb2NhbGU6dm9pZCAwfX0pKTtmdW5jdGlvbiB0KCl7cmV0dXJuIG4oKS5sb2NhbGV9ZnVuY3Rpb24gYyhvKXtuKCkubG9jYWxlPW99ZXhwb3J0e3QgYXMgZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSxjIGFzIHNldENhY2hlZFJlcXVlc3RMb2NhbGV9O1xuIl0sIm5hbWVzIjpbImNhY2hlIiwibyIsIm4iLCJsb2NhbGUiLCJ0IiwiYyIsImdldENhY2hlZFJlcXVlc3RMb2NhbGUiLCJzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\nconst i = (0,react__WEBPACK_IMPORTED_MODULE_2__.cache)(function() {\n    let n;\n    try {\n        n = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME);\n    } catch (e) {\n        throw e instanceof Error && \"DYNAMIC_SERVER_USAGE\" === e.digest ? new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl-docs.vercel.app/docs/getting-started/app-router/with-i18n-routing#static-rendering\", {\n            cause: e\n        }) : e;\n    }\n    return n || (console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl-docs.vercel.app/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"), (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()), n;\n});\nfunction s() {\n    return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || i();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ d)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./i18n.js\");\n\n\n\n\n\n\nconst c = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(function() {\n    return new Date;\n});\nconst l = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(function() {\n    return Intl.DateTimeFormat().resolvedOptions().timeZone;\n});\nconst u = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function(t, n) {\n    if (\"function\" != typeof t) throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl-docs.vercel.app/docs/usage/configuration#i18n-request\\n\");\n    const o = {\n        get locale () {\n            return n || (0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)();\n        },\n        get requestLocale () {\n            return n ? Promise.resolve(n) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)();\n        }\n    };\n    let r = t(o);\n    r instanceof Promise && (r = await r);\n    const s = r.locale || await o.requestLocale;\n    return s || (console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl-docs.vercel.app/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"), (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)()), {\n        ...r,\n        locale: s,\n        now: r.now || c(),\n        timeZone: r.timeZone || l()\n    };\n}), f = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__._createIntlFormatters), m = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__._createCache);\nconst d = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(async function(e) {\n    const t = await u(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"], e);\n    return {\n        ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.initializeConfig)(t),\n        _formatters: f(m())\n    };\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst r = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function() {\n    const o = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    return Promise.resolve(o.locale);\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBOEI7QUFBQSxNQUFNRyxJQUFFRiw0Q0FBQ0EsQ0FBRTtJQUFpQixNQUFNQSxJQUFFLE1BQU1DLHlEQUFDQTtJQUFHLE9BQU9FLFFBQVFDLE9BQU8sQ0FBQ0osRUFBRUssTUFBTTtBQUFDO0FBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRMb2NhbGUuanM/Y2Q4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgb31mcm9tXCJyZWFjdFwiO2ltcG9ydCB0IGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgcj1vKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IG89YXdhaXQgdCgpO3JldHVybiBQcm9taXNlLnJlc29sdmUoby5sb2NhbGUpfSkpO2V4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbImNhY2hlIiwibyIsInQiLCJyIiwiUHJvbWlzZSIsInJlc29sdmUiLCJsb2NhbGUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst t = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function(n) {\n    return (await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now;\n});\nasync function r(n) {\n    return t(null == n ? void 0 : n.locale);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFBOEI7QUFBQSxNQUFNRyxJQUFFRiw0Q0FBQ0EsQ0FBRSxlQUFlQSxDQUFDO0lBQUUsT0FBTSxDQUFDLE1BQU1DLHlEQUFDQSxDQUFDRCxFQUFDLEVBQUdHLEdBQUc7QUFBQTtBQUFJLGVBQWVDLEVBQUVKLENBQUM7SUFBRSxPQUFPRSxFQUFFLFFBQU1GLElBQUUsS0FBSyxJQUFFQSxFQUFFSyxNQUFNO0FBQUM7QUFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldE5vdy5qcz8zYzJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBufWZyb21cInJlYWN0XCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCB0PW4oKGFzeW5jIGZ1bmN0aW9uKG4pe3JldHVybihhd2FpdCBvKG4pKS5ub3d9KSk7YXN5bmMgZnVuY3Rpb24gcihuKXtyZXR1cm4gdChudWxsPT1uP3ZvaWQgMDpuLmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiY2FjaGUiLCJuIiwibyIsInQiLCJub3ciLCJyIiwibG9jYWxlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\n\n\nconst o = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(async function(t) {\n    return (await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone;\n});\nasync function r(t) {\n    return o(null == t ? void 0 : t.locale);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUE4QjtBQUFBLE1BQU1HLElBQUVGLDRDQUFDQSxDQUFFLGVBQWVBLENBQUM7SUFBRSxPQUFNLENBQUMsTUFBTUMseURBQUNBLENBQUNELEVBQUMsRUFBR0csUUFBUTtBQUFBO0FBQUksZUFBZUMsRUFBRUosQ0FBQztJQUFFLE9BQU9FLEVBQUUsUUFBTUYsSUFBRSxLQUFLLElBQUVBLEVBQUVLLE1BQU07QUFBQztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanM/MjZlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgdH1mcm9tXCJyZWFjdFwiO2ltcG9ydCBuIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgbz10KChhc3luYyBmdW5jdGlvbih0KXtyZXR1cm4oYXdhaXQgbih0KSkudGltZVpvbmV9KSk7YXN5bmMgZnVuY3Rpb24gcih0KXtyZXR1cm4gbyhudWxsPT10P3ZvaWQgMDp0LmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiY2FjaGUiLCJ0IiwibiIsIm8iLCJ0aW1lWm9uZSIsInIiLCJsb2NhbGUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\ztech_dev\frontend\node_modules\next-intl\dist\esm\shared\NextIntlClientProvider.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o = \"X-NEXT-INTL-LOCALE\", L = \"locale\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxNQUFNQSxJQUFFLHNCQUFxQkMsSUFBRTtBQUFrRSIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanM/YmU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBvPVwiWC1ORVhULUlOVEwtTE9DQUxFXCIsTD1cImxvY2FsZVwiO2V4cG9ydHtvIGFzIEhFQURFUl9MT0NBTEVfTkFNRSxMIGFzIExPQ0FMRV9TRUdNRU5UX05BTUV9O1xuIl0sIm5hbWVzIjpbIm8iLCJMIiwiSEVBREVSX0xPQ0FMRV9OQU1FIiwiTE9DQUxFX1NFR01FTlRfTkFNRSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ })

};
;