/**
 * Script de test pour vérifier les endpoints API de réinstallation
 * Ce script teste UNIQUEMENT la connectivité et la validation - PAS de réinstallation réelle
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5002';

async function testEndpoints() {
  console.log('🚀 Test des endpoints API de réinstallation\n');

  // Test 1: Vérifier que le serveur répond
  try {
    console.log('1️⃣ Test de connectivité du serveur...');
    const response = await axios.get(`${BASE_URL}/api/vps/providers`);
    console.log('✅ Serveur accessible, status:', response.status);
  } catch (error) {
    console.log('❌ Serveur non accessible:', error.message);
    return;
  }

  // Test 2: Test endpoint images
  try {
    console.log('\n2️⃣ Test endpoint /api/vps/images...');
    const response = await axios.get(`${BASE_URL}/api/vps/images`);
    console.log('✅ Endpoint images accessible, status:', response.status);
    console.log('📋 Nombre d\'images:', response.data.data?.length || 0);
  } catch (error) {
    console.log('❌ Erreur endpoint images:', error.response?.status, error.response?.data?.message || error.message);
  }

  // Test 3: Test endpoint applications
  try {
    console.log('\n3️⃣ Test endpoint /api/vps/applications...');
    const response = await axios.get(`${BASE_URL}/api/vps/applications`);
    console.log('✅ Endpoint applications accessible, status:', response.status);
    console.log('📱 Nombre d\'applications:', response.data.data?.length || 0);
  } catch (error) {
    console.log('❌ Erreur endpoint applications:', error.response?.status, error.response?.data?.message || error.message);
  }

  // Test 4: Test validation endpoint reinstall (avec données invalides)
  try {
    console.log('\n4️⃣ Test validation endpoint reinstall...');
    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/test-instance/reinstall`,
      {}, // Données vides pour tester la validation
      { validateStatus: () => true } // Accepter tous les codes de statut
    );
    console.log('✅ Endpoint reinstall accessible, status:', response.status);
    if (response.status === 400) {
      console.log('✅ Validation fonctionne correctement');
      console.log('📋 Erreurs de validation:', response.data.errors);
    }
  } catch (error) {
    console.log('❌ Erreur endpoint reinstall:', error.message);
  }

  // Test 5: Test validation avec données valides (mais instance inexistante)
  try {
    console.log('\n5️⃣ Test avec données valides...');
    const validData = {
      imageId: 'test-image-id',
      password: 'TestPassword123!'
    };
    
    const response = await axios.post(
      `${BASE_URL}/api/vps/instances/test-instance/reinstall`,
      validData,
      { validateStatus: () => true }
    );
    console.log('✅ Test données valides, status:', response.status);
    console.log('📋 Réponse:', response.data.message || response.data);
  } catch (error) {
    console.log('❌ Erreur test données valides:', error.message);
  }

  console.log('\n🎉 Tests terminés !');
}

// Exécuter les tests
if (require.main === module) {
  testEndpoints().catch(console.error);
}

module.exports = { testEndpoints };
