const ContaboProvider = require('./services/providers/ContaboProvider');
const CustomImageService = require('./services/CustomImageService');

async function testCustomImageOnly() {
  console.log('🧪 Testing Custom Image Creation and Usage...\n');

  try {
    const contaboProvider = new ContaboProvider();
    const customImageService = new CustomImageService();
    const instanceId = 202718127;

    console.log('='.repeat(60));
    console.log('🔄 TEST: CREATE AND USE CUSTOM IMAGE');
    console.log('='.repeat(60));

    // Create a custom image
    console.log('📝 Step 1: Creating Custom Image...');
    
    const customImageData = {
      url: 'https://cloud-images.ubuntu.com/releases/22.04/release/ubuntu-22.04-server-cloudimg-amd64.img',
      name: 'Ubuntu 22.04 Test Custom',
      osType: 'Linux',
      version: '22.04',
      description: 'Test custom Ubuntu 22.04 server image'
    };

    console.log('📤 Creating custom image:');
    console.log('- URL:', customImageData.url);
    console.log('- Name:', customImageData.name);
    console.log('- OS Type:', customImageData.osType);
    console.log('- Version:', customImageData.version);

    const createResult = await customImageService.createCustomImage(customImageData);
    console.log('✅ Custom image created successfully!');
    console.log('📋 Image data:', createResult.data);
    
    const customImageId = createResult.data.imageId || createResult.data.id;
    console.log('🆔 Custom Image ID:', customImageId);

    if (!customImageId) {
      throw new Error('Custom image ID not found in response');
    }

    // Wait a moment for the image to be processed
    console.log('\n⏳ Waiting 30 seconds for image processing...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    // Now test reinstallation with the custom image
    console.log('\n📝 Step 2: Testing Reinstallation with Custom Image...');
    
    const generatePassword = () => {
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const numbers = '0123456789';
      const allChars = lowercase + uppercase + numbers;
      let password = '';
      
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      
      for (let i = 3; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      
      return password.split('').sort(() => Math.random() - 0.5).join('');
    };

    const generateSSHKey = () => {
      const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} <EMAIL>`;
    };

    const customPassword = generatePassword();
    const customSSHKey = generateSSHKey();
    
    const customInstallData = {
      imageId: customImageId,
      password: customPassword,
      enableRootUser: true,
      sshKeys: [customSSHKey],
      userData: `#cloud-config
# Custom image installation test
packages:
  - nginx
  - htop
  - curl
  - git

users:
  - name: testuser
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh_authorized_keys:
      - ${customSSHKey}

# Configure SSH
ssh_pwauth: true
password: ${customPassword}
chpasswd:
  expire: false

write_files:
  - path: /var/www/html/index.html
    content: |
      <h1>Custom Image Test Successful!</h1>
      <p>Custom Image ID: ${customImageId}</p>
      <p>Installation Date: $(date)</p>

runcmd:
  - systemctl enable nginx
  - systemctl start nginx
  - echo "Custom image test completed at $(date)" >> /root/install.log
  - echo "Custom Image ID: ${customImageId}" >> /root/install.log`,
      customScript: `#!/bin/bash
echo "=== Custom Image Test Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
echo "Custom Image ID: ${customImageId}" >> /root/custom_install.log
echo "Password: ${customPassword}" >> /root/custom_install.log

# Create test file
echo "Custom Image Test - ID: ${customImageId}" > /root/test_custom_image.txt

echo "Custom image test script completed" >> /root/custom_install.log`
    };

    console.log('📤 Custom image installation data:');
    console.log('- Custom Image ID:', customImageId);
    console.log('- Password:', customPassword);
    console.log('- SSH Key:', customSSHKey.substring(0, 50) + '...');
    console.log('- Enable Root User:', true);

    const reinstallResult = await contaboProvider.reinstallVPS(instanceId, customInstallData);
    console.log('✅ Custom image installation result:', reinstallResult.message);

    console.log('\n' + '='.repeat(60));
    console.log('✅ CUSTOM IMAGE TEST COMPLETED!');
    console.log('='.repeat(60));

    console.log('\n📋 Summary:');
    console.log('1. ✅ Custom Image Created Successfully');
    console.log('   - Image ID:', customImageId);
    console.log('   - Name:', customImageData.name);
    console.log('   - URL:', customImageData.url);
    
    console.log('\n2. ✅ VPS Reinstallation with Custom Image');
    console.log('   - Password:', customPassword);
    console.log('   - SSH Key configured');
    console.log('   - Nginx web server setup');

    console.log('\n🔍 Verification Steps:');
    console.log('1. Wait 5-10 minutes for installation to complete');
    console.log('2. SSH to the VPS:', `ssh root@<VPS_IP>`);
    console.log('3. Password:', customPassword);
    console.log('4. Check logs:');
    console.log('   - cat /root/install.log');
    console.log('   - cat /root/custom_install.log');
    console.log('   - cat /root/test_custom_image.txt');
    console.log('5. Test web server: curl http://localhost');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
    process.exit(1);
  }
}

// Run the test
testCustomImageOnly();
