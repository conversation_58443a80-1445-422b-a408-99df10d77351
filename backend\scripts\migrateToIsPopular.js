const mongoose = require('mongoose');
const VpsOS = require('../models/VpsOS');
const VpsApp = require('../models/VpsApp');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev';

async function migrateToIsPopular() {
  try {
    console.log('🔄 Connexion à MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('🔄 Migration des images OS...');
    
    // Migrer les OS avec category: 'popular' vers isPopular: true et category: 'os'
    const osResult = await VpsOS.updateMany(
      { category: 'popular' },
      { 
        $set: { 
          category: 'os',
          isPopular: true 
        }
      }
    );
    
    console.log(`✅ ${osResult.modifiedCount} images OS migrées`);

    console.log('🔄 Migration des applications...');
    
    // Migrer les Apps avec category: 'popular' vers isPopular: true et category: 'apps'
    const appResult = await VpsApp.updateMany(
      { category: 'popular' },
      { 
        $set: { 
          category: 'apps',
          isPopular: true 
        }
      }
    );
    
    console.log(`✅ ${appResult.modifiedCount} applications migrées`);

    // Vérification
    console.log('\n📊 VÉRIFICATION POST-MIGRATION :');
    
    const popularOS = await VpsOS.countDocuments({ isPopular: true });
    const popularApps = await VpsApp.countDocuments({ isPopular: true });
    const oldPopularOS = await VpsOS.countDocuments({ category: 'popular' });
    const oldPopularApps = await VpsApp.countDocuments({ category: 'popular' });
    
    console.log(`Images OS populaires (isPopular: true): ${popularOS}`);
    console.log(`Applications populaires (isPopular: true): ${popularApps}`);
    console.log(`Images OS avec category: 'popular' (devrait être 0): ${oldPopularOS}`);
    console.log(`Applications avec category: 'popular' (devrait être 0): ${oldPopularApps}`);

    if (oldPopularOS === 0 && oldPopularApps === 0) {
      console.log('✅ Migration réussie ! Aucune image avec category: "popular"');
    } else {
      console.log('⚠️ Il reste des images avec category: "popular"');
    }

  } catch (error) {
    console.error('❌ Erreur lors de la migration:', error);
  } finally {
    console.log('🔌 Fermeture de la connexion MongoDB...');
    await mongoose.connection.close();
    console.log('✅ Connexion fermée');
  }
}

// Exécuter la migration
migrateToIsPopular();
