const CustomImageService = require('./services/CustomImageService');
const ContaboProvider = require('./services/providers/ContaboProvider');

async function testExistingCustomImages() {
  console.log('🧪 Testing Existing Custom Images...\n');

  try {
    const customImageService = new CustomImageService();
    const contaboProvider = new ContaboProvider();

    console.log('='.repeat(60));
    console.log('🔄 TEST: GET EXISTING CUSTOM IMAGES');
    console.log('='.repeat(60));

    // Get existing custom images
    const getResult = await customImageService.getCustomImages();
    console.log('✅ Custom images retrieved successfully');
    console.log('📋 Found custom images:', getResult.data.length);
    
    if (getResult.data.length > 0) {
      console.log('\n📋 Available Custom Images:');
      getResult.data.forEach((img, index) => {
        console.log(`${index + 1}. ${img.name || 'Unnamed'}`);
        console.log(`   - ID: ${img.imageId || img.id}`);
        console.log(`   - OS: ${img.osType || 'Unknown'}`);
        console.log(`   - Version: ${img.version || 'Unknown'}`);
        console.log(`   - Description: ${img.description || 'No description'}`);
        console.log('');
      });

      // Test reinstallation with the first custom image
      const firstCustomImage = getResult.data[0];
      const customImageId = firstCustomImage.imageId || firstCustomImage.id;
      
      if (customImageId) {
        console.log('='.repeat(60));
        console.log('🔄 TEST: REINSTALL WITH EXISTING CUSTOM IMAGE');
        console.log('='.repeat(60));

        const generatePassword = () => {
          const lowercase = 'abcdefghijklmnopqrstuvwxyz';
          const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
          const numbers = '0123456789';
          const allChars = lowercase + uppercase + numbers;
          let password = '';
          
          password += lowercase[Math.floor(Math.random() * lowercase.length)];
          password += uppercase[Math.floor(Math.random() * uppercase.length)];
          password += numbers[Math.floor(Math.random() * numbers.length)];
          
          for (let i = 3; i < 12; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
          }
          
          return password.split('').sort(() => Math.random() - 0.5).join('');
        };

        const generateSSHKey = () => {
          const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
          return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} <EMAIL>`;
        };

        const instanceId = 202718127;
        const customPassword = generatePassword();
        const customSSHKey = generateSSHKey();
        
        const customInstallData = {
          imageId: customImageId,
          password: customPassword,
          enableRootUser: true,
          sshKeys: [customSSHKey],
          userData: `#cloud-config
# Custom image installation test
packages:
  - nginx
  - htop
  - curl
  - git
  - docker.io

users:
  - name: customuser
    groups: sudo, docker
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh_authorized_keys:
      - ${customSSHKey}

# Configure SSH
ssh_pwauth: true
password: ${customPassword}
chpasswd:
  expire: false

write_files:
  - path: /var/www/html/index.html
    content: |
      <h1>Custom Image Reinstallation Successful!</h1>
      <p>Custom Image: ${firstCustomImage.name || 'Custom Image'}</p>
      <p>Image ID: ${customImageId}</p>
      <p>Installation Date: $(date)</p>
      <p>Password: ${customPassword}</p>

runcmd:
  - systemctl enable nginx
  - systemctl start nginx
  - systemctl enable docker
  - systemctl start docker
  - echo "Custom image reinstallation completed at $(date)" >> /root/install.log
  - echo "Custom Image: ${firstCustomImage.name}" >> /root/install.log
  - echo "Image ID: ${customImageId}" >> /root/install.log`,
          customScript: `#!/bin/bash
echo "=== Custom Image Reinstallation Script ===" >> /root/custom_install.log
echo "Date: $(date)" >> /root/custom_install.log
echo "Custom Image: ${firstCustomImage.name || 'Custom Image'}" >> /root/custom_install.log
echo "Image ID: ${customImageId}" >> /root/custom_install.log
echo "Password: ${customPassword}" >> /root/custom_install.log

# Create test files
echo "Custom Image Reinstallation Test" > /root/test_custom_reinstall.txt
echo "Image: ${firstCustomImage.name}" >> /root/test_custom_reinstall.txt
echo "ID: ${customImageId}" >> /root/test_custom_reinstall.txt

# Setup Docker
docker pull hello-world
docker run hello-world > /root/docker_test.log 2>&1

echo "Custom image reinstallation script completed" >> /root/custom_install.log`
        };

        console.log('📤 Custom image reinstallation data:');
        console.log('- Custom Image:', firstCustomImage.name || 'Custom Image');
        console.log('- Image ID:', customImageId);
        console.log('- Password:', customPassword);
        console.log('- SSH Key:', customSSHKey.substring(0, 50) + '...');
        console.log('- Enable Root User:', true);

        const reinstallResult = await contaboProvider.reinstallVPS(instanceId, customInstallData);
        console.log('✅ Custom image reinstallation result:', reinstallResult.message);

        console.log('\n' + '='.repeat(60));
        console.log('✅ CUSTOM IMAGE REINSTALLATION COMPLETED!');
        console.log('='.repeat(60));

        console.log('\n📋 Final Summary:');
        console.log('1. ✅ Custom Images Retrieved Successfully');
        console.log(`   - Found ${getResult.data.length} custom images`);
        
        console.log('\n2. ✅ VPS Reinstallation with Custom Image');
        console.log('   - Image:', firstCustomImage.name || 'Custom Image');
        console.log('   - ID:', customImageId);
        console.log('   - Password:', customPassword);
        console.log('   - Full web stack with Docker');

        console.log('\n🔍 Verification Steps:');
        console.log('1. Wait 5-10 minutes for installation to complete');
        console.log('2. SSH to the VPS with password:', customPassword);
        console.log('3. Check logs and test files:');
        console.log('   - cat /root/install.log');
        console.log('   - cat /root/custom_install.log');
        console.log('   - cat /root/test_custom_reinstall.txt');
        console.log('   - cat /root/docker_test.log');
        console.log('4. Test web server: curl http://localhost');
        console.log('5. Test Docker: docker ps');

      } else {
        console.log('❌ No valid custom image ID found');
      }
    } else {
      console.log('📝 No custom images found. This is normal for new accounts.');
      console.log('📝 Custom images were created in previous tests but may not be visible yet.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
}

// Run the test
testExistingCustomImages();
